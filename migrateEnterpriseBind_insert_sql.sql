-- 企业绑定迁移的插入 SQL
-- 基于 ToolsServiceImpl.migrateEnterpriseBind(String outEa) 方法

-- 1. 插入企业绑定信息到 outer_oa_enterprise_bind 表
INSERT INTO outer_oa_enterprise_bind (
    id,                    -- 数据中心ID (使用 IdGenerator.get() 生成)
    channel,               -- 渠道 (qywx)
    fs_ea,                 -- 纷享企业EA
    out_ea,                -- 外部企业EA (企业微信corpId)
    app_id,                -- 外部应用appId
    connect_info,          -- 连接参数 (JSON格式的QywxConnectorVo)
    bind_type,             -- 绑定类型 (manual/auto)
    bind_status,           -- 绑定状态 (normal/stop/create)
    create_time,           -- 创建时间
    update_time            -- 更新时间
) VALUES (
    '生成的UUID',           -- 示例: 'dc_12345678901234567890123456789012'
    'qywx',                -- 固定值: 企业微信渠道
    '纷享企业EA',            -- 从 QyweixinAccountEnterpriseMapping.fsEa 获取
    '企业微信corpId',        -- 参数 outEa
    '应用ID',               -- 从 QyweixinCorpBindBo 获取，优先wx开头，其次dk开头
    '{
        "dataCenterId": "生成的UUID",
        "connectorName": "企业微信",
        "dataCenter": "企业微信",
        "channel": "qywx",
        "alertConfig": true,
        "alertTypes": ["CRM_TODO", "CRM_NOTIFICATION"],
        "qywxEnterpriseName": "企业名称",
        "qywxCorpId": "企业微信corpId",
        "qywxDepartmentId": "部门ID",
        "domain": "域名",
        "authType": "OAUTH2",
        "appType": "isv",
        "agentId": 应用代理ID,
        "isFirstLand": true/false,
        "isRetainInformation": true/false
    }',                    -- QywxConnectorVo 的JSON序列化
    'manual',              -- 绑定类型: manual(手动) 或 auto(自动)
    'normal',              -- 绑定状态: normal(正常) 或 stop(停用) 或 create(创建中)
    ***********00,         -- 当前时间戳(毫秒)
    ***********00          -- 当前时间戳(毫秒)
)
ON CONFLICT (fs_ea, out_ea, app_id, channel) DO UPDATE SET
    connect_info = EXCLUDED.connect_info,
    bind_type = EXCLUDED.bind_type,
    bind_status = EXCLUDED.bind_status,
    update_time = EXCLUDED.update_time;

-- 2. 插入配置规则信息到 outer_oa_config_info 表 (账号同步类型)
INSERT INTO outer_oa_config_info (
    id,                    -- 配置ID
    channel,               -- 渠道
    dc_id,                 -- 数据中心ID (关联企业绑定表的id)
    fs_ea,                 -- 纷享企业EA
    out_ea,                -- 外部企业EA
    app_id,                -- 应用ID
    type,                  -- 配置类型
    config_info,           -- 配置信息JSON
    create_time,           -- 创建时间
    update_time            -- 更新时间
) VALUES (
    '生成的配置UUID',        -- 示例: 'config_12345678901234567890123456789012'
    'qywx',                -- 渠道
    '企业绑定表的ID',        -- 关联 outer_oa_enterprise_bind.id
    '纷享企业EA',
    '企业微信corpId',
    '应用ID',
    'SETTING_BIND_RULES',   -- 配置类型: 绑定规则设置
    '{
        "syncTypeEnum": "accountSync",
        "employeeLeaveRule": {
            "unbind": true,
            "stopEmp": true
        },
        "employeeRangeRemoveRule": {
            "unbind": true,
            "stopEmp": true
        }
    }',                    -- 账号同步类型的配置
    ***********00,         -- 当前时间戳(毫秒)
    ***********00          -- 当前时间戳(毫秒)
);

-- 3. 插入配置规则信息到 outer_oa_config_info 表 (账号绑定类型 - 自动绑定)
INSERT INTO outer_oa_config_info (
    id,
    channel,
    dc_id,
    fs_ea,
    out_ea,
    app_id,
    type,
    config_info,
    create_time,
    update_time
) VALUES (
    '生成的配置UUID2',
    'qywx',
    '企业绑定表的ID',
    '纷享企业EA',
    '企业微信corpId',
    '应用ID',
    'SETTING_BIND_RULES',
    '{
        "syncTypeEnum": "accountBind",
        "bindTypeEnum": "auto",
        "employeeLeaveRule": {
            "unbind": false,
            "stopEmp": false
        },
        "employeeRangeRemoveRule": {
            "unbind": false,
            "stopEmp": false
        }
    }',                    -- 自动绑定类型的配置
    ***********00,
    ***********00
);

-- 4. 插入配置规则信息到 outer_oa_config_info 表 (账号绑定类型 - 手动绑定)
INSERT INTO outer_oa_config_info (
    id,
    channel,
    dc_id,
    fs_ea,
    out_ea,
    app_id,
    type,
    config_info,
    create_time,
    update_time
) VALUES (
    '生成的配置UUID3',
    'qywx',
    '企业绑定表的ID',
    '纷享企业EA',
    '企业微信corpId',
    '应用ID',
    'SETTING_BIND_RULES',
    '{
        "syncTypeEnum": "accountBind",
        "bindTypeEnum": "manual",
        "employeeLeaveRule": {
            "unbind": false,
            "stopEmp": false
        },
        "employeeRangeRemoveRule": {
            "unbind": false,
            "stopEmp": false
        }
    }',                    -- 手动绑定类型的配置
    ***********00,
    ***********00
);

-- 实际使用示例 (基于具体数据):
-- 假设 outEa = 'ww1234567890abcdef'

-- 示例1: 插入企业绑定信息
INSERT INTO outer_oa_enterprise_bind (
    id, channel, fs_ea, out_ea, app_id, connect_info, bind_type, bind_status, create_time, update_time
) VALUES (
    'dc_1234567890123456789012345678901234567890',
    'qywx',
    'fs_test_company',
    'ww1234567890abcdef',
    'wx123456789012345678',
    '{"dataCenterId":"dc_1234567890123456789012345678901234567890","connectorName":"企业微信","dataCenter":"企业微信","channel":"qywx","alertConfig":true,"alertTypes":["CRM_TODO","CRM_NOTIFICATION"],"qywxEnterpriseName":"测试企业","qywxCorpId":"ww1234567890abcdef","qywxDepartmentId":"1","domain":"test.com","authType":"OAUTH2","appType":"isv","agentId":1000001}',
    'manual',
    'normal',
    ***********00,
    ***********00
)
ON CONFLICT (fs_ea, out_ea, app_id, channel) DO UPDATE SET
    connect_info = EXCLUDED.connect_info,
    bind_type = EXCLUDED.bind_type,
    bind_status = EXCLUDED.bind_status,
    update_time = EXCLUDED.update_time;

-- 示例2: 插入绑定规则配置
INSERT INTO outer_oa_config_info (
    id, channel, dc_id, fs_ea, out_ea, app_id, type, config_info, create_time, update_time
) VALUES (
    'config_1234567890123456789012345678901234567890',
    'qywx',
    'dc_1234567890123456789012345678901234567890',
    'fs_test_company',
    'ww1234567890abcdef',
    'wx123456789012345678',
    'SETTING_BIND_RULES',
    '{"syncTypeEnum":"accountBind","bindTypeEnum":"manual","employeeLeaveRule":{"unbind":false,"stopEmp":false},"employeeRangeRemoveRule":{"unbind":false,"stopEmp":false}}',
    ***********00,
    ***********00
);

-- 注意事项:
-- 1. 所有的UUID需要使用 IdGenerator.get() 方法生成
-- 2. 时间戳使用 System.currentTimeMillis() 获取当前毫秒时间戳
-- 3. connect_info 字段的JSON内容根据实际的 QywxConnectorVo 对象序列化
-- 4. 绑定类型和状态根据原始数据的 bindType 和 status 字段映射
-- 5. 配置类型根据原始数据的 bindType 决定使用哪种配置规则
-- 6. 使用 ON CONFLICT 处理重复数据的情况

-- 数据映射说明:
-- bindType: 0=auto(自动), 1=manual(手动)
-- status: 0=normal(正常), 1=stop(停用), 其他=create(创建中)
-- channel: 固定为 'qywx' (企业微信)
-- appId优先级: wx开头 > dk开头 > 默认CRM应用ID
