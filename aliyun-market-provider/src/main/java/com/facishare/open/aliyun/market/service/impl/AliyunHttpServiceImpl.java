package com.facishare.open.aliyun.market.service.impl;

import com.aliyun.market20151101.models.DescribeOrderRequest;
import com.aliyun.market20151101.models.DescribeOrderResponse;
import com.aliyun.market20151101.models.DescribeOrderResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.facishare.open.aliyun.market.config.ConfigCenter;
import com.facishare.open.aliyun.market.result.Result;
import com.facishare.open.aliyun.market.service.AliyunHttpService;
import org.springframework.stereotype.Service;

@Service("aliyunHttpService")
public class AliyunHttpServiceImpl implements AliyunHttpService {

    /**
     * 使用AK&SK初始化账号Client
     * @return Client
     * @throws Exception
     */
    private com.aliyun.market20151101.Client createClient() throws Exception {
        Config config = new Config()
                // 您的 AccessKey ID
                .setAccessKeyId(ConfigCenter.ACCESS_KEY_ID)
                // 您的 AccessKey Secret
                .setAccessKeySecret(ConfigCenter.ACCESS_KEY_SECRET)
                .setHttpProxy(ConfigCenter.ALIYUN_SDK_HTTP_PROXY);
        // 访问的域名
        config.endpoint = "market.aliyuncs.com";
        return new com.aliyun.market20151101.Client(config);
    }

    @Override
    public Result<DescribeOrderResponseBody> getOrderInfo(String orderId) throws Exception {
        com.aliyun.market20151101.Client client = createClient();
        DescribeOrderRequest describeOrderRequest = new DescribeOrderRequest()
                .setOrderId(orderId);
        RuntimeOptions runtime = new RuntimeOptions();
        DescribeOrderResponse resp = client.describeOrderWithOptions(describeOrderRequest, runtime);
        return Result.newSuccess(resp.body);
    }
}