package com.facishare.open.aliyun.market.service.impl;

import com.facishare.open.aliyun.market.model.jsapi.JsApiSignatureModel;
import com.facishare.open.aliyun.market.result.Result;
import com.facishare.open.aliyun.market.service.JsApiService;
import org.springframework.stereotype.Service;

@Service("jsApiService")
public class JsApiServiceImpl implements JsApiService {

    @Override
    public Result<JsApiSignatureModel> getJsApiSignature(String appId, String fsEa, String url) {
        return getJsApiSignature2(appId, fsEa, null, url);
    }

    @Override
    public Result<JsApiSignatureModel> getJsApiSignature2(String appId, String fsEa, String outEa, String url) {
        JsApiSignatureModel signatureModel = JsApiSignatureModel.builder()
                .appId(appId)
                .nonceStr("123456789")
                .timestamp(System.currentTimeMillis())
                .url(url)
                .signature("aliyun_default_signature")
                .build();
        return Result.newSuccess(signatureModel);
    }
}
