package com.facishare.open.aliyun.market.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.aliyun.market.entity.OrderInfoEntity;
import com.facishare.open.aliyun.market.mapper.OrderInfoMapper;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * tb_order_info表管理器类
 * <AUTHOR>
 * @date 20220722
 */
@Component
public class OrderInfoManager {
    @Resource
    private OrderInfoMapper orderInfoMapper;

    public OrderInfoEntity getEntity(String orderId) {
        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getOrderId,orderId);

        return orderInfoMapper.selectOne(wrapper);
    }

    /**
     * 获取最新的订单
     * @param corpId
     * @return
     */
    public OrderInfoEntity getLatestOrder(String corpId) {
        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getPaidCorpId,corpId);
        wrapper.orderByDesc(OrderInfoEntity::getEndTime);

        return orderInfoMapper.selectList(wrapper).get(0);
    }

    /**
     * 插入或更新订单信息表
     * @param entity
     * @return
     */
    public Integer insertOrUpdateOrderInfo(OrderInfoEntity entity) {
        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getPaidCorpId, entity.getPaidCorpId());
        wrapper.eq(OrderInfoEntity::getAppId, entity.getAppId());
        wrapper.eq(OrderInfoEntity::getOrderId, entity.getOrderId());
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(wrapper);
        int count = 0;
        if (orderInfoEntity != null) {
            BeanUtils.copyProperties(entity, orderInfoEntity, "id");
            count = orderInfoMapper.updateById(orderInfoEntity);
            LogUtils.info("OrderInfoManager.updateOrderInfo,updateById,count={}", count);
        } else {
            count = orderInfoMapper.insert(entity);
            LogUtils.info("OrderInfoManager.updateOrderInfo,insert,count={}", count);
        }
        return count;
    }
}
