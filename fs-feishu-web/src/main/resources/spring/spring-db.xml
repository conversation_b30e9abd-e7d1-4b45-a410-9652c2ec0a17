<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <!-- MySQL数据库配置 -->
    <bean id="openFeishuDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="fs-feishu-config"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="openFeishuDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.feishu.syncapi.entity"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
        <property name="mapperLocations" value="classpath*:mapper/*.xml"/>
        <property name="plugins">
            <array>
                <bean id="mybatisPlusInterceptor" class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
                    <property name="interceptors">
                        <list>
                            <bean class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
                                <constructor-arg name="dbType" value="MYSQL"/>
                                <!-- 设置请求的页面大于最大页后操作，true调回到首页，false继续请求  默认false -->
                                <property name="overflow" value="false"/>
                                <!-- 设置最大单页限制数量，默认500条，-1不受限制 -->
                                <property name="maxLimit" value="500"/>
                                <!-- 开启count的join优化，只针对部分left join -->
                                <property name="optimizeJoin" value="true"/>
                            </bean>
                        </list>
                    </property>
                </bean>
                <!-- 保留系统原有的拦截器配置 -->
<!--                <bean class="com.github.mybatis.interceptor.MasterSlaveInterceptor"/>-->
<!--                这里的插件会影响分页-->
<!--                <bean class="com.github.mybatis.interceptor.PaginationAutoMapInterceptor">-->
<!--                </bean>-->
            </array>
        </property>
        <property name="globalConfig" ref="globalConfig"/>
    </bean>

<!--    &lt;!&ndash; ck数据库配置 &ndash;&gt;-->
<!--    <bean id="fsClickHouseDB" class="com.github.mybatis.spring.DynamicDataSource">-->
<!--        <property name="configName" value="eye-clickhouse-db"/>-->
<!--    </bean>-->
<!--    &lt;!&ndash; define the SqlSessionFactory &ndash;&gt;-->
<!--    <bean id="clickHouseSqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">-->
<!--        <property name="dataSource" ref="fsClickHouseDB"/>-->
<!--        <property name="typeAliasesPackage" value="com.facishare.open.feishu.syncapi.ckEntity"/>-->
<!--        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>-->
<!--    </bean>-->

<!--    <bean id="mybatisPlusInterceptor" class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">-->
<!--        <property name="interceptors">-->
<!--            <list>-->
<!--                <ref bean="paginationInnerInterceptor"/>-->
<!--            </list>-->
<!--        </property>-->
<!--    </bean>-->



    <!-- 注释掉旧的countSqlParser配置，因为类路径可能已经变更 -->
    <!-- 
    <bean id="countSqlParser"
          class="com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize">
        <property name="optimizeJoin" value="true"/>
    </bean>
    -->

    <bean id="globalConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig">
        <property name="dbConfig" ref="dbConfig"/>
    </bean>

    <bean id="dbConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig.DbConfig">
        <property name="keyGenerators">
            <list>
                <bean class="com.baomidou.mybatisplus.extension.incrementer.H2KeyGenerator"/>
            </list>
        </property>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.feishu.sync.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

<!--    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">-->
<!--        <property name="basePackage" value="com.facishare.open.feishu.sync.ckMapper"/>-->
<!--        <property name="sqlSessionFactoryBeanName" value="clickHouseSqlSessionFactory"/>-->
<!--    </bean>-->
    <!--事务管理器-->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="openFeishuDB"/>
    </bean>
    <!--使用注解事务 -->
    <tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true"/>


</beans>