package com.facishare.open.feishu.web.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.config.OAMessageTag;
import com.facishare.open.feishu.web.handler.ExternalMessageHandler;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> 飞书监听crm消息事件
 * @Date 2022/10/26 16:23
 * @Version 1.0
 */
@Service("externalMessageListener")
public class ExternalMessageListener implements MessageListenerOrderly {
    @Resource
    private ExternalMessageHandler externalMessageHandler;

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
        for (MessageExt msg : list) {
            TraceUtils.initTraceId(msg.getMsgId() + "_" + msg.getQueueId());
            try {
                String receiveTags = msg.getTags();
                switch (receiveTags) {
                    case OAMessageTag.CREATE_TO_DO_TAG:
                        CreateTodoArg createTodoArg = JSONObject.parseObject(msg.getBody(), CreateTodoArg.class);
                        LogUtils.info("externalMessageListener.consumeMessage.createTodoArg={}", createTodoArg);
                        externalMessageHandler.dealCreateTodoHandler(createTodoArg);
                        break;
                    case OAMessageTag.DEAL_TO_DO_TAG:
                        DealTodoArg dealTodoArg
                                = JSONObject.parseObject(msg.getBody(), DealTodoArg.class);
                        LogUtils.info("externalMessageListener.consumeMessage.dealTodoArg={}", dealTodoArg);
                        externalMessageHandler.dealDealTodoHandler(dealTodoArg);
                        break;
                    case OAMessageTag.DELETE_TO_DO:
                        DeleteTodoArg deleteTodoArg
                                = JSONObject.parseObject(msg.getBody(), DeleteTodoArg.class);
                        LogUtils.info("externalMessageListener.consumeMessage.deleteTodoArg={}", deleteTodoArg);
                        externalMessageHandler.dealDeleteTodoHandler(deleteTodoArg);
                        break;
                    case OAMessageTag.TEXT_MSG_TAG:
                        SendTextMessageArg sendTextMessageArg
                                = JSONObject.parseObject(msg.getBody(), SendTextMessageArg.class);
                        LogUtils.info("externalMessageListener.consumeMessage.sendTextMessageArg={}", sendTextMessageArg);
                        externalMessageHandler.dealSendTextMessageHandler(sendTextMessageArg);
                        break;
                    case OAMessageTag.CARD_MSG_TAG:
                        SendTextCardMessageArg sendTextCardMessageArg
                                = JSONObject.parseObject(msg.getBody(), SendTextCardMessageArg.class);
                        LogUtils.info("externalMessageListener.consumeMessage.sendTextCardMessageArg={}", sendTextCardMessageArg);
                        externalMessageHandler.dealSendTextCardMessageHandler(sendTextCardMessageArg);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                LogUtils.error("externalMessageListener.consumeMessage consume failed.", e);
            }
        }
        return ConsumeOrderlyStatus.SUCCESS;
    }
}
