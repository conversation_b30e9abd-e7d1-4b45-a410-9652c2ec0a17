package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.oa.base.dbproxy.mongo.dao.CrmTodoRetryMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.CrmTodoRetryDoc;
import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg;
import com.facishare.open.feishu.syncapi.arg.DealTodoPushArg;
import com.facishare.open.feishu.syncapi.arg.DeleteTodoPushArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.OAMessageTag;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.feishu.syncapi.model.ExternalTodoMsgModel;
import com.facishare.open.feishu.syncapi.model.SendMessage.PostMassageModel;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTemplate;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalsTemplateResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageTemplateEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageTemplateManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageTemplateParams;
import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mongodb.bulk.BulkWriteResult;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service("externalTodoMsgService")
public class ExternalTodoMsgServiceImpl implements ExternalTodoMsgService {
    @Autowired
    private MsgService msgService;

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;
    @Resource
    private ExternalApprovalsTemplateManager externalApprovalsTemplateManager;
    @Autowired
    private OuterOaMessageTemplateManager oaMessageTemplateManager;
    @Resource
    private ExternalApprovalsService externalApprovalsService;
    @Resource
    private ExternalTodoManager externalTodoManager;
    @Resource
    private FsObjServiceProxy fsObjServiceProxy;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private CrmTodoRetryMongoDao crmTodoRetryMongoDao;
    @Resource
    private I18NStringManager i18NStringManager;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    private static final String approvalBizType = "452";
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private OuterOaMessageTemplateManager outerOaMessageTemplateManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;


    @Override
    public Result<Void> createTodo(CreateTodoPushArg createTodoPushArg) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();

        //判断人员信息
        List<String> fsUserIdList = createTodoArg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(enterpriseBindEntity.getOutEa()).fsEa(enterpriseBindEntity.getFsEa()).build();

        List<OuterOaEmployeeBindEntity> employeeBindEntityList = outerOaEmployeeBindManager.getEntitiesByEmpIds(outerOaEmployeeBindParams, null, fsUserIdList);
        if (CollectionUtils.isEmpty(employeeBindEntityList)) {
            return Result.newSuccess();
        }

        //待处理的审批流程
        if (createTodoArg.getBizType().equals(approvalBizType) && ConfigCenter.TODO_GRAY_EA.contains(createTodoArg.getEa())) {
            createExternalApprovalTodo(createTodoPushArg, employeeBindEntityList);
        } else {
            //推送到纷享机器人
            createExternalCrmBot(createTodoPushArg, employeeBindEntityList);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> dealTodo(DealTodoPushArg dealTodoPushArg) {
        String msgType = dealTodoPushArg.getMsgType();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = dealTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        DealTodoArg dealTodoArg = dealTodoPushArg.getDealTodoArg();
        String upstreamEa = dealTodoPushArg.getUpstreamEa();
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();
        //判断类型
        if(!dealTodoArg.getBizType().equals(approvalBizType)) {
            return Result.newSuccess();
        }

        //判断人员信息
        List<String> fsUserIdList = dealTodoArg.getOperators().stream().map(v -> v + "").collect(Collectors.toList());
        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(enterpriseBindEntity.getOutEa()).fsEa(enterpriseBindEntity.getFsEa()).build();

        List<OuterOaEmployeeBindEntity> employeeBindEntityList = outerOaEmployeeBindManager.getEntitiesByEmpIds(outerOaEmployeeBindParams, null, fsUserIdList);
//        if (CollectionUtils.isEmpty(employeeBindEntityList)) {
//            return Result.newSuccess();
//        }
        Gson gson = new Gson();

        if(isDealTodo(channelEnum,fsEa, enterpriseBindEntity.getOutEa(), dealTodoArg.getSourceId())) {
            //审批中心
            List<CrmTodoRetryDoc> crmTodoRetryDocs = new LinkedList<>();
            Result<Void> dealExternalApprovalsResult = null;
            try {
                dealExternalApprovalsResult = externalTodoManager.dealExternalApprovalTodo(dealTodoArg, employeeBindEntityList, enterpriseBindEntity);
            } catch (Exception e) {
                dealExternalApprovalsResult = Result.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
            }
            if (!dealExternalApprovalsResult.isSuccess()) {
                CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                crmTodoRetryDoc.setId(ObjectId.get());
                crmTodoRetryDoc.setFsEa(fsEa);
                crmTodoRetryDoc.setUpstreamEa(upstreamEa);
                crmTodoRetryDoc.setMsgType(msgType);
                crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                crmTodoRetryDoc.setSourceId(dealTodoArg.getSourceId());
                crmTodoRetryDoc.setEvent(OAMessageTag.DEAL_TO_DO_TAG);
                crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DealApproval.name());
                crmTodoRetryDoc.setStatus(0);
                crmTodoRetryDoc.setRetryTimes(0);
                crmTodoRetryDoc.setContent(gson.toJson(dealTodoArg));
                crmTodoRetryDoc.setResult(dealExternalApprovalsResult.getMsg());
                long currentTimeMillis = System.currentTimeMillis();
                crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                crmTodoRetryDocs.add(crmTodoRetryDoc);
            }
            //审批机器人
            Result<Void> dealExternalApprovalTodoTaskResult = null;
            try {
                dealExternalApprovalTodoTaskResult = externalTodoManager.dealExternalApprovalTodoTask(dealTodoArg, employeeBindEntityList, enterpriseBindEntity);
            } catch (Exception e) {
                dealExternalApprovalTodoTaskResult = Result.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
            }
            if (!dealExternalApprovalTodoTaskResult.isSuccess()) {
                CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                crmTodoRetryDoc.setId(ObjectId.get());
                crmTodoRetryDoc.setFsEa(dealTodoArg.getEa());
                crmTodoRetryDoc.setUpstreamEa(upstreamEa);
                crmTodoRetryDoc.setMsgType(msgType);
                crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                crmTodoRetryDoc.setSourceId(dealTodoArg.getSourceId());
                crmTodoRetryDoc.setEvent(OAMessageTag.DEAL_TO_DO_TAG);
                crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DealApprovalTask.name());
                crmTodoRetryDoc.setStatus(0);
                crmTodoRetryDoc.setRetryTimes(0);
                crmTodoRetryDoc.setContent(gson.toJson(dealTodoArg));
                crmTodoRetryDoc.setResult(dealExternalApprovalTodoTaskResult.getMsg());
                long currentTimeMillis = System.currentTimeMillis();
                crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                crmTodoRetryDocs.add(crmTodoRetryDoc);
            }

            if (CollectionUtils.isNotEmpty(crmTodoRetryDocs)) {
                BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocs);
                LogUtils.info("ExternalMsgServiceImpl.dealTodo,bulkWriteResult={}.", bulkWriteResult);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteTodo(DeleteTodoPushArg deleteTodoPushArg) {
        String msgType = deleteTodoPushArg.getMsgType();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = deleteTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        DeleteTodoArg deleteTodoArg = deleteTodoPushArg.getDeleteTodoArg();
        String upstreamEa = deleteTodoPushArg.getUpstreamEa();

        //判断类型
        if(!deleteTodoArg.getBizType().equals(approvalBizType)) {
            return Result.newSuccess();
        }

        //判断人员信息
        List<String> fsUserIdList = deleteTodoArg.getDeleteEmployeeIds().stream().map(v -> v + "").collect(Collectors.toList());
        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(enterpriseBindEntity.getOutEa()).fsEa(enterpriseBindEntity.getFsEa()).build();
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByEmpIds(outerOaEmployeeBindParams, null, fsUserIdList);
        if (CollectionUtils.isEmpty(employeeBindEntities)) {
            return Result.newSuccess();
        }

        Gson gson = new Gson();

        if(isDealTodo(enterpriseBindEntity.getChannel(),fsEa, enterpriseBindEntity.getOutEa(), deleteTodoArg.getSourceId())) {
            //审批中心
            List<CrmTodoRetryDoc> crmTodoRetryDocs = new LinkedList<>();
            Result<Void> deleteExternalApprovalsResult = externalTodoManager.deleteExternalApprovals(deleteTodoArg, employeeBindEntities, enterpriseBindEntity);
            if(!deleteExternalApprovalsResult.isSuccess()) {
                CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                crmTodoRetryDoc.setId(ObjectId.get());
                crmTodoRetryDoc.setFsEa(fsEa);
                crmTodoRetryDoc.setUpstreamEa(upstreamEa);
                crmTodoRetryDoc.setMsgType(msgType);
                crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                crmTodoRetryDoc.setSourceId(deleteTodoArg.getSourceId());
                crmTodoRetryDoc.setEvent(OAMessageTag.DELETE_TO_DO);
                crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DeleteApproval.name());
                crmTodoRetryDoc.setStatus(0);
                crmTodoRetryDoc.setRetryTimes(0);
                crmTodoRetryDoc.setContent(gson.toJson(deleteTodoArg));
                crmTodoRetryDoc.setResult(deleteExternalApprovalsResult.getMsg());
                long currentTimeMillis = System.currentTimeMillis();
                crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                crmTodoRetryDocs.add(crmTodoRetryDoc);
            }
            //审批机器人
            Result<Void> deleteExternalApprovalTodoTaskResult = externalTodoManager.deleteExternalApprovalTodoTask(deleteTodoArg, employeeBindEntities, enterpriseBindEntity);
            if(!deleteExternalApprovalTodoTaskResult.isSuccess()) {
                CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
                crmTodoRetryDoc.setId(ObjectId.get());
                crmTodoRetryDoc.setFsEa(fsEa);
                crmTodoRetryDoc.setUpstreamEa(upstreamEa);
                crmTodoRetryDoc.setMsgType(msgType);
                crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
                crmTodoRetryDoc.setSourceId(deleteTodoArg.getSourceId());
                crmTodoRetryDoc.setEvent(OAMessageTag.DELETE_TO_DO);
                crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.DeleteApprovalTask.name());
                crmTodoRetryDoc.setStatus(0);
                crmTodoRetryDoc.setRetryTimes(0);
                crmTodoRetryDoc.setContent(gson.toJson(deleteTodoArg));
                crmTodoRetryDoc.setResult(deleteExternalApprovalTodoTaskResult.getMsg());
                long currentTimeMillis = System.currentTimeMillis();
                crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                crmTodoRetryDoc.setCreateTime(currentTimeMillis);
                crmTodoRetryDocs.add(crmTodoRetryDoc);
            }

            if(CollectionUtils.isNotEmpty(crmTodoRetryDocs)) {
                BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocs);
                LogUtils.info("ExternalMsgServiceImpl.deleteTodo,bulkWriteResult={}.", bulkWriteResult);
            }
        }

        return Result.newSuccess();
    }

    private Result<Void> createExternalApprovalTodo(CreateTodoPushArg createTodoPushArg,
                                                    List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        LogUtils.info("ExternalMsgServiceImpl.createExternalApprovalTodo,createTodoPushArg={},employeeBindEntities={}.", createTodoPushArg, employeeBindEntities);

        String msgType = createTodoPushArg.getMsgType();
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        //审批定义
        //审批机器人是依托审批中心存在，只有审批中心推送成功，才会尝试去推送审批机器人，即使单独推送审批机器人也是可以的
        OuterOaMessageTemplateParams outerOaMessageTemplateParams = OuterOaMessageTemplateParams.builder().outEa(enterpriseBindEntity.getOutEa()).channel(ChannelEnum.feishu).build();
        OuterOaMessageTemplateEntity templateEntity = outerOaMessageTemplateManager.getEntity(outerOaMessageTemplateParams);
        if(ObjectUtils.isEmpty(templateEntity)) {
            //创建
            Result<ExternalApprovalsTemplateResult> approvalsTemplateResultResult = createExternalApprovalsTemplate(enterpriseBindEntity.getOutEa(), appId);
            LogUtils.info("ExternalTodoMsgServiceImpl.createTodo,approvalsTemplateResultResult={}", approvalsTemplateResultResult);
            if (approvalsTemplateResultResult.isSuccess()) {
                OuterOaMessageTemplateEntity outerOaMessageTemplateEntity = OuterOaMessageTemplateEntity.builder()
                        .channel(ChannelEnum.feishu)
                        .outEa(enterpriseBindEntity.getOutEa())
                        .templateId(approvalsTemplateResultResult.getData().getApprovalCode())
                        .status(OuterOaMessageTemplateStatusEnum.normal)
                        .createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis())
                        .id(IdGenerator.get())
                        .build();
                Integer count = outerOaMessageTemplateManager.upsertByChannelAndOutEa(outerOaMessageTemplateEntity);
                LogUtils.info("ExternalTodoMsgServiceImpl.createTodo,approvalsTemplateResultResult,count={}", count);
                if(count == 0) {
                    //新增模板失败
                    LogUtils.warn("ExternalTodoMsgServiceImpl.createTodo template fail:{}", fsEa);
                    return Result.newSuccess();
                }
                //创建好了就获取下
                templateEntity = outerOaMessageTemplateManager.getEntity(outerOaMessageTemplateParams);
            }
        } else {
            //查看状态是否正常
            if(templateEntity.getStatus() != OuterOaMessageTemplateStatusEnum.normal) {
                //已经停用，走crm推送的模式
                //推送到纷享机器人
                LogUtils.info("ExternalTodoMsgServiceImpl.createTodo,approvalsTemplateResult is stop");
                createExternalCrmBot(createTodoPushArg, employeeBindEntities);
                return Result.newSuccess();
            }
        }
        Gson gson = new Gson();

        //推送到审批中心
        List<CrmTodoRetryDoc> crmTodoRetryDocs = new LinkedList<>();
        Result<Void> externalApprovalTodoResult = null;
        try {
            externalApprovalTodoResult = externalTodoManager.createExternalApprovalTodo(createTodoPushArg, templateEntity, employeeBindEntities);
        } catch (Exception e) {
            externalApprovalTodoResult = Result.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
        if(!externalApprovalTodoResult.isSuccess()) {
            //没有成功，存起来重试
            CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
            crmTodoRetryDoc.setId(ObjectId.get());
            crmTodoRetryDoc.setMsgType(msgType);
            crmTodoRetryDoc.setFsEa(fsEa);
            crmTodoRetryDoc.setUpstreamEa(upstreamEa);
            crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
            crmTodoRetryDoc.setSourceId(createTodoArg.getSourceId());
            crmTodoRetryDoc.setEvent(OAMessageTag.CREATE_TO_DO_TAG);
            crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.CreateApproval.name());
            crmTodoRetryDoc.setStatus(0);
            crmTodoRetryDoc.setRetryTimes(0);
            crmTodoRetryDoc.setContent(gson.toJson(createTodoArg));
            crmTodoRetryDoc.setResult(externalApprovalTodoResult.getMsg());
            long currentTimeMillis = System.currentTimeMillis();
            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
            crmTodoRetryDoc.setCreateTime(currentTimeMillis);
            crmTodoRetryDocs.add(crmTodoRetryDoc);
        }

        //推送到审批机器人
        Result<Void> externalTodoTaskResult = null;
        try {
            externalTodoTaskResult = externalTodoManager.createExternalTodoTask(createTodoPushArg, employeeBindEntities);
        } catch (Exception e) {
            externalTodoTaskResult = Result.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
        if(!externalTodoTaskResult.isSuccess()) {
            //没有成功，存起来重试，失败后就不处理下面的逻辑了
            CrmTodoRetryDoc crmTodoRetryDoc = new CrmTodoRetryDoc();
            crmTodoRetryDoc.setId(ObjectId.get());
            crmTodoRetryDoc.setMsgType(msgType);
            crmTodoRetryDoc.setFsEa(createTodoArg.getEa());
            crmTodoRetryDoc.setUpstreamEa(upstreamEa);
            crmTodoRetryDoc.setOutEa(enterpriseBindEntity.getOutEa());
            crmTodoRetryDoc.setSourceId(createTodoArg.getSourceId());
            crmTodoRetryDoc.setEvent(OAMessageTag.CREATE_TO_DO_TAG);
            crmTodoRetryDoc.setEventType(ExternalMsgTypeEnum.CreateApprovalTask.name());
            crmTodoRetryDoc.setStatus(0);
            crmTodoRetryDoc.setRetryTimes(0);
            crmTodoRetryDoc.setContent(gson.toJson(createTodoArg));
            crmTodoRetryDoc.setResult(externalTodoTaskResult.getMsg());
            long currentTimeMillis = System.currentTimeMillis();
            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
            crmTodoRetryDoc.setCreateTime(currentTimeMillis);
            crmTodoRetryDocs.add(crmTodoRetryDoc);
        }

        if(CollectionUtils.isNotEmpty(crmTodoRetryDocs)) {
            BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocs);
            LogUtils.info("ExternalMsgServiceImpl.createTodo,bulkWriteResult={}.", bulkWriteResult);
        }
        return Result.newSuccess();
    }

    private Result<Void> createExternalCrmBot(CreateTodoPushArg createTodoPushArg,
                                              List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,createTodoPushArg={},employeeBindEntities={}.", createTodoPushArg, employeeBindEntities);

        String msgType = createTodoPushArg.getMsgType();
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();

        //拼接文本内容
        PostMassageModel postMessageModel = new PostMassageModel();
        PostMassageModel.LanguageModel languageModel = new PostMassageModel.LanguageModel();
        PostMassageModel.LanguageModel.TextMessage textMessage = new PostMassageModel.LanguageModel.TextMessage();
        PostMassageModel.LanguageModel.TextMessage.Content content = new PostMassageModel.LanguageModel.TextMessage.Content();
        content.setTag(MsgTypeEnum.text.name());
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
            for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                markdown.append(createTodoArg.getForm().get(i).getKey()).append("：").append(createTodoArg.getForm().get(i).getValue()).append("\n");
            }
        } else {
            markdown.append(createTodoArg.getContent()).append("\n");
        }
        content.setText(markdown.toString());
        textMessage.setTitle(createTodoArg.getTitle());
        PostMassageModel.LanguageModel.TextMessage.Content hrefContent = new PostMassageModel.LanguageModel.TextMessage.Content();
        hrefContent.setTag("a");
        hrefContent.setText("\n查看详情");
        //处理url,如果有自建应用，此处的appId需要改成从库取
        String todoUrl = crmBotUrlGenerator(createTodoPushArg);
        LogUtils.info("ExternalMsgServiceImpl,createExternalCrmBot,todoUrl={}",todoUrl);
        if(StringUtils.isEmpty(todoUrl)) {
            return Result.newSuccess();
        }

        byte[] todoUrlBytes = todoUrl.getBytes();
        String param = new String(Base64.encodeBase64(todoUrlBytes));
        String param2 = "";
        if(msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
            //身份透传
            ExternalTodoMsgModel externalTodoMsgModel = new ExternalTodoMsgModel();
            externalTodoMsgModel.setFsEa(fsEa);
            externalTodoMsgModel.setOutEa(outEa);
            externalTodoMsgModel.setTaskId(createTodoArg.getExtraDataMap().get("taskId"));
            externalTodoMsgModel.setMsgType(msgType);
            externalTodoMsgModel.setUpstreamEa(upstreamEa);
            externalTodoMsgModel.setFsAppId(createTodoArg.getAppId());
            param2 = new String(Base64.encodeBase64(new Gson().toJson(externalTodoMsgModel).getBytes()));
        }

        String authUrl = ConfigCenter.FEISHU_AUTHEN_URL
                .replace("{app_id}", appId)
                .replace("{state}", appId)
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&param2=" + param2 + "&fsEa=" + fsEa));
        String url = ConfigCenter.FEISHU_WEB_URL
                .replace("{mode}", "window")
                .replace("{url}", URLEncoder.encode(authUrl));
        hrefContent.setHref(url);
        List<PostMassageModel.LanguageModel.TextMessage.Content> contentList = new LinkedList<>();
        contentList.add(content);
        contentList.add(hrefContent);
        List<List<PostMassageModel.LanguageModel.TextMessage.Content>> messageContentList = new LinkedList<>();
        messageContentList.add(contentList);
        textMessage.setContent(messageContentList);
        languageModel.setZh_cn(textMessage);
        postMessageModel.setPost(languageModel);
        LogUtils.info("ExternalTodoMsgServiceImpl.createExternalCrmBot,ea={},postMessageModel={}.", fsEa, postMessageModel);
        List<String> receiverIds = employeeBindEntities.stream().map(OuterOaEmployeeBindEntity::getOutEmpId).collect(Collectors.toList());
        Result<Void> msgResult = msgService.batchSend(appId, enterpriseBindEntity.getOutEa(), MsgTypeEnum.post, receiverIds, postMessageModel);
        LogUtils.info("ExternalTodoMsgServiceImpl.createExternalCrmBot,msgResult={}.", msgResult);
        return Result.newSuccess();
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalTodoMsgServiceImpl.GetEnterpriseDataArg,result={}",result);
        return result;
    }

    private Result<ExternalApprovalsTemplateResult> createExternalApprovalsTemplate(String outEa, String appId) {
        LogUtils.info("ExternalTodoMsgServiceImpl.createExternalApprovalsTemplate,outEa={},appId={}", outEa, appId);
        ExternalApprovalsTemplate template = new ExternalApprovalsTemplate();
        template.setApprovalName("@i18n@1");
        template.setApprovalCode("fxiaoke_crm_approval_code");
        template.setGroupName("@i18n@2");
        template.setGroupCode("fxiaoke_crm_group_code");
        ExternalApprovalsTemplate.External external = new ExternalApprovalsTemplate.External();
        external.setBizName("@i18n@3");
        external.setSupportPc(Boolean.TRUE);
        external.setSupportMobile(Boolean.TRUE);
        external.setSupportBatchRead(Boolean.FALSE);
        template.setExternal(external);
        ExternalApprovalsTemplate.I18nResource zhResource = new ExternalApprovalsTemplate.I18nResource();
        zhResource.setLocale(ApprovalLocaleEnum.ZH_CN.getCode());
        zhResource.setIsDefault(Boolean.FALSE);
        ExternalApprovalsTemplate.I18nResource.Text zhText1 = new ExternalApprovalsTemplate.I18nResource.Text();
        zhText1.setKey("@i18n@1");
        zhText1.setValue("纷享销客CRM-审批"); //ignorei18n
        ExternalApprovalsTemplate.I18nResource.Text zhText2 = new ExternalApprovalsTemplate.I18nResource.Text();
        zhText2.setKey("@i18n@2");
        zhText2.setValue("纷享销客CRM"); //ignorei18n
        ExternalApprovalsTemplate.I18nResource.Text zhText3 = new ExternalApprovalsTemplate.I18nResource.Text();
        zhText3.setKey("@i18n@3");
        zhText3.setValue("纷享销客CRM"); //ignorei18n
        zhResource.setTexts(Lists.newArrayList(zhText1, zhText2, zhText3));

        ExternalApprovalsTemplate.I18nResource enResource = new ExternalApprovalsTemplate.I18nResource();
        enResource.setLocale(ApprovalLocaleEnum.EN_US.getCode());
        enResource.setIsDefault(Boolean.TRUE);
        ExternalApprovalsTemplate.I18nResource.Text enText1 = new ExternalApprovalsTemplate.I18nResource.Text();
        enText1.setKey("@i18n@1");
        enText1.setValue("ShareCRM-approval");
        ExternalApprovalsTemplate.I18nResource.Text enText2 = new ExternalApprovalsTemplate.I18nResource.Text();
        enText2.setKey("@i18n@2");
        enText2.setValue("ShareCRM");
        ExternalApprovalsTemplate.I18nResource.Text enText3 = new ExternalApprovalsTemplate.I18nResource.Text();
        enText3.setKey("@i18n@3");
        enText3.setValue("ShareCRM");
        enResource.setTexts(Lists.newArrayList(enText1, enText2, enText3));

        ExternalApprovalsTemplate.I18nResource jaResource = new ExternalApprovalsTemplate.I18nResource();
        jaResource.setLocale(ApprovalLocaleEnum.JA_JP.getCode());
        jaResource.setIsDefault(Boolean.FALSE);
        ExternalApprovalsTemplate.I18nResource.Text jaText1 = new ExternalApprovalsTemplate.I18nResource.Text();
        jaText1.setKey("@i18n@1");
        jaText1.setValue("ShareCRM-approval");
        ExternalApprovalsTemplate.I18nResource.Text jaText2 = new ExternalApprovalsTemplate.I18nResource.Text();
        jaText2.setKey("@i18n@2");
        jaText2.setValue("ShareCRM");
        ExternalApprovalsTemplate.I18nResource.Text jaText3 = new ExternalApprovalsTemplate.I18nResource.Text();
        jaText3.setKey("@i18n@3");
        jaText3.setValue("ShareCRM");
        jaResource.setTexts(Lists.newArrayList(jaText1, jaText2, jaText3));


        template.setI18nResources(Lists.newArrayList(zhResource, enResource, jaResource));
        ExternalApprovalsTemplate.Viewer viewer = new ExternalApprovalsTemplate.Viewer();
        viewer.setViewerType(ApprovalViewerTypeEnum.NONE.name());
        template.setViewers(Lists.newArrayList(viewer));

        return externalApprovalsService.createExternalApprovalsTemplate(outEa, appId, UserIdTypeEnum.open_id, DepartmentIdTypeEnum.open_department_id, template);
    }

    @Override
    public Result<Void> dealCrmTodo(ExternalDealTodoEvent externalDealTodoEvent) {
        String msgId = externalDealTodoEvent.getMessageId();

        String fsEa;
        Integer dealTodoFsEi;
        String tenantId;
        String userId;
        String taskId;
        String lang = "zh-CN";
        //根据message_id查找是哪个审批的
        OuterOaMessageBindParams outerOaMessageBindParams=OuterOaMessageBindParams.builder().outEa(null)
                .fsEa(null).taskId(msgId).build();
        OuterOaMessageBindEntity messageBindEntity = outerOaMessageBindManager.getEntity(outerOaMessageBindParams);
        if(ObjectUtils.isEmpty(messageBindEntity)) {
            LogUtils.info("ExternalTodoMsgServiceImpl.dealCrmTodo,externalDealTodoEvent={}",externalDealTodoEvent);
            //有问题处理失败
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }
        //根据dcId找到对应企业
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntityById(messageBindEntity.getDcId());
        if(ObjectUtils.isEmpty(enterpriseBindEntity)){
            LogUtils.info("ExternalTodoMsgServiceImpl.dealCrmTodo,enterpriseBindEntity={}",externalDealTodoEvent);
            //有问题处理失败
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();
        //根据上下文找到数据
        if (StringUtils.isNotEmpty(externalDealTodoEvent.getActionContext())) {
            ExternalTodoMsgModel approvalsTaskActionContext = new Gson().fromJson(externalDealTodoEvent.getActionContext(), ExternalTodoMsgModel.class);
            fsEa = approvalsTaskActionContext.getFsEa();
            String outEa = approvalsTaskActionContext.getOutEa();
//            ExternalTodoTaskEntity externalTodoTaskEntity = externalTodoTaskEntities.get(0);
//            if(externalTodoTaskEntity.getStatus() != 0) {
//                return Result.newSuccess();
//            }
            if(messageBindEntity.getStatus()!= OuterOaMessageBindStatusEnum.pending){
                return Result.newSuccess();
            }

            String outUserId = messageBindEntity.getOutUserId();

            if (approvalsTaskActionContext.getMsgType().equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
                dealTodoFsEi = eieaConverter.enterpriseAccountToId(fsEa);
                tenantId = String.valueOf(dealTodoFsEi);
                lang = i18NStringManager.getDefaultLang(tenantId);
                userId = approvalsTaskActionContext.getFsUserId();
                OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(outEa).fsEa(fsEa)
                        .channel(ChannelEnum.feishu)
                        .outEmpId(outUserId)
                        .fsEmpId(userId).build();


                //找出人员映射
                List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);

                if(ObjectUtils.isEmpty(entities)) {
                    return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO.getCode(), i18NStringManager.get(I18NStringEnum.s21, lang, tenantId));
                }

                //通过人员对象获取
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> empDetailResult = fsEmployeeServiceProxy.detail(tenantId, userId);
                if(!empDetailResult.isSuccess() && ObjectUtils.isEmpty(empDetailResult.getData())) {
                    return Result.newError(ResultCodeEnum.CRM_USER_QUERY_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s130, lang, tenantId));
                }

                lang = ObjectUtils.isNotEmpty(empDetailResult.getData().get("language")) ? empDetailResult.getData().get("language").toString() : lang;
                taskId = approvalsTaskActionContext.getTaskId();
            } else {
                String upstreamEa = approvalsTaskActionContext.getUpstreamEa();
                int upstreamEi = eieaConverter.enterpriseAccountToId(upstreamEa);
                dealTodoFsEi = upstreamEi;
                taskId = approvalsTaskActionContext.getTaskId();
                //企业语言选择上游的企业语言
                tenantId = String.valueOf(upstreamEi);
                lang = i18NStringManager.getDefaultLang(tenantId);
                String fsUserId = approvalsTaskActionContext.getFsUserId();

                //找出人员映射
                OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(outEa).fsEa(fsEa)
                        .channel(channelEnum)
                        .outEmpId(outUserId)
                        .fsEmpId(fsUserId).build();

                List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);


                if(ObjectUtils.isEmpty(entities)) {
                    return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO.getCode(), i18NStringManager.get(I18NStringEnum.s21, lang, tenantId));
                }

                //查询互联企业对象
                String entParam = "{\"serializeEmpty\":false,\"extractExtendInfo\":true,\"object_describe_api_name\":\"EnterpriseRelationObj\",\"search_template_id\":\"{search_template_id}\",\"include_describe\":false,\"include_layout\":false,\"need_tag\":true,\"search_template_type\":\"default\",\"ignore_scene_record_type\":true,\"search_query_info\":\"{\\\"limit\\\":20,\\\"offset\\\":0,\\\"filters\\\":[{\\\"field_name\\\":\\\"enterprise_account\\\",\\\"field_values\\\":[\\\"{enterprise_account}\\\"],\\\"operator\\\":\\\"EQ\\\"},{\\\"field_name\\\":\\\"tenant_id\\\",\\\"field_values\\\":[\\\"{tenant_id}\\\"],\\\"operator\\\":\\\"EQ\\\"}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100]}";
                entParam = entParam.replace("{search_template_id}", ConfigCenter.INTERCONNECT_ENTERPRISE_TEMPLATE_ID).replace("{enterprise_account}", fsEa).replace("{tenant_id}", upstreamEi + "");
                com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> queryEnterpriseInterConnectResult = fsObjServiceProxy.queryEnterpriseInterconnect(upstreamEi, entParam);
                if(!queryEnterpriseInterConnectResult.isSuccess() || CollectionUtils.isEmpty(queryEnterpriseInterConnectResult.getData())) {
                    return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s62, lang, tenantId));
                }
                String outTenantId = String.valueOf(queryEnterpriseInterConnectResult.getData().get(0).get("_id"));
                //获取互联身份
                String empParam = "{\"serializeEmpty\":false,\"extractExtendInfo\":true,\"object_describe_api_name\":\"PublicEmployeeObj\",\"search_template_id\":\"{search_template_id}\",\"include_describe\":false,\"include_layout\":false,\"need_tag\":true,\"search_template_type\":\"default\",\"ignore_scene_record_type\":true,\"search_query_info\":\"{\\\"limit\\\":20,\\\"offset\\\":0,\\\"filters\\\":[{\\\"field_name\\\":\\\"outer_tenant_id\\\",\\\"field_values\\\":[\\\"{outer_tenant_id}\\\"],\\\"operator\\\":\\\"EQ\\\"},{\\\"field_name\\\":\\\"employee_id\\\",\\\"field_values\\\":[\\\"{employee_id}\\\"],\\\"operator\\\":\\\"EQ\\\"}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100]}";
                empParam = empParam.replace("{search_template_id}", ConfigCenter.INTERCONNECT_EMPLOYEE_TEMPLATE_ID).replace("{outer_tenant_id}", outTenantId).replace("{employee_id}", fsUserId);
                com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> queryEmployeeInterConnectResult = fsObjServiceProxy.queryEmployeeInterconnect(upstreamEi, empParam);
                if(!queryEmployeeInterConnectResult.isSuccess() || CollectionUtils.isEmpty(queryEmployeeInterConnectResult.getData())) {
                    return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s62, lang, tenantId));
                }
                userId = String.valueOf(queryEmployeeInterConnectResult.getData().get(0).get("_id"));
                lang = String.valueOf(queryEmployeeInterConnectResult.getData().get(0).get("er_language"));
            }
        } else {
            if(messageBindEntity.getStatus()!= OuterOaMessageBindStatusEnum.approved){
                return Result.newSuccess();
            }

            String sourceId = messageBindEntity.getSourceId();
            fsEa = messageBindEntity.getFsEa();
            dealTodoFsEi = eieaConverter.enterpriseAccountToId(fsEa);
            tenantId = String.valueOf(dealTodoFsEi);
            String outEa = messageBindEntity.getOutEa();
            String outUserId = messageBindEntity.getOutUserId();
            lang = i18NStringManager.getDefaultLang(tenantId);

            //找出人员映射
            OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                    .fsEa(fsEa)
                    .channel(channelEnum)
                    .outEa(outEa)
                    .outEmpId(outUserId)
                    .build();
            List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);
            if(ObjectUtils.isEmpty(entities)) {
                return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO.getCode(), i18NStringManager.get(I18NStringEnum.s21, lang, tenantId));
            }
            userId = entities.get(0).getFsEmpId();

            //通过人员对象获取
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> empDetailResult = fsEmployeeServiceProxy.detail(tenantId, userId);
            if(!empDetailResult.isSuccess() && ObjectUtils.isEmpty(empDetailResult.getData())) {
                return Result.newError(ResultCodeEnum.CRM_USER_QUERY_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s130, lang, tenantId));
            }

            lang = ObjectUtils.isNotEmpty(empDetailResult.getData().get("language")) ? empDetailResult.getData().get("language").toString() : lang;
            taskId = messageBindEntity.getTaskId();
        }

        DealCrmTodoArg arg = new DealCrmTodoArg();
        arg.setTaskId(taskId);
        String actionType = externalDealTodoEvent.getActionType().equals("APPROVE") ? CrmDealTodoStatusEnum.agree.name() : CrmDealTodoStatusEnum.reject.name();
        arg.setActionType(actionType);
        String opinion = StringUtils.isEmpty(externalDealTodoEvent.getReason())
                ? (externalDealTodoEvent.getActionType().equals("APPROVE")
                ? i18NStringManager.get(I18NStringEnum.s93, lang, tenantId) : i18NStringManager.get(I18NStringEnum.s94, lang, tenantId)) : externalDealTodoEvent.getReason();
        arg.setOpinion(opinion);
        arg.setBlockWithLayout(Boolean.TRUE);

        com.facishare.open.order.contacts.proxy.api.result.ResultV2<Boolean> dealCrmTodoResult = fsObjServiceProxy.dealCrmTodo(dealTodoFsEi, userId, arg);

        if(!dealCrmTodoResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.CRM_DEAL_TODO_ERROR.getCode(), i18NStringManager.get(I18NStringEnum.s131, lang, tenantId));
        } else {
            return Result.newSuccess();
        }
    }

    @Override
    public Result<Void> retryCrmTodo(OuterOaEnterpriseBindEntity entity) {
        TraceUtils.initTraceId("retry_todo_" + entity.getFsEa() + "_" + entity.getOutEa());

        String fsEa = entity.getFsEa();
        String outEa = entity.getOutEa();

        //看看是否有失败的待办
        List<CrmTodoRetryDoc> crmTodoRetryDocs = crmTodoRetryMongoDao.queryCrmTodoInfos(fsEa, outEa, null, 0);
        if(CollectionUtils.isEmpty(crmTodoRetryDocs)) {
            return Result.newSuccess();
        }

        //查询模板
        OuterOaMessageTemplateEntity templateEntity = outerOaMessageTemplateManager.getEntity(OuterOaMessageTemplateParams.builder().channel(ChannelEnum.feishu).outEa(outEa).build());
        if(ObjectUtils.isEmpty(templateEntity)) {
            return Result.newSuccess();
        }

        // 将 List<CrmTodoRetryDoc> 转换成 Map<String, List<CrmTodoRetryDoc>>，并按 createTime 排序
        Map<String, List<CrmTodoRetryDoc>> resultMap = crmTodoRetryDocs.stream()
                .collect(Collectors.groupingBy(
                        CrmTodoRetryDoc::getSourceId, // 以 sourceId 作为键
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(CrmTodoRetryDoc::getCreateTime)) // 根据 createTime 排序
                                        .collect(Collectors.toList())
                        )
                ));

        //逐个重试
        Gson gson = new Gson();
        Set<String> retryTodoSet = resultMap.keySet();//将key作为元素转存入一个set集合
        for (String sourceId : retryTodoSet) {
            LogUtils.info("ExternalTodoMsgServiceImpl.retryCrmTodo,sourceId={}",sourceId);
            List<CrmTodoRetryDoc> crmTodoRetryDocLinkedList = new LinkedList<>();

            for(CrmTodoRetryDoc crmTodoRetryDoc : resultMap.get(sourceId)) {
                int retryTimes = crmTodoRetryDoc.getRetryTimes() + 1;
                if(crmTodoRetryDoc.getEvent().equals(OAMessageTag.CREATE_TO_DO_TAG)) {
                    CreateTodoArg arg = gson.fromJson(crmTodoRetryDoc.getContent(), CreateTodoArg.class);
                    List<String> fsUserIdList = arg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
                    OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(outEa).fsEa(arg.getEa()).build();

                    List<OuterOaEmployeeBindEntity> employeeBindEntityList = outerOaEmployeeBindManager.getEntitiesByEmpIds(outerOaEmployeeBindParams, null, fsUserIdList);

                    CreateTodoPushArg createTodoPushArg = new CreateTodoPushArg();
                    createTodoPushArg.setMsgType(crmTodoRetryDoc.getMsgType());
                    createTodoPushArg.setUpstreamEa(crmTodoRetryDoc.getUpstreamEa());
                    createTodoPushArg.setEnterpriseBindEntity(entity);
                    createTodoPushArg.setCreateTodoArg(arg);


                    if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.CreateApproval.name())) {
                        //审批
                        Result<Void> externalApprovalTodoResult = externalTodoManager.createExternalApprovalTodo(createTodoPushArg,
                                templateEntity,
                                employeeBindEntityList);
                        if(!externalApprovalTodoResult.isSuccess()) {
                            //没有成功，存起来重试
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(externalApprovalTodoResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    } else if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.CreateApprovalTask.name())) {
                        //审批机器人
                        Result<Void> externalTodoTaskResult = externalTodoManager.createExternalTodoTask(createTodoPushArg, employeeBindEntityList);
                        if(!externalTodoTaskResult.isSuccess()) {
                            //没有成功，存起来重试
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(externalTodoTaskResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    }
                } else if(crmTodoRetryDoc.getEvent().equals(OAMessageTag.DEAL_TO_DO_TAG)) {
                    DealTodoArg arg = gson.fromJson(crmTodoRetryDoc.getContent(), DealTodoArg.class);
                    List<String> fsUserIdList = arg.getOperators().stream().map(v -> v + "").collect(Collectors.toList());
                    OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(outEa).fsEa(arg.getEa()).build();
                    List<OuterOaEmployeeBindEntity> employeeBindEntityList = outerOaEmployeeBindManager.getEntitiesByEmpIds(outerOaEmployeeBindParams, null, fsUserIdList);

                    if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DealApproval.name())) {
                        //审批
                        Result<Void> dealExternalApprovalsResult = externalTodoManager.dealExternalApprovalTodo(arg, employeeBindEntityList, entity);
                        if(!dealExternalApprovalsResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(dealExternalApprovalsResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    } else if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DealApprovalTask.name())) {
                        //审批机器人
                        Result<Void> dealExternalApprovalTodoTaskResult = externalTodoManager.dealExternalApprovalTodoTask(arg, employeeBindEntityList, entity);
                        if(!dealExternalApprovalTodoTaskResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(dealExternalApprovalTodoTaskResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    }
                } else if(crmTodoRetryDoc.getEvent().equals(OAMessageTag.DELETE_TO_DO)) {
                    DeleteTodoArg arg = gson.fromJson(crmTodoRetryDoc.getContent(), DeleteTodoArg.class);
                    List<String> fsUserIdList = arg.getDeleteEmployeeIds().stream().map(v -> v + "").collect(Collectors.toList());
                    OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa(outEa).fsEa(arg.getEa()).build();

                    List<OuterOaEmployeeBindEntity> employeeBindEntityList = outerOaEmployeeBindManager.getEntitiesByEmpIds(outerOaEmployeeBindParams, null, fsUserIdList);
                    if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DeleteApproval.name())) {
                        //审批
                        Result<Void> deleteExternalApprovalsResult = externalTodoManager.deleteExternalApprovals(arg, employeeBindEntityList, entity);
                        if(!deleteExternalApprovalsResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(deleteExternalApprovalsResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    } else if(crmTodoRetryDoc.getEventType().equals(ExternalMsgTypeEnum.DeleteApprovalTask.name())) {
                        //审批机器人
                        Result<Void> deleteExternalApprovalTodoTaskResult = externalTodoManager.deleteExternalApprovalTodoTask(arg, employeeBindEntityList, entity);
                        if(!deleteExternalApprovalTodoTaskResult.isSuccess()) {
                            if(retryTimes == 3) {
                                crmTodoRetryDoc.setStatus(2);
                            }
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(deleteExternalApprovalTodoTaskResult.getMsg());
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);
                        } else {
                            crmTodoRetryDoc.setStatus(1);
                            crmTodoRetryDoc.setRetryTimes(retryTimes);
                            crmTodoRetryDoc.setResult(null);
                            long currentTimeMillis = System.currentTimeMillis();
                            crmTodoRetryDoc.setUpdateTime(currentTimeMillis);
                            crmTodoRetryDocLinkedList.add(crmTodoRetryDoc);

                        }
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(crmTodoRetryDocLinkedList)) {
                BulkWriteResult bulkWriteResult = crmTodoRetryMongoDao.batchReplace(crmTodoRetryDocLinkedList);
                LogUtils.info("ExternalMsgServiceImpl.deleteTodo,bulkWriteResult={}.", bulkWriteResult);
            }
        }
        return Result.newSuccess();
    }

    private Boolean isDealTodo(ChannelEnum channelEnum,String fsEa, String outEa, String sourceId) {
        OuterOaMessageTemplateParams outerOaMessageTemplateParams = OuterOaMessageTemplateParams.builder().
                channel(channelEnum).outEa(outEa).build();
        OuterOaMessageTemplateEntity templateEntity = oaMessageTemplateManager.getEntity(outerOaMessageTemplateParams);
        LogUtils.info("ExternalTodoMsgServiceImpl.isDealTodo,templateEntity={}", templateEntity);
        //取消灰度或者不想推送到审批中心去的时候，遗留待办需要处理下
        boolean isDeal = Boolean.FALSE;
        //不等于空才是创建过审批的
        if(ObjectUtils.isNotEmpty(templateEntity)) {
            if(ConfigCenter.TODO_GRAY_EA.contains(fsEa) && templateEntity.getStatus() == OuterOaMessageTemplateStatusEnum.normal) {
                isDeal = Boolean.TRUE;
            } else {
                //需要查看是否还存有审批，审批机器人的不需要处理，只查看审批中心的审批，因为这个会在未办中，处理审批中心的时候审批机器人也会处理
                OuterOaMessageBindParams outerOaMessageBindParams = OuterOaMessageBindParams.builder().
                        channel(channelEnum).outEa(outEa).messageType(OuterOaMessageBindMsgTypeEnum.todo).sourceId(sourceId).build();
                List<OuterOaMessageBindEntity> entities = outerOaMessageBindManager.getEntities(outerOaMessageBindParams);
                if(ObjectUtils.isNotEmpty(entities)) {
                    isDeal = Boolean.TRUE;
                }
            }
        }
        LogUtils.info("ExternalTodoMsgServiceImpl.isDealTodo,sourceId={},isDeal={}", sourceId, isDeal);
        return isDeal;
    }

    private String crmBotUrlGenerator(CreateTodoPushArg createTodoPushArg) {
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        String msgType = createTodoPushArg.getMsgType();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        String todoUrl = null;
        if(msgType.equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
            if(createTodoArg.getGenerateUrlType() == BaseExternalArg.BPM_TASK_URL) {
                if(ObjectUtils.isEmpty(createTodoArg.getExtraDataMap())
                        || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                        || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("activityId"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,createTodoArg={}.", createTodoArg);
                    return null;
                }
                //bpm
//            todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_BPM_URL
//                    .replace("{workflowInstanceId}", arg.getExtraDataMap().get("workflowInstanceId"))
//                    .replace("{activityId}", arg.getExtraDataMap().get("activityId"))
//                    .replace("{apiname}", arg.getExtraDataMap().get("objectApiName"))
//                    .replace("{id}", arg.getExtraDataMap().get("objectId"));
                //bpm业务流，默认先跳转到对象详情页面，等BPM团队做完业务流详情页面，再改成跳转到业务流详情页面
                todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_TODO_URL
                        .replace("{apiname}", createTodoArg.getExtraDataMap().get("objectApiName"))
                        .replace("{id}", createTodoArg.getExtraDataMap().get("objectId"))
                        .replace("{ea}", createTodoArg.getEa());
            } else {
                if(StringUtils.isNotEmpty(createTodoArg.getUrl())) {
                    todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + createTodoArg.getUrl();
                } else if(ObjectUtils.isNotEmpty(createTodoArg.getExtraDataMap())
                        && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))
                        && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("objectId"))
                        && ConfigCenter.FEISHU_IS_ADD_TODO) {
                    todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_TODO_URL.replace("{apiname}", createTodoArg.getExtraDataMap().get("objectApiName")).replace("{id}", createTodoArg.getExtraDataMap().get("objectId")).replace("{ea}", createTodoArg.getEa());
                } else {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,createTodoArg={}.", createTodoArg);
                    return null;
                }
            }
        } else {
            //互联待办构造，跟本企业的构造不同
            //暂时不区分业务流还是审批流，目前都是走对象详情页，只有一个地址，但是还是区分，方便后面给单独类型更改地址
            if(createTodoArg.getGenerateUrlType() == BaseExternalArg.BPM_TASK_URL) {
                if(ObjectUtils.isEmpty(createTodoArg.getExtraDataMap())
                        || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))
                        || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectId"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,createTodoArg={}.", createTodoArg);
                    return null;
                }
                todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_INTERCONNECT_TODO_URL
                        .replace("{fsAppId}", createTodoArg.getAppId())
                        .replace("{upstreamEa}", upstreamEa)
                        .replace("{apiName}", createTodoArg.getExtraDataMap().get("objectApiName"))
                        .replace("{dataId}", createTodoArg.getExtraDataMap().get("objectId")).replace("{webhash}", URLEncoder.encode(
                                ConfigCenter.CRM_WEB_INTERCONNECT_TODO_URL
                                        .replace("{apiname}", createTodoArg.getExtraDataMap().get("objectApiName"))
                                        .replace("{dataId}", createTodoArg.getExtraDataMap().get("objectId"))
                                        .replace("{fsOutAppId}", createTodoArg.getAppId())
                                        .replace("{appId}", createTodoArg.getAppId())));
            } else if(createTodoArg.getGenerateUrlType() == BaseExternalArg.CRM_DETAIL_URL) {
                if(ObjectUtils.isEmpty(createTodoArg.getExtraDataMap())
                        || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))
                        || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectId"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,createTodoArg={}.", createTodoArg);
                    return null;
                }
                todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_INTERCONNECT_TODO_URL
                        .replace("{fsAppId}", createTodoArg.getAppId())
                        .replace("{upstreamEa}", upstreamEa)
                        .replace("{apiName}", createTodoArg.getExtraDataMap().get("objectApiName"))
                        .replace("{dataId}", createTodoArg.getExtraDataMap().get("objectId"))
                        .replace("{webhash}", URLEncoder.encode(
                                ConfigCenter.CRM_WEB_INTERCONNECT_TODO_URL
                                        .replace("{apiname}", createTodoArg.getExtraDataMap().get("objectApiName"))
                                        .replace("{dataId}", createTodoArg.getExtraDataMap().get("objectId"))
                                        .replace("{fsOutAppId}", createTodoArg.getAppId())
                                        .replace("{appId}", createTodoArg.getAppId())));
            } else {
                //不支持的类型
                LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,type is null,createTodoArg={}.", createTodoArg);
                return null;
            }
        }
        return todoUrl;
    }
}
