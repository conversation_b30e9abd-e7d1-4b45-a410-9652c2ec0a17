package com.facishare.open.feishu.sync.mapper;

import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import com.facishare.open.oa.base.dbproxy.pg.mapper.BaseMapper2;

import java.util.List;
import java.util.Map;

@Repository
public interface SuperAdminDao extends ICrudMapper<AppInfoMapper> {
    @Select("${sqlStr}")
    List<Map<String,Object>> superQuerySql(@Param("sqlStr") String sqlStr);

    @Insert("${sqlStr}")
    Integer superInsertSql(@Param("sqlStr") String sqlStr);

    @Update("${sqlStr}")
    Integer superUpdateSql(@Param("sqlStr") String sqlStr);

    @Delete("${sqlStr}")
    Integer superDeleteSql(@Param("sqlStr") String sqlStr);
}