package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.mapper.ExternalTodoTaskMapper;
import com.facishare.open.feishu.syncapi.entity.ExternalTodoTaskEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageBindManager;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ExternalTodoTaskManager {
    @Resource
    private ExternalTodoTaskMapper externalTodoTaskMapper;

    public Integer insert(ExternalTodoTaskEntity entity) {
        int count = externalTodoTaskMapper.insert(entity);
        LogUtils.info("ExternalTodoTaskManager.insert,count={}", count);
        return count;
    }

    public Integer update(ExternalTodoTaskEntity entity) {
        int count = externalTodoTaskMapper.updateById(entity);
        LogUtils.info("ExternalTodoTaskManager.update,count={}", count);
        return count;
    }

    public List<ExternalTodoTaskEntity> queryEntities(String fsEa, String outEa, String sourceId, String taskId,
            Integer status) {
        LambdaQueryWrapper<ExternalTodoTaskEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalTodoTaskEntity::getFsEa, fsEa);
        if (StringUtils.isNotEmpty(sourceId)) {
            wrapper.eq(ExternalTodoTaskEntity::getOutEa, outEa);
        }
        if (StringUtils.isNotEmpty(sourceId)) {
            wrapper.eq(ExternalTodoTaskEntity::getSourceId, sourceId);
        }
        if (StringUtils.isNotEmpty(taskId)) {
            wrapper.eq(ExternalTodoTaskEntity::getTaskId, taskId);
        }
        if (status != null) {
            wrapper.eq(ExternalTodoTaskEntity::getStatus, status);
        }
        return externalTodoTaskMapper.selectList(wrapper);
    }

    public ExternalTodoTaskEntity queryEntityByTaskId(String taskId, Integer status) {
        LambdaQueryWrapper<ExternalTodoTaskEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalTodoTaskEntity::getTaskId, taskId);
        if (status != null) {
            wrapper.eq(ExternalTodoTaskEntity::getStatus, status);
        }
        return externalTodoTaskMapper.selectOne(wrapper);
    }

    /**
     * 分页查询待办任务
     *
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     * @param fsEa     飞书企业账号

     * @return 分页结果
     */
    public Page<ExternalTodoTaskEntity> queryEntitiesPage(Integer pageNum, Integer pageSize, String fsEa) {
        // 创建分页对象
        Page<ExternalTodoTaskEntity> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<ExternalTodoTaskEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalTodoTaskEntity::getFsEa, fsEa);

        // 执行分页查询
        LogUtils.info("ExternalTodoTaskManager.queryEntitiesPage,pageNum={},pageSize={}", pageNum, pageSize);
        return externalTodoTaskMapper.selectPage(page, wrapper);
    }
}
