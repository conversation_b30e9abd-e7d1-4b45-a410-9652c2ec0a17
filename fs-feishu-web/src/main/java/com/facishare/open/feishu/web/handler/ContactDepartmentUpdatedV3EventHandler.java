package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactDepartmentUpdateV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 修改部门处理器
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class ContactDepartmentUpdatedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    private String FEISHU_DEPT_LOCK="FEISHU_DEPT_LOCK_%s";
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Override
    public String getSupportEventType() {
        return "contact.department.updated_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactDepartmentUpdatedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactDepartmentUpdateV3Event event = JSON.parseObject(eventData, FeishuContactDepartmentUpdateV3Event.class);
        LogUtils.info("ContactDepartmentUpdatedV3EventHandler.handle,event={}",event);
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(header.getTenantKey())
                .appId(header.getAppId()).bindStatus(BindStatusEnum.normal).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : enterpriseBindList) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
                if(settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.manual||settingAccountRulesModel.getSyncTypeEnum()== EnterpriseConfigAccountSyncTypeEnum.accountBind){
                log.info("ContactDepartmentUpdatedV3EventHandler.handle,outEa={},账号设置手动绑定，不支持更新部门:{}",header.getTenantKey(),outerOaEnterpriseBindEntity.getFsEa());
                contactsService.saveOrUpdateContactDepartment(header.getEventType(), outerOaEnterpriseBindEntity,
                        event.getObject(), event.getOldObject(), eventData);
            }else{
                String openDepartmentId = event.getObject().getOpenDepartmentId();
                RLock oaLock = redissonClient.getLock(String.format(FEISHU_DEPT_LOCK, openDepartmentId));
                try {
                    boolean lockValue = oaLock.tryLock(10, TimeUnit.SECONDS);
                    //避免报错，没有解锁
                    if(lockValue){
                        try {
                            Result<Void> result = contactsService.addDepList(header.getAppId(),
                                    header.getTenantKey(),
                                    Lists.newArrayList(event.getObject()),outerOaEnterpriseBindEntity);
                            LogUtils.info("ContactDepartmentUpdatedV3EventHandler.handle,department update, not supported");
                        } catch (Exception e) {
                            log.error("departMenet update fail:{}",e);
                        } finally {
                            oaLock.unlock();
                        }
                    }
                } catch (Exception e) {
                    log.error("contact dept get lock fail:{}",e.getMessage());
                }
            }
        }
        return SUCCESS;
    }
}
