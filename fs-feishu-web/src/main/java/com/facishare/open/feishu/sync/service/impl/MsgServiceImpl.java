package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.SendMessage.SendMessageErrorResult;
import com.facishare.open.feishu.syncapi.model.event2.FeishuIMMessageReceiveV1Event;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.FeishuMessageService;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service("msgService")
public class MsgServiceImpl implements MsgService {
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private EmployeeBindManager employeeBindManager;
    @Autowired
    private FeishuAppManager feishuAppManager;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private FeishuMessageService feishuMessageService;
    @Autowired
    private UrlManager urlManager;
    @ReloadableProperty("connector_domain")
    private String connectorDomain; //比如 https://www.fxiaoke.com/erpdss

    @Override
    public Result<Void> batchSend(MsgTypeEnum msgType,
                                    String fsEa,
                                    List<String> fsUserIdList,
                                    Object content,
                                    String appId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntityList = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), fsEa,null);


        LogUtils.info("batchSend,enterpriseBindEntityList={}",enterpriseBindEntityList);
        if(CollectionUtils.isEmpty(enterpriseBindEntityList)) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }
        for(OuterOaEnterpriseBindEntity entity : enterpriseBindEntityList) {
            List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList(fsEa, entity.getOutEa(), fsUserIdList);
            if(CollectionUtils.isEmpty(employeeBindEntities)) {
                LogUtils.info("batchSend,employeeBindEntities is empty,fsEa={},outEa={},fsUserIdList={}",fsEa,entity.getOutEa(),fsUserIdList);
//                return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
                continue;
            }
            List<String> receiverIds = employeeBindEntities.stream().map(EmployeeBindEntity::getOutUserId).collect(Collectors.toList());
            if(StringUtils.isEmpty(appId)) {
                //appId为空默认为第三方应用
                appId = ConfigCenter.feishuCrmAppId;
            }
            LogUtils.info("batchSend,appId={},outEa={},msgType={},receiverIds={},content={}",appId,entity.getOutEa(),msgType,receiverIds,content);
            Result<Void> result = batchSend(appId, entity.getOutEa(), msgType, receiverIds, content);
            LogUtils.info("batchSend,result={}",result);
            if(!result.isSuccess()) {
                return result;
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> batchSend(String appId,
                             String tenantKey,
                             MsgTypeEnum msgType,
                             List<String> receiverIds,
                             Object content) {
        String url = urlManager.getFeishuUrl(tenantKey, appId, "https://open.feishu.cn/open-apis/message/v4/batch_send/");
        HashMap<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("open_ids", receiverIds);
        //请注意卡片消息的请求体不使用content字段，使用card字段
        if(msgType.equals(MsgTypeEnum.interactive)) {
            bodyParams.put("card", content);
        } else {
            bodyParams.put("content", content);
        }
        bodyParams.put("msg_type", msgType.name());

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.message_v4_batch_send.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        SendMessageErrorResult result = proxyHttpClient.postUrl(url, bodyParams, headerMap, new TypeReference<SendMessageErrorResult>() {
        });
        if(result.getCode() == 0) {
            return Result.newSuccess();
        } else {
            return Result.newError(result.getCode(), result.getMsg() + "->" + result.getError());
        }
    }

    @Override
    public Result<Void> replyIMMessage(String appId,
                                       String tenantKey,
                                       FeishuIMMessageReceiveV1Event event) {
        String json = GlobalValue.BOT_REPLY_MESSAGE_MODEL;
        if(ConfigCenter.CURRENT_CHANNEL_ISV_CHANNEL.equals(ConfigCenter.LARK_CONSTANTS)){
            json = GlobalValue.BOT_REPLY_MESSAGE_MODEL_EN;
        }
        //mode统一设为window，当设为appCenter时，web端会报错
        String url = urlManager.getFeishuUrl(tenantKey, appId, "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=open_id");
        String loginUrl = urlManager.getFeishuUrl(tenantKey, appId, "https://open.feishu.cn/open-apis/authen/v1/index?app_id={app_id}&state={app_id}&redirect_uri={connector_domain}/feishu/external/loginAuth");
        loginUrl = loginUrl.replace("{app_id}",appId).replace("{connector_domain}",connectorDomain);
        url = url.replace("{url}", URLEncoder.encode(loginUrl));
        json = json.replace("{crm_app_url}",url);

        //需要判断是否是单聊还是群聊发消息，单聊不需要准确回复某条消息，群里需要回复@机器人的消息，并且是@全部人员的时候，也不需要发送消息
        if(event.getMessage().getChatType().equals(ChatTypeEnum.p2p.toString())) {
            return feishuMessageService.send(appId, tenantKey, MsgTypeEnum.interactive, event.getSender().getSenderId().getOpenId(), json);
        } else if(CollectionUtils.isNotEmpty(event.getMessage().getMentions())) {
            List<FeishuIMMessageReceiveV1Event.Message.Mentions> mentions = event.getMessage().getMentions().stream()
                    .filter(v -> v.getKey().equals(GlobalValue.MENTION_KEY_ALL))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(mentions)) {
                return feishuMessageService.reply(appId, tenantKey, event.getMessage().getMessageId(), json, MsgTypeEnum.interactive);
            }
        }
        return new Result<>();
    }

    @Override
    public Result<Void> GroupChatAddBotToSendMessage(String appId, String tenantKey, String chatId) {
        String json = GlobalValue.WELCOME_MESSAGE_MODEL;
        if(ConfigCenter.CURRENT_CHANNEL_ISV_CHANNEL.equals(ConfigCenter.LARK_CONSTANTS)){
            json=GlobalValue.WELCOME_MESSAGE_MODEL_EN;
        }
        String url = urlManager.getFeishuUrl(tenantKey, appId, "https://applink.feishu.cn/client/web_url/open?mode=window&url={url}");
        String loginUrl = urlManager.getFeishuUrl(tenantKey,appId, "https://open.feishu.cn/open-apis/authen/v1/index?app_id={app_id}&state={app_id}&redirect_uri={connector_domain}/feishu/external/loginAuth");
        loginUrl = loginUrl.replace("{app_id}",appId).replace("{connector_domain}",connectorDomain);
        url = url.replace("{url}", URLEncoder.encode(loginUrl));
        json = json.replace("{crm_app_url}",url);
        return feishuMessageService.send(appId, tenantKey, MsgTypeEnum.interactive, ReceiverIdTypeEnum.chat_id, chatId, json);
    }
}
