package com.facishare.open.feishu.sync.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 员工绑定关系Mapper接口
 * 
 * 关于分页查询的说明： 1. 通常情况下，无需在Mapper中定义自定义分页方法，直接使用BaseMapper的selectPage方法 2.
 * 确保spring-db.xml中配置了分页插件(PaginationInnerInterceptor) 3.
 * 在Service层直接调用下面的代码进行分页：
 * 
 * <pre>
 * // 创建Page对象
 * Page&lt;EmployeeBindEntity&gt; page = new Page&lt;&gt;(current, size);
 * // 构建查询条件
 * QueryWrapper&lt;EmployeeBindEntity&gt; wrapper = new QueryWrapper&lt;&gt;();
 * wrapper.eq("字段名", 值);
 * // 执行分页查询
 * Page&lt;EmployeeBindEntity&gt; result = employeeBindMapper.selectPage(page, wrapper);
 * // 获取分页数据
 * List&lt;EmployeeBindEntity&gt; records = result.getRecords();
 * long total = result.getTotal();
 * </pre>
 * 
 * 注意：selectPage_mpCount方法是为了兼容现有测试代码而保留的，新代码应直接使用BaseMapper提供的方法。
 */
public interface EmployeeBindMapper extends BaseMapper<EmployeeBindEntity> {
    /**
     * 自定义分页查询的计数方法 此方法仅为了兼容现有代码，新代码不应使用
     * 
     * @param queryWrapper 查询条件
     * @return 记录总数
     */
    @Select("SELECT COUNT(*) FROM tb_employee_bind ${ew.customSqlSegment}")
    Long selectPage_mpCount(@Param(Constants.WRAPPER) Wrapper<EmployeeBindEntity> queryWrapper);
}