package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserDeletedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工离职处理器
 * <AUTHOR>
 * @date 20220818
 */
@Slf4j
@Component
public class ContactUserDeletedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private ObjectDataManager objectDataManager;

    @Override
    public String getSupportEventType() {
        return "contact.user.deleted_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactUserDeletedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactUserDeletedV3Event event = JSON.parseObject(eventData, FeishuContactUserDeletedV3Event.class);
        LogUtils.info("ContactUserDeletedV3EventHandler.handle,event={}",event);

        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(header.getTenantKey())
                .appId(header.getAppId()).bindStatus(BindStatusEnum.normal).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : enterpriseBindList) {
            Result<Void> result = contactsService.removeUserList(header.getAppId(),
                    header.getTenantKey(),
                    Lists.newArrayList(event.getObject()),outerOaEnterpriseBindEntity);
            LogUtils.info("ContactUserDeletedV3EventHandler.handle,removeUserList,result={}",result);
        }
        return SUCCESS;
    }
}
