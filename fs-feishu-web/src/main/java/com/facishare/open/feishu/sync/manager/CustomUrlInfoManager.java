package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.CustomUrlInfoMapper;
import com.facishare.open.feishu.syncapi.entity.CustomUrlInfoEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class CustomUrlInfoManager {
    @Resource
    private CustomUrlInfoMapper customUrlInfoMapper;

    public Integer insert(CustomUrlInfoEntity entity) {
        int count = customUrlInfoMapper.insert(entity);
        LogUtils.info("CalendarManager.insert,count={}",count);
        return count;
    }

    public Integer update(CustomUrlInfoEntity entity) {
        int count = customUrlInfoMapper.updateById(entity);
        LogUtils.info("CalendarManager.update,count={}",count);
        return count;
    }

    public List<CustomUrlInfoEntity> queryEntities(ChannelEnum channel) {
        LambdaQueryWrapper<CustomUrlInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomUrlInfoEntity::getChannel, channel);

        return customUrlInfoMapper.selectList(wrapper);
    }
}
