package com.facishare.open.feishu.web.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.outer.oa.connector.common.api.info.FsServiceAuthInfo;
import com.facishare.open.feishu.syncapi.service.ServiceAuthService;
import com.facishare.open.feishu.web.enums.UserContextSingleton;
import com.facishare.open.feishu.web.exception.NoPermissionException;
import com.facishare.open.feishu.web.utils.MD5Helper;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.uc.api.model.usertoken.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * 用户权限验证拦截器
 * 
 * <AUTHOR>
 * @date 2023.5.16
 */
@Component
@Slf4j
// IgnoreI18nFile
public class UserInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Autowired
    private ServiceAuthService serviceAuthService;

    private void getUserFromFsCookie(String fsAuthXCCookie, String requestURI) {
        log.info("UserInterceptor.getUserFromFsCookie,fsAuthXCCookie={}", fsAuthXCCookie);
        CookieToAuth.Argument argument = new CookieToAuth.Argument();
        argument.setCookie(fsAuthXCCookie);
        CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
        if (!result.isSucceed() || result.getValidateStatus() != ValidateStatus.NORMAL) {
            log.info("UserInterceptor.getUserFromFsCookie,failed,result={}", result);
            // 跨云解析cookie会导致失败,但是可以正常返回body
            if (ObjectUtils.isNotEmpty(result.getBody())) {
                AuthXC authXC = result.getBody();
                log.info("UserInterceptor.getUserFromFsCookie,cookieToAuthXC is false,authXC={}",
                        JSON.toJSONString(authXC));
                Integer ei = authXC.getEnterpriseId();
                String ea = authXC.getEnterpriseAccount();
                Integer employeeId = authXC.getEmployeeId();
                UserContextSingleton.INSTANCE.setUserContext(new User(ei, ea, employeeId, authXC.getDeviceId()));
            }
            return;
        }
        AuthXC authXC = result.getBody();
        log.info("UserInterceptor.getUserFromFsCookie,authXC={}", JSON.toJSONString(authXC));

        Integer ei = authXC.getEnterpriseId();
        String ea = authXC.getEnterpriseAccount();
        Integer employeeId = authXC.getEmployeeId();

        // 超级管理员接口权限验证
        if (StringUtils.containsIgnoreCase(requestURI, "/feishu/admin")) {
            if (ConfigCenter.superAdminMap.containsKey(ea) && ConfigCenter.superAdminMap.get(ea).contains(employeeId)) {
                // 管理员身份验证通过
                log.info("UserInterceptor.getUserFromFsCookie,requestURI={},pass", requestURI);
                UserContextSingleton.INSTANCE.setUserContext(new User(ei, ea, employeeId, authXC.getDeviceId()));
                return;
            } else {
                throw new NoPermissionException("管理员身份验证失败"); // ignorei18n
            }
        }

        // 需要鉴权的接口名单，这些接口是企微管理后台WEB调用的接口
        for (String uri : ConfigCenter.needAuthInterfaceList) {
            if (StringUtils.containsIgnoreCase(requestURI, uri)) {
                log.info("UserInterceptor.preHandle,requestURI={},need auth", requestURI);
                try {
                    Result<Boolean> roleCode = fsEmployeeServiceProxy.hasRoleCode(ei + "", employeeId + "",
                            ConfigCenter.validRoleCodeList);
                    log.info("UserInterceptor.getUserFromFsCookie,roleCode={}", roleCode);
                    if (roleCode.getData() == false) {
                        throw new NoPermissionException("用户没有权限访问当前URL"); // ignorei18n
                    }
                } catch (Exception e) {
                    log.info("UserInterceptor.getUserFromFsCookie,hasRoleCode,exception={}", e.getMessage());
                }
            }
        }

        log.info("UserInterceptor.getUserFromFsCookie,requestURI={},passed", requestURI);
        UserContextSingleton.INSTANCE.setUserContext(new User(ei, ea, employeeId, authXC.getDeviceId()));
    }

    private void FillUserContext(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (Objects.isNull(cookies)) {
            if (StringUtils.containsIgnoreCase(request.getRequestURI(), "/feishu/admin")) {
                throw new NoPermissionException("管理员身份验证失败"); // ignorei18n
            }
            return;
        }
        log.info("UserInterceptor.preHandle,cookies={}", JSONObject.toJSONString(cookies));
        for (Cookie cookie : cookies) {
            if (StringUtils.equalsIgnoreCase("lang", cookie.getName())) {
                String lang = cookie.getValue();
                log.info("UserInterceptor.preHandle,lang={}", lang);
                TraceUtils.setLocale(lang);
            }
            if ("FSAuthXC".equalsIgnoreCase(cookie.getName())) {
                getUserFromFsCookie(cookie.getValue(), request.getRequestURI());
            }
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        TraceUtils.initTraceId(UUID.randomUUID().toString());

        String requestURI = request.getRequestURI();
        List<String> headers = Collections.list(request.getHeaderNames());
        log.info("UserInterceptor.preHandle,requestURI={},headers={}", requestURI, JSONObject.toJSONString(headers));

        Enumeration<String> headerNames = request.getHeaderNames();

        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                log.info("UserInterceptor.preHandle all header,requestURI={},headers={}", requestURI,
                        headerName + ": " + headerValue);
            }
        }

        // 需要验证纷享用户身份的接口
        FillUserContext(request);

        // 需要业务调用鉴权的接口
        verifyFsAuth(request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        // 每次请求结束，清除当前线程数据 （tomcat是使用线程池来处理业务的）
        UserContextSingleton.INSTANCE.removeUserContext();
    }

    private void verifyFsAuth(HttpServletRequest request) {
        // 过滤掉不需要业务鉴权的接口
        if (!StringUtils.containsIgnoreCase(request.getRequestURI(), "/oaconnector/web3")
                && !StringUtils.containsIgnoreCase(request.getRequestURI(), "/feishu/tools")) {
            return;
        }
        // 获取业务key和secret
        String fsService = request.getHeader("fs-service");
        String fsKey = request.getHeader("fs-key");
        String fsSecret = request.getHeader("fs-secret");
        if (StringUtils.isAnyEmpty(fsService, fsKey, fsSecret)) {
            throw new NoPermissionException("当前业务没有权限访问当前URL"); // ignorei18n
        }

        // 加密后校验
        String fsServiceEncryption;
        String fsKeyEncryption;
        String fsSecretEncryption;
        try {
            fsServiceEncryption = MD5Helper.getStringMD5(fsService);
            fsKeyEncryption = MD5Helper.getStringMD5(fsKey);
            fsSecretEncryption = MD5Helper.getStringMD5(fsSecret);
        } catch (NoSuchAlgorithmException e) {
            throw new NoPermissionException("权限校验失败"); // ignorei18n
        }
        com.facishare.open.feishu.syncapi.result.Result<FsServiceAuthInfo> serviceAuthResult = serviceAuthService
                .getServiceAuth(fsServiceEncryption);
        if (!serviceAuthResult.isSuccess() || ObjectUtils.isEmpty(serviceAuthResult.getData())) {
            throw new NoPermissionException("当前业务没有权限访问当前URL，请输入正确的服务名"); // ignorei18n
        }

        if (serviceAuthResult.getData().getStatus() != 0) {
            throw new NoPermissionException("当前业务已被停用"); // ignorei18n
        }

        log.info("UserInterceptor.verifyOutAuth,fsServiceEncryption={},fsKeyEncryption={},fsSecretEncryption={}",
                fsServiceEncryption, fsKeyEncryption, fsSecretEncryption);

        if (!(fsKeyEncryption.equals(serviceAuthResult.getData().getFsKey())
                && fsSecretEncryption.equals(serviceAuthResult.getData().getFsSecret()))) {
            throw new NoPermissionException("当前业务没有权限访问当前URL，请输入正确的业务key和secret"); // ignorei18n
        }
    }
}
