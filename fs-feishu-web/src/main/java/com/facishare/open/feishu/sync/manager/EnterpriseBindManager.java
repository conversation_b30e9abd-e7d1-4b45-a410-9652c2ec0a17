package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.mapper.EnterpriseBindMapper;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;

/**
 * tb_enterprise_bind表管理器类
 * <AUTHOR>
 * @date 20220722
 */
@Slf4j
@Component
@Deprecated
public class EnterpriseBindManager {
    @Resource
    private EnterpriseBindMapper enterpriseBindMapper;
    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;
    @Autowired
    private ToolsManager toolsManager;


    public int insert(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.insert(entity);
        LogUtils.info("EnterpriseBindManager.insert,count={}",count);
        return count;
    }

    public int insert(ChannelEnum channel,
                      String fsEa,
                      String outEa,
                      String domain,
                      BindTypeEnum bindType,
                      BindStatusEnum bindStatus,String connectParams,String appId) {
        LogUtils.info("insert entity:{}message",fsEa);
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .channel(channel)
                .fsEa(fsEa)
                .outEa(outEa)
                .domain(domain)
                .bindType(bindType)
                .bindStatus(bindStatus)
                .appId(appId)
                .connectParams(connectParams)
                .extend(GlobalValue.enterprise_extend)
                .build();
        return insert(entity);
    }
    public int insert(ChannelEnum channel,
                      String fsEa,
                      String outEa,
                      String domain,
                      BindTypeEnum bindType,
                      BindStatusEnum bindStatus) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .channel(channel)
                .fsEa(fsEa)
                .outEa(outEa)
                .domain(domain)
                .bindType(bindType)
                .bindStatus(bindStatus)
                .extend(GlobalValue.enterprise_extend)
                .build();
        return insert(entity);
    }

    public int updateById(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.updateById(entity);
        LogUtils.info("EnterpriseBindManager.updateById,count={}",count);
        return count;
    }

    public int updateBindStatus(String fsEa,
                                String outEa,
                                BindStatusEnum bindStatus) {
        EnterpriseBindEntity entity = EnterpriseBindEntity.builder()
                .bindStatus(bindStatus)
                .build();

        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EnterpriseBindEntity::getOutEa,outEa);
        }
        int count = enterpriseBindMapper.update(entity, wrapper);
        LogUtils.info("EnterpriseBindManager.updateBindStatus,count={}",count);
        return count;
    }

    public List<EnterpriseBindEntity> getEnterpriseBindList(String outEa) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public List<EnterpriseBindEntity> getAllEnterpriseBindList(String outEa) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);

        return enterpriseBindMapper.selectList(wrapper);
    }

    public EnterpriseBindEntity getEntity(String fsEa) {
        return getEntity(ChannelEnum.feishu,fsEa,null);
    }

    public EnterpriseBindEntity getEntity(String fsEa, String outEa) {
        return getEntity(ChannelEnum.feishu,fsEa,outEa,null,null,null);
    }

    public EnterpriseBindEntity getEntityByOutEAaAppId(String outEa, String appId) {
        return getEntity(ChannelEnum.feishu,null,outEa,null,null,appId);
    }
    public EnterpriseBindEntity getEntityFsEaAppId(String fsEa, String appId) {
        return getEntity(ChannelEnum.feishu,fsEa,null,null,null,appId);
    }
    public EnterpriseBindEntity getEntityByOutEAafsEaAppId(String outEa, String fsEa, String appId) {
        return getEntity(ChannelEnum.feishu,fsEa,outEa,null,null,appId);
    }


    public EnterpriseBindEntity getEntity(String fsEa,BindStatusEnum bindStatus) {
        return getEntity(ChannelEnum.feishu,fsEa,bindStatus);
    }

    public EnterpriseBindEntity getEntity(String fsEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        return getEntity(ChannelEnum.feishu,fsEa,bindStatus,bindType);
    }

    public EnterpriseBindEntity getEntityByOutEa(String outEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        return getEntity(ChannelEnum.feishu,null,outEa,bindStatus,bindType,null);
    }

    public EnterpriseBindEntity getEntity(String fsEa, String outEa, BindStatusEnum bindStatus) {
        return getEntity(ChannelEnum.feishu,fsEa,outEa,bindStatus,null,null);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus) {
        return getEntity(channel,fsEa,null,bindStatus,null,null);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus,BindTypeEnum bindType) {
        return getEntity(channel,fsEa,null,bindStatus,bindType,null);
    }

    public EnterpriseBindEntity getEntity(ChannelEnum channel,String fsEa,String outEa,BindStatusEnum bindStatus,BindTypeEnum bindType,String appId) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        }
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        }
//        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(bindStatus!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus,bindStatus);
        }

        if(bindType!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindType,bindType);
        }
        if(appId!=null) {
            wrapper.eq(EnterpriseBindEntity::getAppId,appId);
        }

        return enterpriseBindMapper.selectOne(wrapper);
    }

    public List<EnterpriseBindEntity> getEntityList(String fsEa,BindStatusEnum bindStatus) {
        return getEntityList(ChannelEnum.feishu, fsEa, bindStatus);
    }

    public List<EnterpriseBindEntity> getEntityList(ChannelEnum channel,String fsEa,BindStatusEnum bindStatus) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel,channel);
        wrapper.eq(EnterpriseBindEntity::getFsEa,fsEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(bindStatus!=null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus,bindStatus);
        }

        return enterpriseBindMapper.selectList(wrapper);
    }

    public EnterpriseBindEntity getEntity(EnterpriseBindEntity entity) {
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getChannel, entity.getChannel());
        wrapper.eq(EnterpriseBindEntity::getDomain, ConfigCenter.crm_domain);
        if(StringUtils.isNotEmpty(entity.getFsEa())) {
            wrapper.eq(EnterpriseBindEntity::getFsEa, entity.getFsEa());
        }
        if(StringUtils.isNotEmpty(entity.getOutEa())) {
            wrapper.eq(EnterpriseBindEntity::getOutEa, entity.getOutEa());
        }
        if(StringUtils.isNotEmpty(entity.getAppKey())) {
            wrapper.eq(EnterpriseBindEntity::getAppKey, entity.getAppKey());
        }
        if(StringUtils.isNotEmpty(entity.getAccessKey())) {
            wrapper.eq(EnterpriseBindEntity::getAccessKey, entity.getAccessKey());
        }
        if(StringUtils.isNotEmpty(entity.getAccessSecret())) {
            wrapper.eq(EnterpriseBindEntity::getAccessSecret, entity.getAccessSecret());
        }
        if(entity.getBindType() != null) {
            wrapper.eq(EnterpriseBindEntity::getBindType, entity.getBindType());
        }
        if(entity.getBindStatus() != null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus, entity.getBindStatus());
        }

        return enterpriseBindMapper.selectOne(wrapper);
    }

    /**
     * 删除已绑定的企业数据
     *
     * @param fsEa
     * @return
     */
    public int deleteByFsEa(String fsEa, String outEa) {
        LambdaUpdateWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outEa);
        int count = outerOaEnterpriseBindMapper.delete(wrapper);
        LogUtils.info("EnterpriseBindManager.deleteByFsEa,count={}", count);
        return count;
    }

    /**
     * 只删除当前域名下的企业绑定关系
     * @param fsEa
     * @param outEa
     * @param domain
     * @return
     */
    public int deleteByFsEa(String fsEa, String outEa, String domain) {
        LambdaUpdateWrapper<EnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(EnterpriseBindEntity::getFsEa, fsEa);
        wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(EnterpriseBindEntity::getDomain, domain);
        int count = enterpriseBindMapper.delete(wrapper);
        LogUtils.info("EnterpriseBindManager.deleteByFsEa,domain={},count={}", domain, count);
        return count;
    }

    /**
     * 更新企业绑定扩展字段
     * @param fsEa
     * @param entity
     * @return
     */
    public int updateExtend(String fsEa, String outEa, OuterOaEnterpriseBindEntity entity) {
        LambdaUpdateWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa,fsEa);
        if(StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa,outEa);
        }
        int count = outerOaEnterpriseBindMapper.update(entity, wrapper);
        LogUtils.info("EnterpriseBindManager.updateExtend,count={}",count);
        return count;
    }

    public List<OuterOaEnterpriseBindEntity> getEnterpriseBindListByBindType(BindTypeEnum bindType) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getBindType,bindType);

        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }

    public OuterOaEnterpriseBindEntity getEnterpriseBindByAppId(String appId) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getAppId,appId);
        return outerOaEnterpriseBindMapper.selectOne(wrapper);
    }

    public List<OuterOaEnterpriseBindEntity> getEnterpriseBindListByBindStatus(BindStatusEnum bindStatus) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, bindStatus);

        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }

    public List<OuterOaEnterpriseBindEntity> getEnterpriseBindListByChannelEnum(ChannelEnum channelEnum) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, ChannelEnum.feishu);
        return outerOaEnterpriseBindMapper.selectList(wrapper);
    }

    public Integer update(EnterpriseBindEntity entity) {
        int count = enterpriseBindMapper.updateById(entity);
        LogUtils.info("EnterpriseBindManager.update,count={}",count);
        return count;
    }

    public OuterOaEnterpriseBindEntity getEnterpriseBind(String outEa, String fsEa) {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);


        return outerOaEnterpriseBindMapper.selectOne(wrapper);
    }

    /**
     * 基础分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public Page<EnterpriseBindEntity> getPage(Integer pageNum, Integer pageSize) {
        Page<EnterpriseBindEntity> page = new Page<>(pageNum, pageSize);
        return enterpriseBindMapper.selectPage(page, null);
    }

    /**
     * 条件分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param channelEnums 渠道
     * @param bindStatus 绑定状态
     * @param bindType 绑定类型
     * @param fsEa 飞书企业ID
     * @param outEa 外部企业ID
     * @return 分页结果
     */
    public Page<EnterpriseBindEntity> getPageWithCondition(Integer pageNum, 
                                                          Integer pageSize,
                                                          List<ChannelEnum> channelEnums,
                                                          BindStatusEnum bindStatus,
                                                          BindTypeEnum bindType,
                                                          String fsEa,
                                                          String outEa) {
        Page<EnterpriseBindEntity> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<EnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (channelEnums != null) {
            wrapper.in(EnterpriseBindEntity::getChannel, channelEnums);
        }
        if (bindStatus != null) {
            wrapper.eq(EnterpriseBindEntity::getBindStatus, bindStatus);
        }
        if (bindType != null) {
            wrapper.eq(EnterpriseBindEntity::getBindType, bindType);
        }
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(EnterpriseBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(EnterpriseBindEntity::getOutEa, outEa);
        }
        
        // 添加排序
        wrapper.orderByDesc(EnterpriseBindEntity::getCreateTime);
        
        return enterpriseBindMapper.selectPage(page, wrapper);
    }

    /**
     * 外部企业绑定关系分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param channel 渠道
     * @param bindStatus 绑定状态
     * @param bindType 绑定类型
     * @param fsEa 飞书企业ID
     * @param outEa 外部企业ID
     * @return 分页结果
     */
    public Page<OuterOaEnterpriseBindEntity> getOuterBindPage(Integer pageNum,
                                                             Integer pageSize,
                                                             ChannelEnum channel,
                                                             BindStatusEnum bindStatus,
                                                             BindTypeEnum bindType,
                                                             String fsEa,
                                                             String outEa) {
        Page<OuterOaEnterpriseBindEntity> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件public class EnterpriseBindManager {
        if (channel != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, channel);
        }
        if (bindStatus != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, bindStatus);
        }
        if (bindType != null) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getBindType, bindType);
        }
        if (StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
        }
        if (StringUtils.isNotEmpty(outEa)) {
            wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outEa);
        }
        
        // 添加排序
        wrapper.orderByDesc(OuterOaEnterpriseBindEntity::getCreateTime);
        
        return outerOaEnterpriseBindMapper.selectPage(page, wrapper);
    }


    /**
     * 批量处理规则
     */
    public void transferBindRules(){

    }


    /**
     * 批量处理企业列表

     */
    public void batchProcessEnterprises() {
        int pageSize = 100;
        int pageNum = 1;
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        // 创建线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            10, // 核心线程数
            20, // 最大线程数
            60L, // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "enterprise-transfer-thread-" + threadNumber.getAndIncrement());
                }
            }
        );

        try {
            while (true) {
                // 分页获取企业列表
                Page<EnterpriseBindEntity> page = getPageWithCondition(
                    pageNum,
                    pageSize, Lists.newArrayList(ChannelEnum.feishu,ChannelEnum.lark),null,null,null,null);

                List<EnterpriseBindEntity> enterprises = page.getRecords();
                if (enterprises.isEmpty()) {
                    break;
                }

                // 将企业列表分成多个小批次
                List<List<EnterpriseBindEntity>> batches = new ArrayList<>();
                for (int i = 0; i < enterprises.size(); i += 10) {
                    int end = Math.min(i + 10, enterprises.size());
                    batches.add(enterprises.subList(i, end));
                }

                // 使用线程池处理每个批次
                List<Future<?>> futures = new ArrayList<>();
                for (List<EnterpriseBindEntity> batch : batches) {
                    Future<?> future = executor.submit(() -> {
                        for (EnterpriseBindEntity enterprise : batch) {
                            try {
                                // 处理每个企业
                                toolsManager.transfer(enterprise.getFsEa());
                                successCount.incrementAndGet();
                            } catch (Exception e) {
                                failCount.incrementAndGet();
                                log.error("处理企业失败: fsEa={}, outEa={}, error={}", 
                                    enterprise.getFsEa(), 
                                    enterprise.getOutEa(), 
                                    e.getMessage(), 
                                    e);
                            }
                        }
                    });
                    futures.add(future);
                }

                // 等待所有任务完成
                for (Future<?> future : futures) {
                    try {
                        future.get();
                    } catch (Exception e) {
                        log.error("等待任务完成时发生错误", e);
                    }
                }

                // 如果当前页记录数小于页大小，说明已经是最后一页
                if (enterprises.size() < pageSize) {
                    break;
                }

                pageNum++;
            }
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 打印执行结果
        log.info("企业处理完成 - Transfer success! 总处理: {}, 成功: {}, 失败: {}", 
            successCount.get() + failCount.get(),
            successCount.get(),
            failCount.get()
        );
    }


    public void transferRules(){

    }
}
