package com.facishare.open.feishu.sync.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.feishu.syncapi.model.DealTodoOpinion;
import com.facishare.open.feishu.syncapi.model.ExternalTodoMsgModel;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskUpdateDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalTaskResult;
import com.facishare.open.feishu.syncapi.result.ExternalInstancesDetailResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.vo.InstanceVo.ExternalMessageInstanceVo;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageTemplateEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeDataParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.qixin.api.model.pushSession.arg.ExternalMessage;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.model.todo.TaskStatusEnum;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ExternalTodoManager {
    @Resource
    private ExternalTodoTaskManager externalTodoTaskManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private ExternalApprovalsService externalApprovalsService;
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private EmployeeBindManager employeeBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    public Result<Void> createExternalApprovalTodo(CreateTodoPushArg createTodoPushArg,
            OuterOaMessageTemplateEntity outerOaMessageTemplateEntity,
            List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        String msgType = createTodoPushArg.getMsgType();
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String dcId = enterpriseBindEntity.getId();
        OuterOaEmployeeBindEntity todoCreateEmployee = outerOaEmployeeBindManager.getEntitiesByDcId(dcId, String.valueOf(createTodoPushArg.getCreateTodoArg().getSenderId()), null);
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId
                : enterpriseBindEntity.getAppId();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        OuterOaMessageBindParams outerOaMessageBindParams = OuterOaMessageBindParams.builder()
                .channel(channelEnum).fsEa(fsEa).outEa(outEa).sourceId(createTodoArg.getSourceId()).messageType(OuterOaMessageBindMsgTypeEnum.todo).build();
        OuterOaMessageBindEntity outerOaMessageBindEntities = outerOaMessageBindManager
                .getEntity(outerOaMessageBindParams);
        LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,externalTodoInstanceEntity={}",
                outerOaMessageBindEntities);
        String curTime = System.currentTimeMillis() + "";
        ExternalInstancesDetail externalInstancesDetail;
        ExternalInstancesDetail.Links links;
        if (ObjectUtils.isEmpty(outerOaMessageBindEntities)) {
            externalInstancesDetail = new ExternalInstancesDetail();
            List<ExternalInstancesDetail.TextResource> texts = new LinkedList<>();
            List<ExternalInstancesDetail.Form> forms = new LinkedList<>();

            String todoUrl = feishuApprovalUrlGenerator(createTodoPushArg);
            if (StringUtils.isEmpty(todoUrl)) {
                // 暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,param is null,createTodoPushArg={}.",
                        createTodoPushArg);
                ExternalMessageInstanceVo externalMessageInstanceVo = ExternalMessageInstanceVo.builder()
                        .taskId(createTodoArg.getExtraDataMap().get("taskId"))
                        .workflowInstanceId(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                        .objectId(createTodoArg.getExtraDataMap().get("objectId"))
                        .objectApiName(createTodoArg.getExtraDataMap().get("objectApiName")).build();
                String outEmpId=ObjectUtils.isNotEmpty(todoCreateEmployee)?todoCreateEmployee.getOutEmpId():"1000";
                OuterOaMessageBindEntity outerOaMessageBindEntity = OuterOaMessageBindEntity.builder()
                        .channel(channelEnum).dcId(dcId).fsEa(fsEa).outEa(outEa).appId(appId)
                        .messageType(OuterOaMessageBindMsgTypeEnum.todo)
                        .eventType(OuterOaMessageBindEventTypeEnum.commonMsg)
                        .outUserId(outEmpId)
                        .templateId(outerOaMessageTemplateEntity.getTemplateId()).createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis())
                        .messageInfo(JSON.toJSONString(externalMessageInstanceVo)).sourceId(createTodoArg.getSourceId())
                        .build();
                Integer count = outerOaMessageBindManager.batchUpsertInfos(Lists.newArrayList(outerOaMessageBindEntity));
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,insert,count={}", count);

                return Result.newSuccess();
            }

            byte[] todoUrlBytes = todoUrl.getBytes();
            String param = new String(Base64.encodeBase64(todoUrlBytes));
            String param2 = "";
            if (msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                // 身份透传
                ExternalTodoMsgModel externalTodoMsgModel = new ExternalTodoMsgModel();
                externalTodoMsgModel.setFsEa(fsEa);
                externalTodoMsgModel.setOutEa(outEa);
                externalTodoMsgModel.setTaskId(createTodoArg.getExtraDataMap().get("taskId"));
                externalTodoMsgModel.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                externalTodoMsgModel.setUpstreamEa(upstreamEa);
                externalTodoMsgModel.setFsAppId(createTodoArg.getAppId());
                param2 = new String(Base64.encodeBase64(new Gson().toJson(externalTodoMsgModel).getBytes()));
            }

            String authUrl = ConfigCenter.FEISHU_AUTHEN_URL.replace("{app_id}", appId).replace("{state}", appId)
                    .replace("{redirect_uri}", URLEncoder.encode(
                            ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&param2=" + param2 + "&fsEa=" + fsEa));

            ExternalInstancesDetail.TextResource titleText = new ExternalInstancesDetail.TextResource();
            titleText.setKey("@i18n@1");
            titleText.setValue(createTodoArg.getTitle());

            if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
                int j = 2;
                for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                    ExternalInstancesDetail.Form form = new ExternalInstancesDetail.Form();
                    form.setName("@i18n@" + j);
                    form.setValue("@i18n@" + (j + 1));
                    forms.add(form);

                    ExternalInstancesDetail.TextResource formText1 = new ExternalInstancesDetail.TextResource();
                    formText1.setKey("@i18n@" + j);
                    formText1.setValue(createTodoArg.getForm().get(i).getKey());
                    texts.add(formText1);

                    ExternalInstancesDetail.TextResource formText2 = new ExternalInstancesDetail.TextResource();
                    formText2.setKey("@i18n@" + (j + 1));
                    formText2.setValue(createTodoArg.getForm().get(i).getValue());
                    texts.add(formText2);
                    j += 2;
                }
            } else {
                titleText.setValue(createTodoArg.getTitle() + "：" + createTodoArg.getContent());
            }
            texts.add(titleText);

            externalInstancesDetail.setApprovalCode(outerOaMessageTemplateEntity.getTemplateId());
            externalInstancesDetail.setStatus(ApprovalStatusEnum.PENDING.name());
            externalInstancesDetail.setInstanceId(outEa + appId + createTodoArg.getSourceId());
            externalInstancesDetail.setTitle("@i18n@1");
            externalInstancesDetail.setForm(forms);
            links = new ExternalInstancesDetail.Links();
            links.setPcLink(authUrl);
            links.setMobileLink(authUrl);
            externalInstancesDetail.setLinks(links);
            externalInstancesDetail.setStartTime(curTime);
            externalInstancesDetail.setEndTime("0");
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setDisplayMethod(ApprovalDisplayMethodEnum.NORMAL.name());
            externalInstancesDetail.setUpdateMode(ApprovalUpdateModeEnum.REPLACE.name());
            List<ExternalInstancesDetail.I18NResource> i18nResources = new LinkedList<>();
            ExternalInstancesDetail.I18NResource i18NResource = new ExternalInstancesDetail.I18NResource();
            i18NResource.setLocale(ApprovalLocaleEnum.ZH_CN.getCode());
            i18NResource.setIsDefault(Boolean.TRUE);
            i18NResource.setTexts(texts);
            i18nResources.add(i18NResource);
            externalInstancesDetail.setI18nResources(i18nResources);
        } else {
            ExternalMessageInstanceVo externalMessageInstanceVo= convertMessageInfo(outerOaMessageBindEntities.getMessageInfo());
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime("0");
            externalInstancesDetail.setStatus(ApprovalStatusEnum.PENDING.name());
            links = externalInstancesDetail.getLinks();
        }

        List<ExternalInstancesDetail.Task> tasks = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(externalInstancesDetail.getTaskList())) {
            tasks.addAll(externalInstancesDetail.getTaskList());
        }

        Map<String, ExternalInstancesDetail.Task> taskMap = CollectionUtils
                .isEmpty(externalInstancesDetail.getTaskList())
                        ? new HashMap<>()
                        : externalInstancesDetail.getTaskList().stream()
                                .collect(Collectors.toMap(ExternalInstancesDetail.Task::getOpenId, Function.identity(),
                                        (existing, replacement) -> existing));
        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            // 未避免重复新增人员事件，这里做去重处理，不用取最新结果
            // 有可能当前同一个待办，一个人处理完成了，下个节点还是这个人，也需要重新推送
            if (taskMap.containsKey(employeeBindEntity.getOutEmpId())) {
                // 状态不是为待审批的需要重新推送
                if (!taskMap.get(employeeBindEntity.getOutEmpId()).getStatus()
                        .equals(ApprovalTaskStatusEnum.PENDING.name())) {
                    tasks.remove(taskMap.get(employeeBindEntity.getOutEmpId()));
                } else {
                    // 待审批的不需要更新
                    continue;
                }
            }

            ExternalInstancesDetail.Task task = new ExternalInstancesDetail.Task();
            task.setTaskId(createTodoArg.getSourceId() + employeeBindEntity.getOutEmpId());
            task.setOpenId(employeeBindEntity.getOutEmpId());
            // json序列化的时候，有相同的对象，被引用了，再次直接使用的话，只会显示引用地址
            // :{
            // "$ref":"$.links"
            // },
            ExternalInstancesDetail.Links employeeLinks = new ExternalInstancesDetail.Links();
            employeeLinks.setPcLink(links.getPcLink());
            employeeLinks.setMobileLink(links.getMobileLink());
            task.setLinks(employeeLinks);
            task.setStatus(ApprovalTaskStatusEnum.PENDING.name());
            task.setCreateTime(curTime);
            task.setEndTime("0");
            task.setUpdateTime(curTime);
            task.setDisplayMethod(ApprovalDisplayMethodEnum.NORMAL.name());
            tasks.add(task);
        }
        externalInstancesDetail.setTaskList(tasks);
        Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService
                .syncExternalApprovals(enterpriseBindEntity.getOutEa(), appId, externalInstancesDetail);
        LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,instancesDetailResultResult={}",
                instancesDetailResultResult);
        if (instancesDetailResultResult.isSuccess()) {
            // 保存入库
            if (ObjectUtils.isEmpty(outerOaMessageBindEntities)) {
//                ExternalTodoInstanceEntity insertEntity = ExternalTodoInstanceEntity.builder()
//                        .channel(ChannelEnum.feishu).fsEa(fsEa).outEa(enterpriseBindEntity.getOutEa())
//                        .sourceId(createTodoArg.getSourceId()).taskId(createTodoArg.getExtraDataMap().get("taskId"))
//                        .instanceId(outEa + appId + createTodoArg.getSourceId())
//                        .fsUserId(String.valueOf(createTodoArg.getSenderId()))
//                        .workflowInstanceId(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
//                        .objectApiName(createTodoArg.getExtraDataMap().get("objectApiName"))
//                        .objectId(createTodoArg.getExtraDataMap().get("objectId"))
//                        .todoDetail(JSON.toJSONString(instancesDetailResultResult.getData().getData()))
//                        .status(ExternalTodoStatusEnum.PENDING.getStatus()).build();
                ExternalMessageInstanceVo externalMessageInstanceVo = ExternalMessageInstanceVo.builder()
                        .taskId(createTodoArg.getExtraDataMap().get("taskId"))
                        .workflowInstanceId(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                        .objectId(createTodoArg.getExtraDataMap().get("objectId"))
                        .sourceId(createTodoArg.getSourceId())
                        .todoDetail(instancesDetailResultResult.getData().getData())
                        .objectApiName(createTodoArg.getExtraDataMap().get("objectApiName")).build();
                String outEmpId=ObjectUtils.isNotEmpty(todoCreateEmployee)?todoCreateEmployee.getOutEmpId():"1000";
                OuterOaMessageBindEntity outerOaMessageBindEntity = OuterOaMessageBindEntity.builder()
                        .channel(channelEnum).dcId(dcId).fsEa(fsEa).outEa(enterpriseBindEntity.getOutEa())
                        .appId(appId).templateId(outerOaMessageTemplateEntity.getTemplateId())
                        .outUserId(outEmpId)
                        .sourceId(createTodoArg.getSourceId())
                        .createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                        .eventType(OuterOaMessageBindEventTypeEnum.commonMsg)
                        .id(IdGenerator.get()).taskId(outEa + appId + createTodoArg.getSourceId())
                        .status(OuterOaMessageBindStatusEnum.pending)
                        .messageInfo(JSON.toJSONString(externalMessageInstanceVo))
                        .messageType(OuterOaMessageBindMsgTypeEnum.todo).build();
                Integer count=outerOaMessageBindManager.batchUpsertInfos(Lists.newArrayList(outerOaMessageBindEntity));
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,insert,count={}", count);
            } else {
                ExternalMessageInstanceVo externalTodoInstanceEntity = JSONObject
                        .parseObject(outerOaMessageBindEntities.getMessageInfo(), ExternalMessageInstanceVo.class);
                externalTodoInstanceEntity.setTodoDetail(instancesDetailResultResult.getData().getData());
                outerOaMessageBindEntities
                        .setMessageInfo(JSON.toJSONString(externalTodoInstanceEntity));
                outerOaMessageBindEntities.setStatus(OuterOaMessageBindStatusEnum.pending);
                Integer count = outerOaMessageBindManager.updateById(outerOaMessageBindEntities);
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,update,count={}", count);
            }
        } else {
            // 新增失败
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(),
                    instancesDetailResultResult.getMsg());
        }
        return Result.newSuccess();
    }

    public Result<Void> createExternalTodoTask(CreateTodoPushArg createTodoPushArg,
            List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        String msgType = createTodoPushArg.getMsgType();
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String dcId = enterpriseBindEntity.getId();
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId
                : enterpriseBindEntity.getAppId();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        String sourceId = createTodoArg.getSourceId();

        // 已经同步过了，但是会有这么一个问题，就是之前删除了或者是拒绝了的卡片，现在重新推送了待办，这个需要重新推送一个新的待办，因为这个也是不同的外部id，所以会有这种情况，一个sourceId下，同一个fsUserId会有多条待办记录，这些待办详情都是一样的，除了状态
        // 创建待办
        ExternalApprovalsTaskDetail externalApprovalsTaskDetail = new ExternalApprovalsTaskDetail();
        List<ExternalApprovalsTaskDetail.I18nResource> i18nResources = new LinkedList<>();
        ExternalApprovalsTaskDetail.I18nResource i18nResource = new ExternalApprovalsTaskDetail.I18nResource();
        i18nResource.setLocale(ApprovalLocaleEnum.ZH_CN.getCode());
        i18nResource.setDefault(Boolean.TRUE);
        Map<String, String> texts = new HashMap<>();

        externalApprovalsTaskDetail.setTemplateId("1008");
        externalApprovalsTaskDetail.setApprovalName("@i18n@1");
        texts.put("@i18n@1", createTodoArg.getContent());
        externalApprovalsTaskDetail.setNote("@i18n@2");
        texts.put("@i18n@2", "纷享销客crm"); // ignorei18n

        ExternalApprovalsTaskDetail.Content content = new ExternalApprovalsTaskDetail.Content();
        List<ExternalApprovalsTaskDetail.Summary> summaries = new LinkedList<>();
        int j = 3;
        if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
            for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                ExternalApprovalsTaskDetail.Summary summary = new ExternalApprovalsTaskDetail.Summary();
                summary.setSummary("@i18n@" + j);
                summaries.add(summary);
                texts.put("@i18n@" + j,
                        createTodoArg.getForm().get(i).getKey() + "：" + createTodoArg.getForm().get(i).getValue());
                j += 1;
                // code='60001', msg='too much Summaries error', traceMsg='null'
                if (summaries.size() >= 5) {
                    break;
                }
            }
        } else {
            ExternalApprovalsTaskDetail.Summary summary = new ExternalApprovalsTaskDetail.Summary();
            summary.setSummary("@i18n@" + j);
            summaries.add(summary);
            texts.put("@i18n@" + j, createTodoArg.getContent());
            j += 1;
        }
        content.setSummaries(summaries);

        List<ExternalApprovalsTaskDetail.Action> actions = new LinkedList<>();
        ExternalApprovalsTaskDetail.Action action = new ExternalApprovalsTaskDetail.Action();
        action.setActionName("DETAIL");
        String todoUrl = feishuApprovalUrlGenerator(createTodoPushArg);
        if (StringUtils.isEmpty(todoUrl)) {
            // 暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
            LogUtils.info("ExternalTodoManager.createTodo,param is null,createTodoArg={}.", createTodoArg);
            OuterOaMessageBindParams outerOaMessageBindParams = OuterOaMessageBindParams.builder()
                    .channel(channelEnum).fsEa(fsEa).outEa(outEa).sourceId(createTodoArg.getSourceId()).messageType(OuterOaMessageBindMsgTypeEnum.bot).build();
            OuterOaMessageBindEntity outerOaMessageBindEntities = outerOaMessageBindManager
                    .getEntity(outerOaMessageBindParams);
            if (ObjectUtils.isEmpty(outerOaMessageBindEntities)) {
                for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                    OuterOaMessageBindEntity oaMessageBindEntity = OuterOaMessageBindEntity.builder()
                            .channel(channelEnum).dcId(dcId).fsEa(fsEa).outEa(outEa).appId(appId)
                            .eventType(OuterOaMessageBindEventTypeEnum.commonMsg)
                            .messageType(OuterOaMessageBindMsgTypeEnum.bot).sourceId(sourceId).taskId(sourceId)
                            .outUserId(employeeBindEntity.getOutEmpId()).status(OuterOaMessageBindStatusEnum.IGNORE)
                            .build();
                    Integer insert = outerOaMessageBindManager.insert(oaMessageBindEntity);
                    LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,insert={}", insert);
                }
            }
            return Result.newSuccess();
        }

        byte[] todoUrlBytes = todoUrl.getBytes();
        String param = new String(Base64.encodeBase64(todoUrlBytes));
        String param2 = "";

        ExternalTodoMsgModel externalTodoMsgModel = new ExternalTodoMsgModel();
        externalTodoMsgModel.setFsEa(fsEa);
        externalTodoMsgModel.setOutEa(outEa);
        externalTodoMsgModel.setTaskId(createTodoArg.getExtraDataMap().get("taskId"));
        externalTodoMsgModel.setFsAppId(createTodoArg.getAppId());
        externalTodoMsgModel.setMsgType(msgType);
        externalTodoMsgModel.setUpstreamEa(upstreamEa);
        if (msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
            param2 = new String(Base64.encodeBase64(new Gson().toJson(externalTodoMsgModel).getBytes()));
        }

        String authUrl = ConfigCenter.FEISHU_AUTHEN_URL.replace("{app_id}", appId).replace("{state}", appId)
                .replace("{redirect_uri}", URLEncoder
                        .encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&param2=" + param2 + "&fsEa=" + fsEa));

        action.setUrl(authUrl);
        action.setAndroidUrl(authUrl);
        action.setIosUrl(authUrl);
        action.setPcUrl(ConfigCenter.FEISHU_WEB_URL.replace("{url}", URLEncoder.encode(authUrl)).replace("{mode}",
                "sidebar-semi"));
        actions.add(action);
        externalApprovalsTaskDetail.setActions(actions);

        List<ExternalApprovalsTaskDetail.ActionConfig> actionConfigs = new LinkedList<>();
        ExternalApprovalsTaskDetail.ActionConfig approveConfig = new ExternalApprovalsTaskDetail.ActionConfig();
        approveConfig.setActionType("APPROVE");
        approveConfig.setNeedReason(Boolean.TRUE);
        approveConfig.setReasonRequired(Boolean.FALSE);
        approveConfig.setNeedAttachment(Boolean.FALSE);
        actionConfigs.add(approveConfig);
        ExternalApprovalsTaskDetail.ActionConfig rejectConfig = new ExternalApprovalsTaskDetail.ActionConfig();
        rejectConfig.setActionType("REJECT");
        rejectConfig.setNeedReason(Boolean.TRUE);
        rejectConfig.setReasonRequired(Boolean.FALSE);
        rejectConfig.setNeedAttachment(Boolean.FALSE);
        actionConfigs.add(rejectConfig);
        externalApprovalsTaskDetail.setActionConfigs(actionConfigs);

        ExternalApprovalsTaskDetail.ActionCallback actionCallback = new ExternalApprovalsTaskDetail.ActionCallback();
        actionCallback.setActionCallbackUrl(ConfigCenter.crm_domain + ConfigCenter.APPROVAL_ACTION_CALLBACK_URL);
        actionCallback.setActionCallbackKey(ConfigCenter.APPROVAL_ACTION_CALLBACK_KEY);
        actionCallback.setActionCallbackToken(ConfigCenter.APPROVAL_ACTION_CALLBACK_TOKEN);
        externalApprovalsTaskDetail.setActionCallback(actionCallback);

        // 互联待办不用标注谁发起的，因为通常是上游的人推送过去的
        if (msgType.equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
            // 兼容多企业的情况
            String applicantId = createTodoArg.getExtraDataMap().get("applicantId");
            // 通过applicantId 找到外部outUserId
            OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder()
                    .fsEmpId(applicantId)
                    .fsEa(fsEa)
                    .outEa(outEa)
                    .build();
            List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getEntities(outerOaEmployeeBindParams);
            if (ObjectUtils.isNotEmpty(entities)) {
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = entities.get(0);
                externalApprovalsTaskDetail.setTitleUserId(outerOaEmployeeBindEntity.getOutEmpId());
                externalApprovalsTaskDetail.setTitleUserIdType("open_id");
                content.setUserId(outerOaEmployeeBindEntity.getOutEmpId());
                content.setUserIdType("open_id");
            }
        }

        externalApprovalsTaskDetail.setContent(content);

        i18nResource.setTexts(texts);
        i18nResources.add(i18nResource);
        externalApprovalsTaskDetail.setI18nResources(i18nResources);

        OuterOaMessageBindParams outerOaMessageBindParams=OuterOaMessageBindParams.builder().fsEa(fsEa).outEa(outEa).sourceId(sourceId).status(OuterOaMessageBindStatusEnum.approved).build();
        List<OuterOaMessageBindEntity> oaMessageBindEntities = outerOaMessageBindManager.getEntities(outerOaMessageBindParams);
        LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,externalTodoTaskEntities={}", oaMessageBindEntities);
        List<String> externalTodoTaskUserIds = oaMessageBindEntities.stream()
                .map(OuterOaMessageBindEntity::getOutUserId).collect(Collectors.toList());
        StringBuilder msg = new StringBuilder();
        for (OuterOaEmployeeBindEntity entity : employeeBindEntities) {
            // 存在未办的，不用重复推送
            if (externalTodoTaskUserIds.contains(entity.getOutEmpId())) {
                continue;
            }

            externalApprovalsTaskDetail.setOpenId(entity.getOutEmpId());
            externalApprovalsTaskDetail.setUuid(String.valueOf(UUID.randomUUID()));

            // 操作上下文，回调的时候会把该参数回传
            externalTodoMsgModel.setFsUserId(entity.getFsEmpId());

            actionCallback.setActionContext(new Gson().toJson(externalTodoMsgModel));
            externalApprovalsTaskDetail.setActionCallback(actionCallback);

            Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService
                    .sendExternalApprovalTask(outEa, appId, externalApprovalsTaskDetail);
            LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,externalApprovalTaskResultResult={}",
                    externalApprovalTaskResultResult);
            if (externalApprovalTaskResultResult.isSuccess()) {
                // 保存入库
                OuterOaMessageBindEntity outerOaMessageBindEntity = OuterOaMessageBindEntity.builder()
                        .id(IdGenerator.get())
                        .appId(appId)
                        .eventType(OuterOaMessageBindEventTypeEnum.commonMsg)
                        .createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis())
                        .outUserId(entity.getOutEmpId())
                        .taskId(externalApprovalTaskResultResult.getData().getMessageId())
                        .channel(channelEnum).messageType(
                                OuterOaMessageBindMsgTypeEnum.bot).fsEa(fsEa).outEa(outEa).dcId(dcId).sourceId(sourceId).status(OuterOaMessageBindStatusEnum.pending).build();
                Integer insert = outerOaMessageBindManager.insert(outerOaMessageBindEntity);
                LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,insert={}", insert);
            } else {
                msg.append(entity.getOutEmpId()).append(":").append(externalApprovalTaskResultResult.getMsg())
                        .append("\n");
            }
        }

        if (ObjectUtils.isNotEmpty(msg)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), msg.toString());
        }
        return Result.newSuccess();
    }

    public Result<Void> dealExternalApprovalTodo(DealTodoArg arg, List<OuterOaEmployeeBindEntity> employeeBindEntities,
            OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        // 查库
        String appId = ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId()) ? enterpriseBindEntity.getAppId()
                : ConfigCenter.feishuCrmAppId;
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();
        OuterOaMessageBindParams outerOaMessageBindParams = OuterOaMessageBindParams.builder()
                .channel(channelEnum).fsEa(arg.getEa()).outEa(enterpriseBindEntity.getOutEa())
                .messageType(OuterOaMessageBindMsgTypeEnum.todo)
                .sourceId(arg.getSourceId()).build();

        OuterOaMessageBindEntity externalTodoInstanceEntity = outerOaMessageBindManager
                .getEntity(outerOaMessageBindParams);
        // 顺序消费会有绑定记录，除了失败
        if (ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
            LogUtils.info("ExternalTodoManager.dealExternalApprovals,externalTodoInstanceEntity is null,arg={}", arg);
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }

        if (externalTodoInstanceEntity.getStatus() != OuterOaMessageBindStatusEnum.pending) {
            return Result.newSuccess();
        }

        // 组装数据
        String curTime = System.currentTimeMillis() + "";
        ExternalMessageInstanceVo externalMessageInstanceVo=convertMessageInfo(externalTodoInstanceEntity.getMessageInfo());
        ExternalInstancesDetail externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        externalInstancesDetail.setUpdateTime(curTime);

        Gson gson = new Gson();
        Type opinionsType = new TypeToken<List<DealTodoOpinion>>() {}.getType();
        List<DealTodoOpinion> opinions = gson.fromJson(gson.toJson(arg.getExtDataMap().get("opinions")), opinionsType);
        Map<String, DealTodoOpinion> opinionMap = opinions.stream()
                .filter(opinion -> opinion.getReply_user() != null && !opinion.getReply_user().isEmpty())
                .filter(opinion -> opinion.getAction_type() != DealTodoOpinion.ActionType.addTag && opinion.getAction_type() != DealTodoOpinion.ActionType.tagAfter)
                .collect(Collectors.toMap(
                        opinion -> opinion.getReply_user().get(0),
                        opinion -> opinion,
                        (existing, replacement) -> {
                            // 比较 reply_time，取更大的记录
                            return existing.getReply_time() > replacement.getReply_time() ? existing : replacement;
                        }
                ));
        LogUtils.info("ExternalTodoManager.dealExternalApprovals,opinionMap={}", opinionMap);

        Map<String, String> employeeMap = employeeBindEntities.stream()
                .collect(Collectors.toMap(OuterOaEmployeeBindEntity::getOutEmpId, OuterOaEmployeeBindEntity::getFsEmpId, (v1, v2) -> v1));
        for(ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
            if(employeeMap.containsKey(task.getOpenId())) {
                //查看当前处理员工的处理状态
                DealTodoOpinion.ActionType actionType = DealTodoOpinion.ActionType.agree;
                if (!opinionMap.containsKey(employeeMap.get(task.getOpenId()))) {
                    //理论上不会出现，先默认为同意，日志记录
                    LogUtils.info("ExternalTodoManager.dealExternalApprovals,actionType is null,employeeMap={}", employeeMap);
                } else {
                    actionType = opinionMap.get(employeeMap.get(task.getOpenId())).getAction_type();
                }

                //需要设置完成
                //测试只有同意、拒绝、撤回的时候才会发送dealTodo
                if (actionType == DealTodoOpinion.ActionType.agree) {
                    task.setStatus(ApprovalTaskStatusEnum.APPROVED.name());
                } else {
                    task.setStatus(ApprovalTaskStatusEnum.REJECTED.name());
                }
                task.setEndTime(curTime);
                task.setUpdateTime(curTime);
            }
        }

        OuterOaMessageBindStatusEnum todoStatus;

        DealTodoStateEnum state = Optional.ofNullable(arg.getExtDataMap().get("state"))
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .map(DealTodoStateEnum::valueOf) // 将 String 转换为枚举
                .orElse(null); // 或者提供默认值

        if (state == null || state == DealTodoStateEnum.in_progress || state == DealTodoStateEnum.tag_waiting) {
            //未完成
            externalInstancesDetail.setUpdateTime(curTime);
            todoStatus = OuterOaMessageBindStatusEnum.pending;
        } else if (state == DealTodoStateEnum.cancel) {
            //撤回
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.CANCELED.name());
            todoStatus = OuterOaMessageBindStatusEnum.cancel;
        } else if (state == DealTodoStateEnum.error) {
            //错误，直接删除代办
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.DELETED.name());
            todoStatus = OuterOaMessageBindStatusEnum.deleted;
        }else if (state == DealTodoStateEnum.reject) {
            //拒绝代办
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.REJECTED.name());
            todoStatus = OuterOaMessageBindStatusEnum.rejected;
        } else {
            //同意代办
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
            todoStatus = OuterOaMessageBindStatusEnum.approved;
        }

        Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), appId, externalInstancesDetail);
        LogUtils.info("ExternalTodoManager.dealExternalApprovals,instancesDetailResultResult={}", instancesDetailResultResult);
        if(instancesDetailResultResult.isSuccess()) {
            //保存入库
            externalMessageInstanceVo.setTodoDetail(instancesDetailResultResult.getData().getData());
            externalTodoInstanceEntity.setMessageInfo(JSON.toJSONString(externalMessageInstanceVo));
            externalTodoInstanceEntity.setStatus(todoStatus);
            Integer count = outerOaMessageBindManager.updateById(externalTodoInstanceEntity);
            LogUtils.info("ExternalTodoManager.dealExternalApprovals,update,count={}", count);
        } else {
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR.getCode(),
                    instancesDetailResultResult.getMsg());
        }

        return Result.newSuccess();
    }

    public Result<Void> dealExternalApprovalTodoTask(DealTodoArg arg,
            List<OuterOaEmployeeBindEntity> employeeBindEntities, OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        String fsEa = arg.getEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String sourceId = arg.getSourceId();
        String appId = ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId()) ? enterpriseBindEntity.getAppId()
                : ConfigCenter.feishuCrmAppId;
        // 根据sourceId查找bot审批

        OuterOaMessageBindParams outerOaMessageBindParams = OuterOaMessageBindParams.builder()
                .channel(ChannelEnum.feishu).fsEa(fsEa).outEa(outEa).sourceId(sourceId).messageType(OuterOaMessageBindMsgTypeEnum.bot).build();
        List<OuterOaMessageBindEntity> externalTodoTaskEntities = outerOaMessageBindManager
                .getEntities(outerOaMessageBindParams);
        if (CollectionUtils.isEmpty(externalTodoTaskEntities)) {
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }
        StringBuilder msg = new StringBuilder();
        List<OuterOaMessageBindEntity> filteredExternalTodoTaskEntities = new LinkedList<>();

        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            boolean isEmpRetry = Boolean.TRUE;
            for (OuterOaMessageBindEntity externalTodoTaskEntity : externalTodoTaskEntities) {
                if (employeeBindEntity.getOutEmpId().equals(externalTodoTaskEntity.getOutUserId())) {
                    isEmpRetry = Boolean.FALSE;
                    if (externalTodoTaskEntity.getStatus() != OuterOaMessageBindStatusEnum.pending) {
                        // 已处理过
                        continue;
                    }
                    filteredExternalTodoTaskEntities.add(externalTodoTaskEntity);
                }
            }

            if (isEmpRetry) {
                msg.append(employeeBindEntity.getOutEmpId()).append(":")
                        .append(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getMsg()).append("\n");
            }
        }

        if (CollectionUtils.isEmpty(filteredExternalTodoTaskEntities)) {
            return Result.newSuccess();
        }

        Gson gson = new Gson();
        Type opinionsType = new TypeToken<List<DealTodoOpinion>>() {}.getType();
        List<DealTodoOpinion> opinions = gson.fromJson(gson.toJson(arg.getExtDataMap().get("opinions")), opinionsType);
        Map<String, DealTodoOpinion> opinionMap = opinions.stream()
                .filter(opinion -> opinion.getReply_user() != null && !opinion.getReply_user().isEmpty())
                .filter(opinion -> opinion.getAction_type() != DealTodoOpinion.ActionType.addTag && opinion.getAction_type() != DealTodoOpinion.ActionType.tagAfter)
                .collect(Collectors.toMap(
                        opinion -> opinion.getReply_user().get(0),
                        opinion -> opinion,
                        (existing, replacement) -> {
                            // 比较 reply_time，取更大的记录
                            return existing.getReply_time() > replacement.getReply_time() ? existing : replacement;
                        }
                ));
        LogUtils.info("ExternalTodoManager.dealExternalApprovals,opinionMap={}", opinionMap);
        Map<String, String> employeeMap = employeeBindEntities.stream()
                .collect(Collectors.toMap(OuterOaEmployeeBindEntity::getOutEmpId, OuterOaEmployeeBindEntity::getFsEmpId, (v1, v2) -> v1));

        for(OuterOaMessageBindEntity externalTodoTaskEntity : filteredExternalTodoTaskEntities) {
            ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
            externalApprovalsTaskUpdateDetail.setMessageId(externalTodoTaskEntity.getTaskId());
            if (!opinionMap.containsKey(employeeMap.get(externalTodoTaskEntity.getOutUserId()))) {
                continue;
            }

            //查看当前处理员工的处理状态
            OuterOaMessageBindStatusEnum todoStatus;
            DealTodoOpinion.ActionType actionType = opinionMap.get(employeeMap.get(externalTodoTaskEntity.getOutUserId())).getAction_type();
            if (actionType == DealTodoOpinion.ActionType.agree) {
                externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.APPROVED.name());
                todoStatus = OuterOaMessageBindStatusEnum.approved;
            } else if (actionType == DealTodoOpinion.ActionType.cancel) {
                externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.CANCELLED.name());
                todoStatus = OuterOaMessageBindStatusEnum.cancel;
            } else if (actionType == DealTodoOpinion.ActionType.reject) {
                externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.REJECTED.name());
                todoStatus = OuterOaMessageBindStatusEnum.rejected;
            } else {
                externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.PROCESSED.name());
                todoStatus = OuterOaMessageBindStatusEnum.processed;
            }
            Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(outEa, appId, externalApprovalsTaskUpdateDetail);
            if(externalApprovalTaskResultResult.isSuccess()) {
                externalTodoTaskEntity.setStatus(todoStatus);
//                Integer update = externalTodoTaskManager.update(externalTodoTaskEntity);
                Integer update = outerOaMessageBindManager.updateById(externalTodoTaskEntity);
                LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,update={}",update);
            } else {
                msg.append(externalTodoTaskEntity.getOutUserId()).append(":")
                        .append(externalApprovalTaskResultResult.getMsg()).append("\n");
            }
        }

        if (ObjectUtils.isNotEmpty(msg)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), msg.toString());
        }
        return Result.newSuccess();
    }

    public Result<Void> deleteExternalApprovals(DeleteTodoArg var1,
            List<OuterOaEmployeeBindEntity> employeeBindEntities, OuterOaEnterpriseBindEntity enterpriseBindEntity) {

        // 查库
        String appId = ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId()) ? enterpriseBindEntity.getAppId()
                : ConfigCenter.feishuCrmAppId;

        OuterOaMessageBindParams outerOaMessageBindParams = OuterOaMessageBindParams.builder()
                .channel(ChannelEnum.feishu).fsEa(var1.getEa()).outEa(enterpriseBindEntity.getOutEa())
                .messageType(OuterOaMessageBindMsgTypeEnum.todo)
                .sourceId(var1.getSourceId()).build();
        OuterOaMessageBindEntity externalTodoInstanceEntity = outerOaMessageBindManager.getEntity(outerOaMessageBindParams);
        if (ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
            LogUtils.info("ExternalTodoManager.deleteExternalApprovals,externalTodoInstanceEntity is null,arg={}",
                    var1);
            return Result.newError(ResultCodeEnum.DELETE_APPROVAL_TODO_ERROR);
        }

//        if(externalTodoInstanceEntity.getStatus() != 0) {
//            return Result.newSuccess();
//        }

        // 组装数据
        String curTime = System.currentTimeMillis() + "";
        ExternalMessageInstanceVo externalMessageInstanceVo=convertMessageInfo(externalTodoInstanceEntity.getMessageInfo());
        LogUtils.info("exterl message :{},",externalTodoInstanceEntity.getMessageInfo());
        ExternalInstancesDetail externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        //移除不需要的人员
        externalInstancesDetail.setUpdateTime(curTime);
        Map<String, String> employeeMap = employeeBindEntities.stream().collect(Collectors
                .toMap(OuterOaEmployeeBindEntity::getOutEmpId, OuterOaEmployeeBindEntity::getFsEmpId, (v1, v2) -> v1));
        List<ExternalInstancesDetail.Task> deleteInstancesDetail = new LinkedList<>(
                externalInstancesDetail.getTaskList());
        for (ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
            if (employeeMap.containsKey(task.getOpenId())) {
                deleteInstancesDetail.remove(task);
            }
        }

        externalInstancesDetail.setTaskList(deleteInstancesDetail);
        LogUtils.info("ExternalTodoManager.deleteExternalApprovals,deleteInstancesDetail.size={}",
                deleteInstancesDetail.size());
        if (CollectionUtils.isEmpty(deleteInstancesDetail)) {
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.DELETED.name());
        }

        Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService
                .syncExternalApprovals(enterpriseBindEntity.getOutEa(), appId, externalInstancesDetail);
        LogUtils.info("ExternalTodoManager.deleteExternalApprovals,instancesDetailResultResult={}",
                instancesDetailResultResult);
        if (instancesDetailResultResult.isSuccess()) {
            // 保存入库
            externalMessageInstanceVo.setTodoDetail(instancesDetailResultResult.getData().getData());
            if (CollectionUtils.isEmpty(deleteInstancesDetail)) {
                externalTodoInstanceEntity.setStatus(OuterOaMessageBindStatusEnum.deleted);
            }
            externalTodoInstanceEntity.setMessageInfo(JSON.toJSONString(externalMessageInstanceVo));
            externalTodoInstanceEntity.setUpdateTime(System.currentTimeMillis());
            Integer count = outerOaMessageBindManager.updateById(externalTodoInstanceEntity);
            LogUtils.info("ExternalTodoManager.deleteExternalApprovals,update,count={}", count);
        } else {
            return Result.newError(ResultCodeEnum.DELETE_APPROVAL_TODO_ERROR.getCode(),
                    instancesDetailResultResult.getMsg());
        }

        return Result.newSuccess();
    }

    public Result<Void> deleteExternalApprovalTodoTask(DeleteTodoArg arg,
            List<OuterOaEmployeeBindEntity> employeeBindEntities, OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        String fsEa = arg.getEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String sourceId = arg.getSourceId();
        ChannelEnum channelEnum = enterpriseBindEntity.getChannel();
        String appId = ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId()) ? enterpriseBindEntity.getAppId()
                : ConfigCenter.feishuCrmAppId;
        // 根据sourceId查找bot审批

        OuterOaMessageBindParams outerOaMessageBindParams=OuterOaMessageBindParams.builder()
                .channel(channelEnum).fsEa(fsEa).outEa(outEa)
                .sourceId(sourceId).messageType(OuterOaMessageBindMsgTypeEnum.bot).build();
        List<OuterOaMessageBindEntity> externalTodoTaskEntities = outerOaMessageBindManager.getEntities(outerOaMessageBindParams);
        if (CollectionUtils.isEmpty(externalTodoTaskEntities)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR);
        }
        StringBuilder msg = new StringBuilder();
        List<OuterOaMessageBindEntity> filteredExternalTodoTaskEntities = new LinkedList<>();

        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            boolean isEmpRetry = Boolean.TRUE;
            for (OuterOaMessageBindEntity externalTodoTaskEntity : externalTodoTaskEntities) {
                if (employeeBindEntity.getOutEmpId().equals(externalTodoTaskEntity.getOutUserId())) {
                    isEmpRetry = Boolean.FALSE;
                    if (externalTodoTaskEntity.getStatus() != OuterOaMessageBindStatusEnum.pending) {
                        // 已处理过
                        continue;
                    }
                    filteredExternalTodoTaskEntities.add(externalTodoTaskEntity);
                }
            }

            if (isEmpRetry) {
                msg.append(employeeBindEntity.getOutEmpId()).append(":")
                        .append(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getMsg()).append("\n");
            }
        }

        if (CollectionUtils.isEmpty(filteredExternalTodoTaskEntities)) {
            return Result.newSuccess();
        }

        for (OuterOaMessageBindEntity externalTodoTaskEntity : filteredExternalTodoTaskEntities) {
            ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
            externalApprovalsTaskUpdateDetail.setMessageId(externalTodoTaskEntity.getTaskId());
            externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.DELETED.name());
            Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService
                    .updateExternalApprovalTask(outEa, appId, externalApprovalsTaskUpdateDetail);
            if (externalApprovalTaskResultResult.isSuccess()) {
                externalTodoTaskEntity.setStatus(OuterOaMessageBindStatusEnum.deleted);
                Integer update = outerOaMessageBindManager.updateById(externalTodoTaskEntity);

                LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,update={}", update);
            } else {
                msg.append(externalTodoTaskEntity.getOutUserId()).append(":")
                        .append(externalApprovalTaskResultResult.getMsg()).append("\n");
            }
        }

        if (ObjectUtils.isNotEmpty(msg)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), msg.toString());
        }
        return Result.newSuccess();
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,result={}", result);
        return result;
    }

    private String feishuApprovalUrlGenerator(CreateTodoPushArg createTodoPushArg) {
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        String msgType = createTodoPushArg.getMsgType();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        String todoUrl = null;
        if (msgType.equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
            // 正常的
            if (ObjectUtils.isNotEmpty(createTodoArg.getExtraDataMap())
                    && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                    && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))
                    && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("objectId"))) {
                String domain = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain();
                todoUrl = domain + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_APPROVAL_BRIDGE_URL
                        .replace("{workflowInstanceId}", createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                        .replace("{objectApiName}", createTodoArg.getExtraDataMap().get("objectApiName"))
                        .replace("{objectId}", createTodoArg.getExtraDataMap().get("objectId"))
                        .replace("{ea}", createTodoArg.getEa()) + "&forceRedirectH5=true";
            }
        } else {
            // 互联的
            if (ObjectUtils.isEmpty(createTodoArg.getExtraDataMap())
                    || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                    || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectId"))
                    || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))) {
                // 暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,createTodoArg={}.",
                        createTodoArg);
                return null;
            }
            todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain()
                    + ConfigCenter.CRM_INTERCONNECT_APPROVAL_BRIDGE_URL.replace("{fsAppId}", createTodoArg.getAppId())
                            .replace("{upstreamEa}", upstreamEa)
                            .replace("{objectId}", createTodoArg.getExtraDataMap().get("objectId"))
                            .replace("{objectApiName}", createTodoArg.getExtraDataMap().get("objectApiName"))
                            .replace("{workflowInstanceId}", createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                            .replace("{forceRedirectH5}", String.valueOf(Boolean.TRUE));
        }
        return todoUrl;
    }
    //兼容之前的数据
    private ExternalMessageInstanceVo convertMessageInfo(String messageInfo){
        ExternalMessageInstanceVo externalMessageInstanceVo = JSONObject.parseObject(messageInfo, new TypeReference<ExternalMessageInstanceVo>() {
        });
        //兼容之前的数据
        ExternalInstancesDetail externalInstancesDetail=null;
        if(ObjectUtils.isEmpty(externalMessageInstanceVo)||ObjectUtils.isEmpty(externalMessageInstanceVo.getTodoDetail())){
            externalMessageInstanceVo=new ExternalMessageInstanceVo();
            externalInstancesDetail = JSONObject.parseObject(messageInfo, new TypeReference<ExternalInstancesDetail>() {
            });
            externalMessageInstanceVo.setTodoDetail(externalInstancesDetail);

        }else{
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        }
        return externalMessageInstanceVo;
    }

    public static void main(String[] args) {
        String data="{\"objectApiName\":\"ContactObj\",\"objectId\":\"6806677a2fd74e0001a28062\",\"sourceId\":\"6806677aad61ac58584f4b54\",\"taskId\":\"6806677aad61ac58584f4b54\",\"todoDetail\":{\"approval_code\":\"2F56525B-0ACB-44A2-A6CD-A4F81230F60E\",\"display_method\":\"NORMAL\",\"end_time\":\"1745250210799\",\"form\":[{\"name\":\"@i18n@2\",\"value\":\"@i18n@3\"},{\"name\":\"@i18n@4\",\"value\":\"@i18n@5\"},{\"name\":\"@i18n@6\",\"value\":\"@i18n@7\"},{\"name\":\"@i18n@8\",\"value\":\"@i18n@9\"},{\"name\":\"@i18n@10\",\"value\":\"@i18n@11\"},{\"name\":\"@i18n@12\",\"value\":\"@i18n@13\"}],\"i18n_resources\":[{\"is_default\":true,\"locale\":\"zh-CN\",\"texts\":[{\"key\":\"@i18n@2\",\"value\":\"流程主题\"},{\"key\":\"@i18n@3\",\"value\":\"联系人审批(2025-04-21 23:42)\"},{\"key\":\"@i18n@4\",\"value\":\"姓名\"},{\"key\":\"@i18n@5\",\"value\":\"多人审批联系人01\"},{\"key\":\"@i18n@6\",\"value\":\"客户名称\"},{\"key\":\"@i18n@7\",\"value\":\"--\"},{\"key\":\"@i18n@8\",\"value\":\"手机1\"},{\"key\":\"@i18n@9\",\"value\":\"--\"},{\"key\":\"@i18n@10\",\"value\":\"职务\"},{\"key\":\"@i18n@11\",\"value\":\"--\"},{\"key\":\"@i18n@12\",\"value\":\"负责人\"},{\"key\":\"@i18n@13\",\"value\":\"杨贤杰Summer\"},{\"key\":\"@i18n@1\",\"value\":\"待处理的CRM审批流程\"}]}],\"instance_id\":\"106114c88acad75dcli_a77e25e1acf8d00e6806677aad61ac58584f4b54\",\"links\":{\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fwww.fxiaoke.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vaGNybS9mZWlzaHUvZnVuY3Rpb24vYXBwcm92YWwtYnJpZGdlP3dvcmtmbG93SW5zdGFuY2VJZD02ODA2Njc3YWVmMTg3MzZiN2I5MjQzYzcmb2JqZWN0QXBpTmFtZT1Db250YWN0T2JqJm9iamVjdElkPTY4MDY2NzdhMmZkNzRlMDAwMWEyODA2MiZlYT1ma3Rlc3Q3NTc4JmZvcmNlUmVkaXJlY3RINT10cnVl%26param2%3D%26fsEa%3Dfktest7578&app_id=cli_a77e25e1acf8d00e&state=cli_a77e25e1acf8d00e\",\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fwww.fxiaoke.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vaGNybS9mZWlzaHUvZnVuY3Rpb24vYXBwcm92YWwtYnJpZGdlP3dvcmtmbG93SW5zdGFuY2VJZD02ODA2Njc3YWVmMTg3MzZiN2I5MjQzYzcmb2JqZWN0QXBpTmFtZT1Db250YWN0T2JqJm9iamVjdElkPTY4MDY2NzdhMmZkNzRlMDAwMWEyODA2MiZlYT1ma3Rlc3Q3NTc4JmZvcmNlUmVkaXJlY3RINT10cnVl%26param2%3D%26fsEa%3Dfktest7578&app_id=cli_a77e25e1acf8d00e&state=cli_a77e25e1acf8d00e\"},\"start_time\":\"1745250171621\",\"status\":\"APPROVED\",\"task_list\":[{\"create_time\":\"1745250171621\",\"display_method\":\"NORMAL\",\"end_time\":\"1745250210799\",\"links\":{\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fwww.fxiaoke.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vaGNybS9mZWlzaHUvZnVuY3Rpb24vYXBwcm92YWwtYnJpZGdlP3dvcmtmbG93SW5zdGFuY2VJZD02ODA2Njc3YWVmMTg3MzZiN2I5MjQzYzcmb2JqZWN0QXBpTmFtZT1Db250YWN0T2JqJm9iamVjdElkPTY4MDY2NzdhMmZkNzRlMDAwMWEyODA2MiZlYT1ma3Rlc3Q3NTc4JmZvcmNlUmVkaXJlY3RINT10cnVl%26param2%3D%26fsEa%3Dfktest7578&app_id=cli_a77e25e1acf8d00e&state=cli_a77e25e1acf8d00e\",\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fwww.fxiaoke.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vaGNybS9mZWlzaHUvZnVuY3Rpb24vYXBwcm92YWwtYnJpZGdlP3dvcmtmbG93SW5zdGFuY2VJZD02ODA2Njc3YWVmMTg3MzZiN2I5MjQzYzcmb2JqZWN0QXBpTmFtZT1Db250YWN0T2JqJm9iamVjdElkPTY4MDY2NzdhMmZkNzRlMDAwMWEyODA2MiZlYT1ma3Rlc3Q3NTc4JmZvcmNlUmVkaXJlY3RINT10cnVl%26param2%3D%26fsEa%3Dfktest7578&app_id=cli_a77e25e1acf8d00e&state=cli_a77e25e1acf8d00e\"},\"open_id\":\"ou_6af60ccb1017d9db655c9173f14a2bfe\",\"status\":\"APPROVED\",\"task_id\":\"6806677aad61ac58584f4b54ou_6af60ccb1017d9db655c9173f14a2bfe\",\"update_time\":\"1745250210799\"},{\"create_time\":\"1745250171621\",\"display_method\":\"NORMAL\",\"end_time\":\"1745250210799\",\"links\":{\"mobile_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fwww.fxiaoke.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vaGNybS9mZWlzaHUvZnVuY3Rpb24vYXBwcm92YWwtYnJpZGdlP3dvcmtmbG93SW5zdGFuY2VJZD02ODA2Njc3YWVmMTg3MzZiN2I5MjQzYzcmb2JqZWN0QXBpTmFtZT1Db250YWN0T2JqJm9iamVjdElkPTY4MDY2NzdhMmZkNzRlMDAwMWEyODA2MiZlYT1ma3Rlc3Q3NTc4JmZvcmNlUmVkaXJlY3RINT10cnVl%26param2%3D%26fsEa%3Dfktest7578&app_id=cli_a77e25e1acf8d00e&state=cli_a77e25e1acf8d00e\",\"pc_link\":\"https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=https%3A%2F%2Fwww.fxiaoke.com%2Ferpdss%2Ffeishu%2Fexternal%2FloginAuth%3Fparam%3DaHR0cHM6Ly93d3cuZnhpYW9rZS5jb20vaGNybS9mZWlzaHUvZnVuY3Rpb24vYXBwcm92YWwtYnJpZGdlP3dvcmtmbG93SW5zdGFuY2VJZD02ODA2Njc3YWVmMTg3MzZiN2I5MjQzYzcmb2JqZWN0QXBpTmFtZT1Db250YWN0T2JqJm9iamVjdElkPTY4MDY2NzdhMmZkNzRlMDAwMWEyODA2MiZlYT1ma3Rlc3Q3NTc4JmZvcmNlUmVkaXJlY3RINT10cnVl%26param2%3D%26fsEa%3Dfktest7578&app_id=cli_a77e25e1acf8d00e&state=cli_a77e25e1acf8d00e\"},\"open_id\":\"ou_d068b827fc0bf15bc8843160086349e6\",\"status\":\"APPROVED\",\"task_id\":\"6806677aad61ac58584f4b54ou_d068b827fc0bf15bc8843160086349e6\",\"update_time\":\"1745250210799\"}],\"title\":\"@i18n@1\",\"update_mode\":\"REPLACE\",\"update_time\":\"1745250210799\"},\"workflowInstanceId\":\"6806677aef18736b7b9243c7\"}";
        ExternalMessageInstanceVo externalMessageInstanceVo = JSONObject.parseObject(data, new TypeReference<ExternalMessageInstanceVo>() {
        });
        //兼容之前的数据
        ExternalInstancesDetail externalInstancesDetail=null;
        if(ObjectUtils.isEmpty(externalMessageInstanceVo)||ObjectUtils.isEmpty(externalMessageInstanceVo.getTodoDetail())){
            externalMessageInstanceVo=new ExternalMessageInstanceVo();
            externalInstancesDetail = JSONObject.parseObject(data, new TypeReference<ExternalInstancesDetail>() {
            });
            externalMessageInstanceVo.setTodoDetail(externalInstancesDetail);

        }else{
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        }
        System.out.printf("ExternalMessageInstanceVo");
    }
}
