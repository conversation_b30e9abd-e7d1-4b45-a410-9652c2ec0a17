package com.facishare.open.feishu.web.template.outer.event.order;

import cn.hutool.core.thread.ThreadUtil;
import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OpenEnterpriseHandlerTemplate;
import com.facishare.open.feishu.sync.manager.OrderInfoManager;
import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.CrmOrderProductVo;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaOrderInfoManager;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.facishare.open.feishu.web.utils.RetryUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class FeishuOpenEnterpriseHandlerTemplate extends OpenEnterpriseHandlerTemplate {
    @Resource
    private OrderService orderService;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OrderInfoManager orderInfoManager;


    @Override
    public void saveOrder(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,context={}", context);
        FeishuOrderPaidEvent event = context.getData();
        Result<OuterOaOrderInfoEntity> result = orderService.saveOrder(event);
        log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,result={}", result);
        if (result.isSuccess()) {
            OuterOaOrderInfoEntity orderInfoEntity = result.getData();
            String appId = orderInfoEntity.getAppId();
            String outEa = orderInfoEntity.getPaidOutEa();
            //先延迟4秒，避免飞书企业事件延迟导致下面的代码获取不到飞书企业信息
            ThreadUtil.safeSleep(4000);
            QueryTenantInfoData tenantInfoData = orderService.getFeishuEnterpriseInfo(appId, outEa).getData();

            String outEn = outEa;//默认的逻辑
            String outEid = outEa;
            // 获取飞书企业信息(带重试)
//            Result<QueryTenantInfoData> queryTenantInfoDataResult = RetryUtil.doWithRetry(
//                    () -> orderService.getFeishuEnterpriseInfo(appId, outEa),
//                    10,  // 重试3次
//                    4000, // 每次间隔4秒
//                    "Get feishu enterprise info"
//            );
            if(tenantInfoData==null) {
                //如果获取不到飞书企业信息，再休眠4秒，之后再尝试获取
                ThreadUtil.safeSleep(4000);
                tenantInfoData = tenantInfoData = orderService.getFeishuEnterpriseInfo(appId, outEa).getData();
                log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,corpInfoEntity.2={}", tenantInfoData);
                //如果获取不到飞书企业信息，再休眠8秒，之后再尝试获取
                if (tenantInfoData == null) {
                    ThreadUtil.safeSleep(8000);
                    tenantInfoData = orderService.getFeishuEnterpriseInfo(appId, outEa).getData();
                    log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,corpInfoEntity.3={}", tenantInfoData);
                }
                //如果多次尝试仍获取不到飞书企业信息，走默认逻辑
                if (tenantInfoData == null) {
                    tenantInfoData = new QueryTenantInfoData();
                    QueryTenantInfoData.Tenant tenant = new QueryTenantInfoData.Tenant();
                    tenant.setTenantKey(outEa);
                    tenant.setName(outEa);
                    tenantInfoData.setTenant(tenant);
                }
            }
            // 处理企业信息结果
            if (ObjectUtils.isNotEmpty(tenantInfoData)) {
                outEn = tenantInfoData.getTenant().getName();
                outEid = tenantInfoData.getTenant().getDisplayId();
            }
            String fsEa = orderService.genFsEa(outEn).getData();
            log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,genFsEa,fsEa={}", fsEa);
            Result<UserData.User> appInstallerInfo = orderService.getAppInstallerInfo(appId, outEa);
            log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,appInstallerInfo={}",result);
            if(!appInstallerInfo.isSuccess()) {
                ThreadUtil.safeSleep(4000);
                appInstallerInfo = orderService.getAppInstallerInfo(appId, outEa);
                log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,appInstallerInfo.2={}",result);
                if(!appInstallerInfo.isSuccess()) {
                    ThreadUtil.safeSleep(4000);
                    appInstallerInfo = orderService.getAppInstallerInfo(appId, outEa);
                    log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,appInstallerInfo.3={}",result);
                }
            }
            if (!appInstallerInfo.isSuccess()) {
                context.setResult(TemplateResult.newError("获取不到飞书应用安装人员的信息")); //ignorei18n
                log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,getAppInstallerInfo failed,context={}", context);
                return;
            }
            CreateCustomerAndUpdateMappingArg customer = new CreateCustomerAndUpdateMappingArg();
            customer.setFsEa(fsEa);
            customer.setOutEid(outEid);
            customer.setOutEa(outEa);
            customer.setEnterpriseName(outEn);
            customer.setInstallerUserId(appInstallerInfo.getData().getOpenId());
            customer.setInstallerName(appInstallerInfo.getData().getName());
            customer.setAppId(appId);
            Map<String,Object> contextMap = new HashMap<>();
            contextMap.put("order",event);
            contextMap.put("customer",customer);
            log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,contextMap={}",contextMap);
            context.setData(contextMap);
            context.setResult(TemplateResult.newSuccess());
        }else {
            context.setResult(TemplateResult.newError(result.getCode(),result.getMsg()));
        }
        log.info("FeishuOpenEnterpriseHandlerTemplate.saveOrder,context.end={}",context);
    }


    @Override
    public void initEnterpriseAndAdminMapping(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.initEnterpriseAndAdminMapping,context={}",context);
        //暂时不实现
    }

    @Override
    public void createFsCustomerAndUpdateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context={}",context);

        Map<String,Object> contextMap = context.getData();
        CreateCustomerAndUpdateMappingArg arg = (CreateCustomerAndUpdateMappingArg) contextMap.get("customer");
        Result<Void> result = orderService.createCustomerAndUpdateMapping(arg);
        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,result={}",result);

        if(result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getCode(),result.getMsg()));
        }
        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsCustomerAndUpdateEnterpriseAndAdminMapping,context.end={}",context);
    }

    @Override
    public void createFsOrder(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsOrder,context={}",context);
        Map<String,Object> contextMap = context.getData();
        FeishuOrderPaidEvent order = (FeishuOrderPaidEvent) contextMap.get("order");
        CreateCustomerAndUpdateMappingArg customer = (CreateCustomerAndUpdateMappingArg) contextMap.get("customer");


        CreateOrderArg createOrderArg = new CreateOrderArg();
        createOrderArg.setFsEa(customer.getFsEa());
        createOrderArg.setOutEa(customer.getOutEa());
        createOrderArg.setOrderId(order.getOrderId());

        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsOrder,createOrderArg={}",createOrderArg);
        Result<Void> result = orderService.createOrder(createOrderArg);
        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsOrder,result={}",result);
        if(result.isSuccess()) {
            context.setResult(TemplateResult.newSuccess());
        } else {
            context.setResult(TemplateResult.newError(result.getCode(),result.getMsg()));
        }
        log.info("FeishuOpenEnterpriseHandlerTemplate.createFsOrder,context.end={}",context);
    }





    @Override
    public boolean isEnterpriseBind(String ea) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.isEnterpriseBind,ea={}",ea);
        return orderService.isEnterpriseBind(ea).getData();
    }

//    @Override
//    public TemplateResult onEnterpriseOpened(Object data) {
//        return super.onEnterpriseOpened(data);
//    }

    @Override
    public void updateEnterpriseAndAdminMapping(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        Result<Void> result = orderService.updateEnterpriseAndAdminMapping(enterpriseAddEvent.getEnterpriseAccount(),
                GlobalValue.FS_ADMIN_USER_ID + "");
        log.info("FeishuOpenEnterpriseHandlerTemplate.updateEnterpriseAndAdminMapping,result={}",result);
    }

    @Override
    public void sendWelcomeMsg(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.sendWelcomeMsg,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        Result<Void> result = orderService.sendWelcomeMsg(enterpriseAddEvent.getEnterpriseAccount());
        log.info("FeishuOpenEnterpriseHandlerTemplate.sendWelcomeMsg,result={}",result);
    }

    @Override
    public void initEnterpriseContacts(MethodContext context) {
        log.info("FeishuOpenEnterpriseHandlerTemplate.initEnterpriseContacts,context={}",context);
        EnterpriseAddEvent enterpriseAddEvent = context.getData();
        Result<Void> result = orderService.initEnterpriseContacts(enterpriseAddEvent.getEnterpriseAccount());
        log.info("FeishuOpenEnterpriseHandlerTemplate.initEnterpriseContacts,result={}",result);
    }
}
