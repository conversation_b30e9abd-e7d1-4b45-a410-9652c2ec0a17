package com.facishare.open.feishu.sync.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.sync.mq.sender.MQSyncSender;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.proto.OutEventDateChangeProto;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 *@IgnoreI18n
 */
@Service("toolsService")
public class ToolsServiceImpl implements ToolsService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private DepartmentBindService departmentBindService;
    @Resource
    private ContactsService contactsService;
    @Resource
    private FsUserInfoManager fsUserInfoManager;
    @Resource
    private FsDepartmentInfoManager fsDepartmentInfoManager;
    @Resource
    private OaConnectorOutDepartmentInfoManager oaConnectorOutDepartmentInfoManager;
    @Resource
    private OaConnectorOutUserInfoManager oaConnectorOutUserInfoManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private CorpInfoManager corpInfoManager;
    @Resource
    private FeishuUserService feishuUserService;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private MQSyncSender mqSyncSender;

    @Resource
    private AppInfoManager appInfoManager;

    @Resource
    private PhoneManager phoneManager;
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Override
    public Result<Void> initEnterprisesCache() {
        //找到所有反绑定的企业
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEnterpriseBindListByBindType(BindTypeEnum.manual);
        //初始化的时候，查看文档是否有数据
        for(OuterOaEnterpriseBindEntity entity : enterpriseBindEntities) {
            initEnterpriseCache(entity);
        }
        return null;
    }

    @Override
    public Result<Void> initEnterpriseCache(String fsEa, String outEa) {
        int parallelism = ForkJoinPool.getCommonPoolParallelism();
        LogUtils.info("ToolsServiceImpl.initEnterpriseCache.parallelism={}", parallelism);
        //找到企业

        List<OuterOaEnterpriseBindEntity> entitiesByOuterEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesByOuterEaChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), fsEa, outEa, null);
        //初始化的时候，查看文档是否有数据
        if(ObjectUtils.isNotEmpty(entitiesByOuterEaChanelEnums)) {
            initEnterpriseCache(entitiesByOuterEaChanelEnums.get(0));
        }
        return null;
    }

    private void initEnterpriseCache(OuterOaEnterpriseBindEntity entity) {
        String fsEa = entity.getFsEa();
        String outEa = entity.getOutEa();
        LogUtils.info("ToolsServiceImpl.initEnterpriseCache.fsEa={},outEa={}", fsEa, outEa);
        //纷享人员
        employeeBindService.fsEmployeeInfoCache(fsEa, null);
        //纷享部门
        departmentBindService.fsDepartmentInfoCache(fsEa, null);
        //飞书可见范围
        contactsService.saveOrUpdateContactData(ConfigCenter.feishuCrmAppId, outEa,entity.getChannel());
    }

    @Override
    public Result<Void> clearEnterpriseInfoCache(String fsEa, String outEa, Integer type, String appId) {
        //找到企业
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder()
                .channel(ChannelEnum.feishu)
                .fsEa(fsEa)
                .outEa(outEa)
                .appId(appId)
                .build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(ObjectUtils.isEmpty(entities)) {
            return Result.newSuccess();
        }
        for (OuterOaEnterpriseBindEntity entity : entities) {

            if(type == 1) {
                DeleteResult deleteResult = fsUserInfoManager.deleteNotInCollectionDocs(fsEa, new LinkedList<>());
                LogUtils.info("ToolsServiceImpl.clearEnterpriseInfoCache.type={},outEa={}", type, deleteResult);
            }
            if(type == 2) {
                DeleteResult deleteResult = fsDepartmentInfoManager.deleteNotInCollectionDocs(fsEa, new LinkedList<>());
                LogUtils.info("ToolsServiceImpl.clearEnterpriseInfoCache.type={},outEa={}", type, deleteResult);
            }
            if(type == 3) {
                DeleteResult deleteResult = oaConnectorOutUserInfoManager.deleteNotInCollectionDocs(ChannelEnum.feishu, outEa, appId, new LinkedList<>());
                LogUtils.info("ToolsServiceImpl.clearEnterpriseInfoCache.type={},outEa={}", type, deleteResult);
            }
            if(type == 4) {
                DeleteResult deleteResult = oaConnectorOutDepartmentInfoManager.deleteNotInCollectionDocs(ChannelEnum.feishu, outEa, appId, new LinkedList<>());
                LogUtils.info("ToolsServiceImpl.clearEnterpriseInfoCache.type={},outEa={}", type, deleteResult);
            }
        }




        return Result.newSuccess();
    }

    @Override
    public Result<String> queryFsEnterpriseOpen(String displayId) {
        if(StringUtils.isEmpty(displayId)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByDisplayId(displayId);
        if(corpInfoEntity==null) {
            return Result.newSuccess("应用未安装");
        }
        //corpId获取到的企业信息
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntitiesByOuterEaChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), corpInfoEntity.getTenantKey(), null, null);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newSuccess("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }

        enterpriseBindList = enterpriseBindList.stream().filter(v -> v.getBindType() == BindTypeEnum.auto).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newSuccess("该企业没有自动创建的纷享企业");
        }

        DateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = outputFormat.format(enterpriseBindList.get(0).getCreateTime());
        if(enterpriseBindList.get(0).getBindStatus() == BindStatusEnum.create) {
            return Result.newSuccess(String.format("该企业创建中，企业ea=%s，创建时间=%s", enterpriseBindList.get(0).getFsEa(), format));
        }
        return Result.newSuccess(String.format("该企业已创建成功，企业ea=%s，创建时间=%s", enterpriseBindList.get(0).getFsEa(), format));
    }

    @Override
    public Result<String> queryFsEmployeeOpen(String dataCenterId, String phone) {
        if(StringUtils.isAnyEmpty(dataCenterId, phone)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        //corpId获取到的企业信息
        OuterOaEnterpriseBindEntity enterpriseBindList = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if(ObjectUtils.isEmpty(enterpriseBindList)) {
            return Result.newSuccess("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }
        String ea = enterpriseBindList.getFsEa();
        String outEa = enterpriseBindList.getOutEa();
        //获取人员绑定关系
        Result<List<UserData.User>> batchGetUserIdsResult = feishuUserService.batchGetUserIds(ConfigCenter.feishuCrmAppId, outEa, new LinkedList<>(), Lists.newArrayList(phone));
        if(CollectionUtils.isEmpty(batchGetUserIdsResult.getData())) {
            return Result.newSuccess("未找到相关人员，请确认手机号码是否正确，该人员是否在飞书应用可见范围");
        }
        List<UserData.User> batchGetUserIds = batchGetUserIdsResult.getData().stream()
                .filter(v -> StringUtils.isNotEmpty(v.getUserId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(batchGetUserIds)) {
            return Result.newSuccess("未找到相关人员，请确认手机号码是否正确，该人员是否在飞书应用可见范围");
        }
        String outUserId = batchGetUserIds.get(0).getUserId();
        EmployeeBindEntity entity = employeeBindManager.getEntity(outEa, outUserId, ea);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newSuccess("该人员创建失败，请联系集成平台相关研发人员排查");
        }
        DateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = outputFormat.format(entity.getCreateTime());
        if(entity.getBindStatus() == BindStatusEnum.create) {
            return Result.newSuccess(String.format("该人员创建中，纷享企业ea=%s，纷享人员id=%s，飞书人员id=%s，创建时间=%s", ea, entity.getFsUserId(), outUserId, format));
        }
        return Result.newSuccess(String.format("该人员已创建成功，纷享企业ea=%s，纷享人员id=%s，飞书人员id=%s，创建时间=%s", ea, entity.getFsUserId(), outUserId, format));
    }

    @Override
    public Result<String> queryEnterpriseBindType(String fsEa, String outEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntitiesByOuterEaChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), fsEa, outEa, null);

        if(ObjectUtils.isEmpty(enterpriseBindList)) {
            return Result.newSuccess("该企业跟飞书无绑定关系");
        }
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=enterpriseBindList.get(0);
        if(outerOaEnterpriseBindEntity.getBindType() == BindTypeEnum.manual) {
            return Result.newSuccess("反绑定的纷享企业");
        }

        return Result.newSuccess("应用开通的纷享企业");
    }

    @Override
    public Result<String> queryFsEmployeeStatus(String dataCenterId, String phone) {

        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if(ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            return Result.newSuccess("该企业没有自动创建的纷享企业，如果不是手动删除了绑定关系或者输错了账号，请联系集成平台相关研发人员排查");
        }
        String ea = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        //获取人员绑定关系
        Result<List<UserData.User>> batchGetUserIdsResult = feishuUserService.batchGetUserIds(ConfigCenter.feishuCrmAppId, outEa, new LinkedList<>(), Lists.newArrayList(phone));
        if(CollectionUtils.isEmpty(batchGetUserIdsResult.getData())) {
            return Result.newSuccess("未找到相关人员，请确认手机号码是否正确，该人员是否在飞书应用可见范围");
        }
        List<UserData.User> batchGetUserIds = batchGetUserIdsResult.getData().stream()
                .filter(v -> StringUtils.isNotEmpty(v.getUserId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(batchGetUserIds)) {
            return Result.newSuccess("未找到相关人员，请确认手机号码是否正确，该人员是否在飞书应用可见范围");
        }
        String outUserId = batchGetUserIds.get(0).getUserId();
        EmployeeBindEntity entity = employeeBindManager.getEntity(outEa, outUserId, ea);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newSuccess("该人员创建失败，请联系集成平台相关研发人员排查");
        }
        //查询crm
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Integer fsUserId = Integer.valueOf(entity.getFsUserId());
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoResult = fsEmployeeServiceProxy.batchGetEmployeeDto(ei, Lists.newArrayList(fsUserId));
        if(employeeDtoResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
            return Result.newSuccess(String.format("该人员已被停用，纷享企业ea=%s，纷享人员id=%s，飞书人员id=%s", ea, fsUserId, outUserId));
        }
        return Result.newSuccess(String.format("该人员状态正常，纷享企业ea=%s，纷享人员id=%s，飞书人员id=%s", ea, fsUserId, outUserId));
    }

    @Override
    public Result<Void> pushCorpBindData2Cloud(String domain) {
        Gson gson = new Gson();
        List<CorpInfoEntity> allEntity = corpInfoManager.getAllEntity();
        if(CollectionUtils.isNotEmpty(allEntity)) {
            String json = gson.toJson(allEntity);
            OutEventDateChangeProto proto = new OutEventDateChangeProto();
            proto.setAppId(null);
            proto.setAppType("dataPush");
            proto.setEventType("CorpInfoEntity");
            proto.setOutEa(null);
            proto.setDomain(domain);
            proto.setContent(json);
            System.out.println(gson.toJson(proto));
            mqSyncSender.sendOutEventDataChangeMQ(ChannelEnum.feishu.name(), proto,"0");
        }
        return Result.newSuccess();
    }

//    private void pushCorpBindData2Cloud2(String domain) {
//        String traceId = UUID.randomUUID().toString();
//        TraceUtil.initTrace("pushCorpBindData2Cloud-" + traceId);
//        //数据较多，分页
//        int pageSize = 10;
//        int pageNum = 1;
//        boolean isHasNextPage = Boolean.TRUE;
//        Gson gson = new Gson();
//        do {
//            List<CorpInfoEntity> allEntity = corpInfoManager.getAllEntity(pageNum, pageSize);
//            if(allEntity.size() < pageSize) {
//                isHasNextPage = Boolean.FALSE;
//            }
//            if(CollectionUtils.isNotEmpty(allEntity)) {
//                String json = gson.toJson(allEntity);
//                OutEventDateChangeProto proto = new OutEventDateChangeProto();
//                proto.setAppId(null);
//                proto.setAppType("dataPush");
//                proto.setEventType("CorpInfoEntity");
//                proto.setOutEa(null);
//                proto.setDomain(domain);
//                proto.setContent(json);
//                System.out.println(gson.toJson(proto));
//                mqSender.sendOutEventDataChangeMQ(ChannelEnum.feishu.name(), proto,"0");
//                try {
//                    Thread.sleep(200L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } while(isHasNextPage);
//        LogUtils.info("ToolsServiceImpl.pushCorpBindData2Cloud,end");
//    }

    @Override
    public Result<Void> pushAppBindData2Cloud(String domain) {
        Gson gson = new Gson();
        List<AppInfoEntity> allEntity = appInfoManager.getAllEntity();
        if(CollectionUtils.isNotEmpty(allEntity)) {
            String json = gson.toJson(allEntity);
            OutEventDateChangeProto proto = new OutEventDateChangeProto();
            proto.setAppId(null);
            proto.setAppType("dataPush");
            proto.setEventType("AppInfoEntity");
            proto.setOutEa(null);
            proto.setDomain(domain);
            proto.setContent(json);
            mqSyncSender.sendOutEventDataChangeMQ(ChannelEnum.feishu.name(), proto,"0");
        }
        return Result.newSuccess();
    }

//    private void pushAppBindData2Cloud2(String domain) {
//        String traceId = UUID.randomUUID().toString();
//        TraceUtil.initTrace("pushCorpBindData2Cloud-" + traceId);
//        //数据较多，分页
//        int pageSize = 1000;
//        int pageNum = 1;
//        boolean isHasNextPage = Boolean.TRUE;
//        Gson gson = new Gson();
//        do {
//            List<AppInfoEntity> allEntity = appInfoManager.getAllEntity(pageNum, pageSize);
//            if(allEntity.size() < pageSize) {
//                isHasNextPage = Boolean.FALSE;
//            }
//            if(CollectionUtils.isNotEmpty(allEntity)) {
//                String json = gson.toJson(allEntity);
//                OutEventDateChangeProto proto = new OutEventDateChangeProto();
//                proto.setAppId(null);
//                proto.setAppType("dataPush");
//                proto.setEventType("AppInfoEntity");
//                proto.setOutEa(null);
//                proto.setDomain(domain);
//                proto.setContent(json);
//                mqSender.sendOutEventDataChangeMQ(ChannelEnum.feishu.name(), proto,"0");
//                try {
//                    Thread.sleep(200L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } while(isHasNextPage);
//        LogUtils.info("ToolsServiceImpl.pushCorpBindData2Cloud,end");
//    }

    @Override
    public Result<Void> pushEnterpriseData2Cloud(String fsEa, String domain) {
//        Gson gson = new Gson();
//        List<OuterOaEnterpriseBindEntity> entityList = enterpriseBindManager.getEntityList(fsEa, null);
//        if(CollectionUtils.isNotEmpty(entityList)) {
//            String json = gson.toJson(entityList);
//            OutEventDateChangeProto proto = new OutEventDateChangeProto();
//            proto.setAppId(null);
//            proto.setAppType("dataPush");
//            proto.setEventType("EnterpriseBindEntity");
//            proto.setOutEa(null);
//            proto.setDomain(domain);
//            proto.setContent(json);
//            mqSyncSender.sendOutEventDataChangeMQ(ChannelEnum.feishu.name(), proto,"0");
//        }
//
//        String outEa = null;
//        List<EmployeeBindEntity> entityList1 = employeeBindManager.getEntityList(fsEa, outEa);
//        if(CollectionUtils.isNotEmpty(entityList1)) {
//            String json = gson.toJson(entityList1);
//            OutEventDateChangeProto proto = new OutEventDateChangeProto();
//            proto.setAppId(null);
//            proto.setAppType("dataPush");
//            proto.setEventType("EmployeeBindEntity");
//            proto.setOutEa(null);
//            proto.setDomain(domain);
//            proto.setContent(json);
//            mqSyncSender.sendOutEventDataChangeMQ(ChannelEnum.feishu.name(), proto,"0");
//        }
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> updateEnterpriseDomain(String fsEa, String domain) {

        return Result.newSuccess();
    }

    @Override
    public Result<Void> pushCloudData(OutEventDateChangeProto proto) {
//        LogUtils.info("ToolsServiceImpl.pushCloudData.proto={}", proto);
//        if(!proto.getDomain().equals(ConfigCenter.crm_domain)) {
//            return Result.newSuccess();
//        }
//        //企业信息
//        Gson gson = new Gson();
//        if(proto.getEventType().equals("CorpInfoEntity")) {
//            List<CorpInfoEntity> corpInfoEntities = gson.fromJson(proto.getContent(), new TypeToken<List<CorpInfoEntity>>(){}.getType());
//            for(CorpInfoEntity entity : corpInfoEntities) {
//                corpInfoManager.updateCorpInfo(entity);
//
//                //限速
//                try {
//                    Thread.sleep(100L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } else if(proto.getEventType().equals("AppInfoEntity")) {
//            List<AppInfoEntity> appInfoEntities = gson.fromJson(proto.getContent(), new TypeToken<List<AppInfoEntity>>(){}.getType());
//            for(AppInfoEntity entity : appInfoEntities) {
//                appInfoManager.updateAppInfo(entity);
//
//                //限速
//                try {
//                    Thread.sleep(100L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } else if(proto.getEventType().equals("EnterpriseBindEntity")) {
//            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = gson.fromJson(proto.getContent(), new TypeToken<List<EnterpriseBindEntity>>(){}.getType());
//            for(OuterOaEnterpriseBindEntity entity : enterpriseBindEntities) {
//                OuterOaEnterpriseBindEntity bindManagerEntity = enterpriseBindManager.getEntity(entity.getFsEa(), entity.getOutEa());
//                if(ObjectUtils.isEmpty(bindManagerEntity)) {
//                    enterpriseBindManager.insert(entity);
//                } else {
//                    enterpriseBindManager.update(entity);
//                }
//
//                //限速
//                try {
//                    Thread.sleep(100L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        } else if(proto.getEventType().equals("EmployeeBindEntity")) {
//            List<EmployeeBindEntity> employeeBindEntities = gson.fromJson(proto.getContent(), new TypeToken<List<EmployeeBindEntity>>(){}.getType());
//            for(EmployeeBindEntity entity : employeeBindEntities) {
//                EmployeeBindEntity employeeBindManagerEntity = employeeBindManager.getEntity(entity.getOutEa(), entity.getOutUserId(), entity.getFsEa());
//                if(ObjectUtils.isEmpty(employeeBindManagerEntity)) {
//                    employeeBindManager.insert(entity);
//                } else {
//                    employeeBindManager.updateById(entity);
//                }
//
//                //限速
//                try {
//                    Thread.sleep(100L);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        }
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> deleteEnterpriseBindById(String fsEa, String outEa, String domain) {
        //删除
        int delete = enterpriseBindManager.deleteByFsEa(fsEa, outEa, domain);
        return Result.newSuccess(delete);
    }

    @Override
    public Result<Integer> deletePhoneBind(String fsEa, String phone) {
        //删除
        Integer delete = phoneManager.delete(fsEa, phone);

        return Result.newSuccess(delete);
    }

    @Override
    public Result<Integer> brushDataAppIdByFeishu() {

        return Result.newSuccess();
    }
}
