package com.facishare.open.feishu.sync.service.impl.whatsapp;

import com.alibaba.fastjson.JSONArray;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.PhoneManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappBindInfo;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.PhoneEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappBalanceResult;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappEnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.params.WhatsAppConnectParams;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service("whatsappEnterpriseBindService")
public class WhatsappEnterpriseBindServiceImpl implements WhatsappEnterpriseBindService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private WhatsappService whatsappService;
    @Resource
    private PhoneManager phoneManager;
    @Resource
    private WhatsappCommonService whatsappCommonService;

    @Override
    public Result<String> saveEnterpriseBindInfo(WhatsappBindInfo bindInfo) {
        //查询
        String outEa = bindInfo.getAccessKey() + "_" + bindInfo.getAccessSecret();
        EnterpriseBindEntity queryEntity = new EnterpriseBindEntity();
        queryEntity.setChannel(ChannelEnum.whatsapp);
        queryEntity.setOutEa(outEa);
        queryEntity.setAccessKey(bindInfo.getAccessKey());
        queryEntity.setAccessSecret(bindInfo.getAccessSecret());
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(queryEntity);
        if(ObjectUtils.isNotEmpty(entity)) {
            //已绑定后，代码不做更改
            //appKey 每个企业也只有一个，代码不做更改
            LogUtils.info("WhatsappEnterpriseBindServiceImpl.saveEnterpriseBindInfo.The enterprise has been bound,outEa={}", outEa);
            if(!bindInfo.getFsEa().equals(entity.getFsEa()) || !bindInfo.getAppKey().equals(entity.getAppKey())) {
                return Result.newError(ResultCodeEnum.WHATSAPP_HAS_BOUND);
            }
            return Result.newSuccess();
        }

        Result<Object> allPhoneResult = getWhatsappPhone(bindInfo);
        if(!allPhoneResult.isSuccess()) {
            return Result.newError(allPhoneResult.getCode(), allPhoneResult.getMsg());
        }

        if(StringUtils.isNotEmpty(bindInfo.getNxAppKey()) || StringUtils.isNotEmpty(bindInfo.getNxSecretKey())) {
            LogUtils.info("WhatsappEnterpriseBindServiceImpl.saveEnterpriseBindInfo.get balance error,outEa={}", outEa);
            com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappBalanceResult> whatsappBalanceResult = whatsappService.whatsappGetBalance(bindInfo);
            if(!whatsappBalanceResult.isSuccess() || ObjectUtils.isEmpty(whatsappBalanceResult.getData())) {
                return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(whatsappBalanceResult.getCode()), ResultCodeEnum.GET_NX_BALANCE_ERROR.getCode()), whatsappBalanceResult.getMessage());
            }
        }
        //插入
        EnterpriseBindEntity insertEntity = new EnterpriseBindEntity();
        insertEntity.setChannel(ChannelEnum.whatsapp);
        insertEntity.setFsEa(bindInfo.getFsEa());
        insertEntity.setAccessKey(bindInfo.getAccessKey());
        insertEntity.setAccessSecret(bindInfo.getAccessSecret());
        insertEntity.setAppKey(bindInfo.getAppKey());
        insertEntity.setOutEa(outEa);
        insertEntity.setNxAppKey(bindInfo.getNxAppKey());
        insertEntity.setNxSecretKey(bindInfo.getNxSecretKey());
        insertEntity.setDomain(ConfigCenter.crm_domain);
        insertEntity.setBindType(BindTypeEnum.manual);
        insertEntity.setBindStatus(BindStatusEnum.normal);
        int insert = enterpriseBindManager.insert(insertEntity);
        LogUtils.info("WhatsappEnterpriseBindServiceImpl.saveEnterpriseBindInfo.insert={}", insert);
        return Result.newSuccess();
    }

    @Override
    public Result<WhatsappBindInfo> queryEnterpriseBindInfo(String fsEa) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, fsEa, null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newSuccess();
        }
        WhatsappBindInfo bindInfo = new WhatsappBindInfo();
        bindInfo.setFsEa(fsEa);
        bindInfo.setAppKey(entity.getAppKey());
        bindInfo.setAccessKey(entity.getAccessKey());
        bindInfo.setAccessSecret(entity.getAccessSecret());
        bindInfo.setNxAppKey(entity.getNxAppKey());
        bindInfo.setNxSecretKey(entity.getNxSecretKey());
        return Result.newSuccess(bindInfo);
    }

    @Override
    public Result<Object> getEnterpriseAllPhone(String fsEa) {
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, fsEa, null);
        if(ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        WhatsappBindInfo bindInfo = new WhatsappBindInfo();
        bindInfo.setFsEa(fsEa);
        bindInfo.setAppKey(enterpriseBindEntity.getAppKey());
        bindInfo.setAccessKey(enterpriseBindEntity.getAccessKey());
        bindInfo.setAccessSecret(enterpriseBindEntity.getAccessSecret());
        return getWhatsappPhone(bindInfo);
    }

    private Result<Object> getWhatsappPhone(WhatsappBindInfo bindInfo) {
        WhatsappBindInfo whatsappBindInfo = new WhatsappBindInfo();
        BeanUtils.copyProperties(bindInfo, whatsappBindInfo);
        whatsappBindInfo.setFsEa(null);
        com.facishare.open.feishu.syncapi.result.whatsapp.Result<Object> whatsappPhoneResult = whatsappService.getWhatsappPhone(whatsappBindInfo);

        if(!whatsappPhoneResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(whatsappPhoneResult.getCode()), ResultCodeEnum.GET_ENTERPRISE_PHONE_ERROR.getCode()), whatsappPhoneResult.getMessage());
        }

        //入库
        Object whatsappPhone = whatsappPhoneResult.getData();
        if(ObjectUtils.isEmpty(whatsappPhone)) {
            return Result.newSuccess(whatsappPhone);
        }
        List<PhoneEntity> phoneEntities = phoneManager.queryEntities(bindInfo.getFsEa(), null, null);
        if(CollectionUtils.isNotEmpty(phoneEntities)) {
            //删除
            Integer deleteCount = phoneManager.delete(bindInfo.getFsEa());
            LogUtils.info("WhatsappEnterpriseBindServiceImpl.getEnterpriseAllPhone.deleteCount={}", deleteCount);
        }
        JSONObject jsonObject = JSONObject.parseObject(whatsappPhone.toString());
        Set<String> businessPhones = jsonObject.keySet();
        for(String businessPhone : businessPhones) {
            JSONArray jsonArray = jsonObject.getJSONArray(businessPhone);
            for (int i = 0; i < jsonArray.size(); i++) {
                String wabaId = jsonArray.getJSONObject(i).getString("waba_id");
                PhoneEntity entity = new PhoneEntity();
                entity.setFsEa(bindInfo.getFsEa());
                entity.setPhone(businessPhone);
                entity.setWabaId(wabaId);
                Integer count = phoneManager.insert(entity);
                LogUtils.info("WhatsappEnterpriseBindServiceImpl.getEnterpriseAllPhone.count={}", count);
            }
        }

        return Result.newSuccess(whatsappPhone);
    }

    @Override
    public Result<String> updateEnterpriseBindInfo(WhatsappBindInfo bindInfo) {
        //查询
        String outEa = bindInfo.getAccessKey() + "_" + bindInfo.getAccessSecret();
        EnterpriseBindEntity queryEntity = new EnterpriseBindEntity();
        queryEntity.setChannel(ChannelEnum.whatsapp);
        queryEntity.setFsEa(bindInfo.getFsEa());
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(queryEntity);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        Result<Object> allPhoneResult = getWhatsappPhone(bindInfo);
        if(!allPhoneResult.isSuccess()) {
            return Result.newError(allPhoneResult.getCode(), allPhoneResult.getMsg());
        }

        if(StringUtils.isNotEmpty(bindInfo.getNxAppKey()) || StringUtils.isNotEmpty(bindInfo.getNxSecretKey())) {
            LogUtils.info("WhatsappEnterpriseBindServiceImpl.updateEnterpriseBindInfo.get balance error,fsEa={}", bindInfo.getFsEa());
            com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappBalanceResult> whatsappBalanceResult = whatsappService.whatsappGetBalance(bindInfo);
            if(!whatsappBalanceResult.isSuccess() || ObjectUtils.isEmpty(whatsappBalanceResult.getData())) {
                return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(whatsappBalanceResult.getCode()), ResultCodeEnum.GET_NX_BALANCE_ERROR.getCode()), whatsappBalanceResult.getMessage());
            }
        }
        //更新
        entity.setAccessKey(bindInfo.getAccessKey());
        entity.setAccessSecret(bindInfo.getAccessSecret());
        entity.setAppKey(bindInfo.getAppKey());
        entity.setOutEa(outEa);
        entity.setNxAppKey(bindInfo.getNxAppKey());
        entity.setNxSecretKey(bindInfo.getNxSecretKey());
        int insert = enterpriseBindManager.update(entity);
        LogUtils.info("WhatsappEnterpriseBindServiceImpl.updateEnterpriseBindInfo.insert={}", insert);
        return Result.newSuccess();
    }
}
