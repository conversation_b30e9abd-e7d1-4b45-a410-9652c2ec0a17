package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserUpdatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工信息变更处理器
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
// IgnoreI18nFile
public class ContactUserUpdatedV3EventHandler extends FeishuEventHandler {
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private ContactsService contactsService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @Override
    public String getSupportEventType() {
        return "contact.user.updated_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactUserUpdatedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactUserUpdatedV3Event event = JSON.parseObject(eventData, FeishuContactUserUpdatedV3Event.class);
        // 查询企业绑定状态
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(header.getTenantKey())
                .appId(header.getAppId()).bindStatus(BindStatusEnum.normal).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : enterpriseBindList) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            contactsService.saveOrUpdateContactUser(header.getEventType(), outerOaEnterpriseBindEntity, outerOaEnterpriseBindEntity.getChannel(),
                    event.getObject(), event.getOldObject(), eventData);
            if(settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
                LogUtils.info("ContactUserUpdatedV3EventHandler.handle,outEa={},自动更新人员:{}",header.getTenantKey(),outerOaEnterpriseBindEntity.getFsEa());
                Result<Void> result = employeeBindService.addOrUpdateEmployee(header, event,outerOaEnterpriseBindEntity);
                LogUtils.info("ContactUserUpdatedV3EventHandler.handle,addOrUpdateEmployee,result={}",result);
            }
        }
        return SUCCESS;
    }
}
