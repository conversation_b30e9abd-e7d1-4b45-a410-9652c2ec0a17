package com.facishare.open.feishu.sync.service.impl.whatsapp;

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.FileTableManager;
import com.facishare.open.feishu.sync.manager.FsFileManager;
import com.facishare.open.feishu.sync.manager.PhoneManager;
import com.facishare.open.feishu.sync.mq.sender.MQSyncSender;
import com.facishare.open.feishu.syncapi.arg.UploadFileArg;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappGetMedia;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.FileEntity;
import com.facishare.open.feishu.syncapi.entity.PhoneEntity;
import com.facishare.open.feishu.syncapi.event.WhatsappEvent;
import com.facishare.open.feishu.syncapi.event.WhatsappMsgPushEvent;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.UploadFileResult;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappGetFileResult;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappEventService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;

@Service("whatsappEventService")
public class WhatsappEventServiceImpl implements WhatsappEventService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private PhoneManager phoneManager;
    @Resource
    private WhatsappService whatsappService;
    @Resource
    private FsFileManager fsFileManager;
    @Resource
    private FileTableManager fileTableManager;
    @Resource
    private MQSyncSender mqSyncSender;

    private static final String whatsappMsgPush = "whatsappMsgPush";

    @Override
    public Result<Void> whatsappEventHandle(WhatsappEvent whatsappEventData) {
        String phone = whatsappEventData.getBusiness_phone();
        LogUtils.info("WhatsappEventServiceImpl.whatsappEventHandle.phone={}", phone);
        //获取绑定信息
        List<PhoneEntity> phoneEntities = phoneManager.queryEntities(null, null, phone);
        if(CollectionUtils.isEmpty(phoneEntities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, phoneEntities.get(0).getFsEa(), null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        if(CollectionUtils.isNotEmpty(whatsappEventData.getMessages())) {
            for(WhatsappEvent.MessageModel messageModel : whatsappEventData.getMessages()) {
                if(ObjectUtils.isNotEmpty(messageModel.getImage())) {
                    String npath = switchNpathFile(entity, phone, messageModel.getImage().getId());
                    if(StringUtils.isNotEmpty(npath)) {
                        messageModel.getImage().setNpath(npath);
                    }
                }

                if(ObjectUtils.isNotEmpty(messageModel.getAudio())) {
                    String npath = switchNpathFile(entity, phone, messageModel.getAudio().getId());
                    if(StringUtils.isNotEmpty(npath)) {
                        messageModel.getAudio().setNpath(npath);
                    }
                }

                if(ObjectUtils.isNotEmpty(messageModel.getVideo())) {
                    String npath = switchNpathFile(entity, phone, messageModel.getVideo().getId());
                    if(StringUtils.isNotEmpty(npath)) {
                        messageModel.getVideo().setNpath(npath);
                    }
                }

                if(ObjectUtils.isNotEmpty(messageModel.getVoice())) {
                    String npath = switchNpathFile(entity, phone, messageModel.getVoice().getId());
                    if(StringUtils.isNotEmpty(npath)) {
                        messageModel.getVoice().setNpath(npath);
                    }
                }

                if(ObjectUtils.isNotEmpty(messageModel.getDocument())) {
                    String npath = switchNpathFile(entity, phone, messageModel.getDocument().getId());
                    if(StringUtils.isNotEmpty(npath)) {
                        messageModel.getDocument().setNpath(npath);
                    }
                }

                if(ObjectUtils.isNotEmpty(messageModel.getSticker())) {
                    String npath = switchNpathFile(entity, phone, messageModel.getSticker().getId());
                    if(StringUtils.isNotEmpty(npath)) {
                        messageModel.getSticker().setNpath(npath);
                    }
                }
            }
        }
        WhatsappMsgPushEvent event = new WhatsappMsgPushEvent();
        event.setChannel(ChannelEnum.whatsapp.name());
        event.setFsEa(entity.getFsEa());
        event.setBusinessPhone(phone);
        event.setWhatsappEvent(whatsappEventData);

        mqSyncSender.sendWhatsappMsgNotifyMQ(whatsappMsgPush, event);
        return Result.newSuccess();
    }

    private String switchNpathFile(EnterpriseBindEntity entity, String phone, String id) {
        LogUtils.info("WhatsappEventServiceImpl.switchNpathFile.id={}", id);
        //查询
        List<FileEntity> fileEntities = fileTableManager.queryEntities(entity.getFsEa(), null, id);
        if(CollectionUtils.isNotEmpty(fileEntities)) {
            return fileEntities.get(0).getNpath();
        }
        WhatsappGetMedia getMedia = new WhatsappGetMedia();
        getMedia.setBusiness_phone(phone);
        getMedia.setMedia_id(id);
        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappGetFileResult> fileResultResult = whatsappService.whatsappGetMedia(entity, getMedia);
        if(!fileResultResult.isSuccess()) {
            LogUtils.info("WhatsappEventServiceImpl.switchNpathFile.fileResultResult={}", fileResultResult);
            return null;
        }
        WhatsappGetFileResult data = fileResultResult.getData();
        String npath = null;
        try {
            List<String> accountList = Splitter.on("/").splitToList(data.getMime_type());
            URL url = new URL(data.getUrl());
            InputStream inputStream = url.openStream();
            URLConnection connection = url.openConnection();
            connection.connect();
            String contentLength = connection.getHeaderField("Content-Length");
            long length = Long.parseLong(contentLength);
            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setEa(entity.getFsEa());
            uploadFileArg.setFileExt(accountList.get(1));
            uploadFileArg.setFileName(data.getFile_name());
            uploadFileArg.setFileSize(length);
            uploadFileArg.setMessageType(accountList.get(0));
            UploadFileResult uploadFileResult = fsFileManager.uploadFile(inputStream, uploadFileArg);
            if(ObjectUtils.isEmpty(uploadFileResult)) {
                LogUtils.info("WhatsappEventServiceImpl.switchNpathFile.uploadFileResult={}", uploadFileResult);
                return null;
            }
            npath = uploadFileResult.getNpath();

            //存库
            FileEntity fileEntity = new FileEntity();
            fileEntity.setFsEa(entity.getFsEa());
            fileEntity.setFileSize(uploadFileResult.getFileSize());
            fileEntity.setType(data.getMime_type());
            fileEntity.setNpath(npath);
            fileEntity.setMediaId(id);
            Integer count = fileTableManager.insert(fileEntity);
            LogUtils.info("WhatsappEventServiceImpl.switchNpathFile.fileEntity={},count={}", fileEntity, count);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return npath;
    }
}
