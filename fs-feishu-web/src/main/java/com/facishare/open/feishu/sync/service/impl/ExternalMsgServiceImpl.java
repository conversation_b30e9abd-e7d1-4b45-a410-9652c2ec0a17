package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.arg.SendTextCardMessagePushArg;
import com.facishare.open.feishu.syncapi.arg.SendTextMessagePushArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.CrmMessageEnum;
import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.enums.OutOaMsgEventTypeEnum;
import com.facishare.open.feishu.syncapi.model.ExternalTodoMsgModel;
import com.facishare.open.feishu.syncapi.model.SendMessage.PostMassageModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ExternalMsgService;
import com.facishare.open.feishu.syncapi.service.MsgService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaScheduleBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.message.extrnal.platform.model.arg.BaseExternalArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.gson.Gson;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service("externalMsgService")
public class ExternalMsgServiceImpl implements ExternalMsgService {
    @Autowired
    private MsgService msgService;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Override
    public Result<Void> sendTextMessage(SendTextMessagePushArg sendTextMessagePushArg) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = sendTextMessagePushArg.getEnterpriseBindEntity();
        SendTextMessageArg sendTextMessageArg = sendTextMessagePushArg.getSendTextMessageArg();
        String feishuAppId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();

        if(CollectionUtils.isEmpty(sendTextMessageArg.getReceiverIds())) {
            return Result.newSuccess();
        }
//        //消息推送有可能把专属云的待办推送到纷享云
//        if(!enterpriseBindEntity.getDomain().equals(ConfigCenter.crm_domain)) {
//            return Result.newSuccess();
//        }
        List<String> fsUserIdList = sendTextMessageArg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByDcId(enterpriseBindEntity.getId(), fsUserIdList, null);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(employeeBindEntities)) {
            return Result.newSuccess();
        }

        //拼接文本内容
        JSONObject jsonObject  = JSONObject.parseObject(sendTextMessageArg.getReceiverChannelData());
        Object appId = jsonObject.get("appId");
        CrmMessageEnum messageEnum = CrmMessageEnum.getAppId(appId.toString());
        PostMassageModel postMessageModel = new PostMassageModel();
        PostMassageModel.LanguageModel languageModel = new PostMassageModel.LanguageModel();
        PostMassageModel.LanguageModel.TextMessage textMessage = new PostMassageModel.LanguageModel.TextMessage();
        PostMassageModel.LanguageModel.TextMessage.Content content = new PostMassageModel.LanguageModel.TextMessage.Content();
        content.setTag(MsgTypeEnum.text.name());
        content.setText(sendTextMessageArg.getMessageContent());
        textMessage.setTitle(messageEnum.getMessageType());
        List<PostMassageModel.LanguageModel.TextMessage.Content> contentList = new LinkedList<>();
        contentList.add(content);
        List<List<PostMassageModel.LanguageModel.TextMessage.Content>> messageContentList = new LinkedList<>();
        messageContentList.add(contentList);
        textMessage.setContent(messageContentList);
        languageModel.setZh_cn(textMessage);
        postMessageModel.setPost(languageModel);
        LogUtils.info("ExternalMsgServiceImpl.sendTextMessage,ea={},fsUserIdList={},postMessageModel={}.", sendTextMessageArg.getEa(), fsUserIdList, postMessageModel);
        List<String> receiverIds = employeeBindEntities.stream().map(OuterOaEmployeeBindEntity::getOutEmpId).collect(Collectors.toList());
        Result<Void> msgResult = msgService.batchSend(feishuAppId, enterpriseBindEntity.getOutEa(), MsgTypeEnum.post, receiverIds, postMessageModel);
        LogUtils.info("ExternalMsgServiceImpl.sendTextMessage,msgResult={}.", msgResult);

        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendTextCardMessage(SendTextCardMessagePushArg sendTextCardMessagePushArg) {
        String msgType = sendTextCardMessagePushArg.getMsgType();
        String upstreamEa = sendTextCardMessagePushArg.getUpstreamEa();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = sendTextCardMessagePushArg.getEnterpriseBindEntity();
        SendTextCardMessageArg sendTextCardMessageArg = sendTextCardMessagePushArg.getSendTextCardMessageArg();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String feishuAppId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();

        if(CollectionUtils.isEmpty(sendTextCardMessageArg.getReceiverIds())) {
            return Result.newSuccess();
        }

//        if(!enterpriseBindEntity.getDomain().equals(ConfigCenter.crm_domain)) {
//            return Result.newSuccess();
//        }

        List<String> fsUserIdList = sendTextCardMessageArg.getReceiverIds().stream().map(v -> v + "").collect(Collectors.toList());
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByDcId(enterpriseBindEntity.getId(), fsUserIdList, null);
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return Result.newSuccess();
        }

        //拼接文本内容
        JSONObject jsonObject  = JSONObject.parseObject(sendTextCardMessageArg.getReceiverChannelData());
        Object appId = jsonObject.get("appId");
        CrmMessageEnum messageEnum = CrmMessageEnum.getAppId(ObjectUtils.isNotEmpty(appId) ? appId.toString() : null);
        PostMassageModel postMessageModel = new PostMassageModel();
        PostMassageModel.LanguageModel languageModel = new PostMassageModel.LanguageModel();
        PostMassageModel.LanguageModel.TextMessage textMessage = new PostMassageModel.LanguageModel.TextMessage();
        PostMassageModel.LanguageModel.TextMessage.Content content = new PostMassageModel.LanguageModel.TextMessage.Content();
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(sendTextCardMessageArg.getForm())) {
            for (int i = 0; i < sendTextCardMessageArg.getForm().size(); i++) {
                markdown.append(sendTextCardMessageArg.getForm().get(i).getKey()).append("：").append(sendTextCardMessageArg.getForm().get(i).getValue()).append("\n");
            }
        } else {
            markdown.append(sendTextCardMessageArg.getMessageContent()).append("\n");
        }
        content.setTag(MsgTypeEnum.text.name());
        content.setText(markdown.toString());
        textMessage.setTitle(messageEnum.getMessageType() + (StringUtils.isNotEmpty(sendTextCardMessageArg.getTitle()) ? "：" + sendTextCardMessageArg.getTitle() : ""));
        PostMassageModel.LanguageModel.TextMessage.Content hrefContent = new PostMassageModel.LanguageModel.TextMessage.Content();
        hrefContent.setTag("a");
        hrefContent.setText("\n查看详情");
        //处理url,如果有自建应用，此处的appId需要改成从库取
        String todoUrl = crmBotUrlGenerator(sendTextCardMessagePushArg, appId);
        LogUtils.info("sendTextCardMessage,todoUrl={}",todoUrl);
        if(StringUtils.isEmpty(todoUrl)) {
            return Result.newSuccess();
        }
        byte[] todoUrlBytes = todoUrl.getBytes();
        String param = new String(Base64.encodeBase64(todoUrlBytes));
        String param2 = "";
        if(msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
            //身份透传
            ExternalTodoMsgModel externalTodoMsgModel = new ExternalTodoMsgModel();
            externalTodoMsgModel.setFsEa(fsEa);
            externalTodoMsgModel.setOutEa(outEa);
            externalTodoMsgModel.setTaskId(sendTextCardMessageArg.getExtraDataMap().get("taskId"));
            externalTodoMsgModel.setMsgType(msgType);
            externalTodoMsgModel.setUpstreamEa(upstreamEa);
            externalTodoMsgModel.setFsAppId(sendTextCardMessageArg.getAppId());
            param2 = new String(Base64.encodeBase64(new Gson().toJson(externalTodoMsgModel).getBytes()));
        }

        String authUrl = ConfigCenter.FEISHU_AUTHEN_URL
                .replace("{app_id}", feishuAppId)
                .replace("{state}", feishuAppId )
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&param2=" + param2 + "&fsEa=" + fsEa));
        String url = ConfigCenter.FEISHU_WEB_URL
                .replace("{mode}", "window")
                .replace("{url}", URLEncoder.encode(authUrl));
        hrefContent.setHref(url);
        List<PostMassageModel.LanguageModel.TextMessage.Content> contentList = new LinkedList<>();
        contentList.add(content);
        contentList.add(hrefContent);
        List<List<PostMassageModel.LanguageModel.TextMessage.Content>> messageContentList = new LinkedList<>();
        messageContentList.add(contentList);
        textMessage.setContent(messageContentList);
        languageModel.setZh_cn(textMessage);
        postMessageModel.setPost(languageModel);
        LogUtils.info("ExternalMsgServiceImpl.sendTextCardMessage,ea={},fsUserIdList={},postMessageModel={}.", fsEa, fsUserIdList, postMessageModel);
        List<String> receiverIds = employeeBindEntities.stream().map(OuterOaEmployeeBindEntity::getOutEmpId).collect(Collectors.toList());
        Result<Void> msgResult = msgService.batchSend(feishuAppId, enterpriseBindEntity.getOutEa(), MsgTypeEnum.post, receiverIds, postMessageModel);
        LogUtils.info("ExternalMsgServiceImpl.sendTextCardMessage,msgResult={}.", msgResult);
        return Result.newSuccess();
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalMsgServiceImpl.getEnterpriseInfo,result={}",result);
        return result;
    }

    private String crmBotUrlGenerator(SendTextCardMessagePushArg sendTextCardMessagePushArg,
                                      Object appId) {
        String todoUrl = null;
        String upstreamEa = sendTextCardMessagePushArg.getUpstreamEa();
        String msgType = sendTextCardMessagePushArg.getMsgType();
        SendTextCardMessageArg sendTextCardMessageArg = sendTextCardMessagePushArg.getSendTextCardMessageArg();
        if(msgType.equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
            if(sendTextCardMessageArg.getGenerateUrlType() == BaseExternalArg.BPM_TASK_URL) {
                if(ObjectUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap())
                        || StringUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap().get("workflowInstanceId"))
                        || StringUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap().get("activityId"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.sendTextCardMessage,param is null,sendTextCardMessageArg={}.", sendTextCardMessageArg);
                    return todoUrl;
                }
                //bpm
//            todoUrl = getEnterpriseInfo(arg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_BPM_URL
//                    .replace("{workflowInstanceId}", arg.getExtraDataMap().get("workflowInstanceId"))
//                    .replace("{activityId}", arg.getExtraDataMap().get("activityId"))
//                    .replace("{apiname}", arg.getExtraDataMap().get("objectApiName"))
//                    .replace("{id}", arg.getExtraDataMap().get("objectId"));
                //bpm业务流，默认先跳转到对象详情页面，等BPM团队做完业务流详情页面，再改成跳转到业务流详情页面
                todoUrl = getEnterpriseInfo(sendTextCardMessageArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_TODO_URL
                        .replace("{apiname}", sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))
                        .replace("{id}", sendTextCardMessageArg.getExtraDataMap().get("objectId"))
                        .replace("{ea}", sendTextCardMessageArg.getEa());
            } else if(sendTextCardMessageArg.getGenerateUrlType() == 5
                    && org.apache.commons.lang3.StringUtils.isNotEmpty(sendTextCardMessageArg.getReceiverChannelData())
                    && ObjectUtils.isNotEmpty(appId)
                    && appId.toString().equals("WJTZ")
                    && ObjectUtils.isNotEmpty(sendTextCardMessageArg.getExtraDataMap())
                    && org.apache.commons.lang3.StringUtils.isNotEmpty(sendTextCardMessageArg.getExtraDataMap().get("filePath"))) {
                //不能直接使用
                String fileUrl = getEnterpriseInfo(sendTextCardMessageArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FILE_URL.replace("{path}", sendTextCardMessageArg.getExtraDataMap().get("filePath"));
                //encode 4 次
                fileUrl = URLEncoder.encode(URLEncoder.encode(URLEncoder.encode(URLEncoder.encode(fileUrl))));
                todoUrl = getEnterpriseInfo(sendTextCardMessageArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_COMMON_WEBVIEW + fileUrl;
            } else {
                todoUrl = getEnterpriseInfo(sendTextCardMessageArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_FUNCTION_URL + sendTextCardMessageArg.getUrl();
            }
        } else {
            //互联待办构造，跟本企业的构造不同
            //暂时不区分业务流还是审批流，目前都是走对象详情页，只有一个地址，但是还是区分，方便后面给单独类型更改地址
            if(sendTextCardMessageArg.getGenerateUrlType() == BaseExternalArg.BPM_TASK_URL) {
                if(ObjectUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap())
                        || StringUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap().get("workflowInstanceId"))
                        || StringUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,sendTextCardMessageArg={}.", sendTextCardMessageArg);
                    return null;
                }
                todoUrl = getEnterpriseInfo(sendTextCardMessageArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_INTERCONNECT_TODO_URL
                        .replace("{fsAppId}", sendTextCardMessageArg.getAppId())
                        .replace("{upstreamEa}", upstreamEa)
                        .replace("{apiName}", sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))
                        .replace("{dataId}", sendTextCardMessageArg.getExtraDataMap().get("objectId")).replace("{webhash}", URLEncoder.encode(
                                ConfigCenter.CRM_WEB_INTERCONNECT_TODO_URL
                                        .replace("{apiname}", sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))
                                        .replace("{dataId}", sendTextCardMessageArg.getExtraDataMap().get("objectId"))
                                        .replace("{fsOutAppId}", sendTextCardMessageArg.getAppId())
                                        .replace("{appId}", sendTextCardMessageArg.getAppId())));
            } else if(sendTextCardMessageArg.getGenerateUrlType() == BaseExternalArg.CRM_DETAIL_URL) {
                if(ObjectUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap())
                        || StringUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap().get("workflowInstanceId"))
                        || StringUtils.isEmpty(sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))) {
                    //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                    LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,sendTextCardMessageArg={}.", sendTextCardMessageArg);
                    return null;
                }
                todoUrl = getEnterpriseInfo(sendTextCardMessageArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_INTERCONNECT_TODO_URL
                        .replace("{fsAppId}", sendTextCardMessageArg.getAppId())
                        .replace("{upstreamEa}", upstreamEa)
                        .replace("{apiName}", sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))
                        .replace("{dataId}", sendTextCardMessageArg.getExtraDataMap().get("objectId")).replace("{webhash}", URLEncoder.encode(
                                ConfigCenter.CRM_WEB_INTERCONNECT_TODO_URL
                                        .replace("{apiname}", sendTextCardMessageArg.getExtraDataMap().get("objectApiName"))
                                        .replace("{dataId}", sendTextCardMessageArg.getExtraDataMap().get("objectId"))
                                        .replace("{fsOutAppId}", sendTextCardMessageArg.getAppId())
                                        .replace("{appId}", sendTextCardMessageArg.getAppId())));
            } else {
                //不支持的类型
                LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,type is null,sendTextCardMessageArg={}.", sendTextCardMessageArg);
                return null;
            }
        }
        return todoUrl;
    }
}
