package com.facishare.open.feishu.sync.manager;

import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

@Slf4j
@Component("oaConnectorOutUserInfoManager")
public class OaConnectorOutUserInfoManager {
    @Autowired
    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;

    public BulkWriteResult batchReplace(List<OaConnectorOutUserInfoDoc> docs) {
        BulkWriteResult bulkWriteResult = oaConnectorOutUserInfoMongoDao.batchReplace(docs);
        return bulkWriteResult;
    }

    public DeleteResult deleteUserInfoByUserId(ChannelEnum channel, String outEa, String outUserId) {
        DeleteResult deleteResult = oaConnectorOutUserInfoMongoDao.deleteUserInfoByUserId(channel, outEa, null, outUserId);
        return deleteResult;
    }

    public List<OaConnectorOutUserInfoDoc> queryUserInfos(ChannelEnum channel, String outEa) {
        List<OaConnectorOutUserInfoDoc> docs = oaConnectorOutUserInfoMongoDao.queryUserInfos(channel, outEa, null);
        return docs;
    }

    public List<OaConnectorOutUserInfoDoc> queryUserInfosByIds(ChannelEnum channel, String outEa, List<String> outUserIds) {
        List<OaConnectorOutUserInfoDoc> docs = oaConnectorOutUserInfoMongoDao.queryUserInfosByIds(channel, outEa, null, outUserIds);
        return docs;
    }

    public DeleteResult deleteNotInCollectionDocs(ChannelEnum channel, String outEa,String appId, Collection<OaConnectorOutUserInfoDoc> docs) {
        DeleteResult deleteResult = oaConnectorOutUserInfoMongoDao.deleteNotInCollectionDocs(channel, outEa, appId, docs);
        return deleteResult;
    }

    public Long countDocuments(ChannelEnum channel, String outEa) {
        Long counts = oaConnectorOutUserInfoMongoDao.countDocuments(channel, outEa, null);
        return counts;
    }
}
