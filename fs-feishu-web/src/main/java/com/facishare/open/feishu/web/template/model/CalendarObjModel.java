package com.facishare.open.feishu.web.template.model;

import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import lombok.Data;

import java.io.Serializable;

@Data
public class CalendarObjModel implements Serializable {
    private OuterOaEnterpriseBindEntity enterpriseBindEntity;
    private String objectId;
    private String eventType;
    private ObjectData objectData;
}
