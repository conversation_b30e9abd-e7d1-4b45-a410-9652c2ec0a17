package com.facishare.open.feishu.sync.service.impl;


import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.enums.MemberTypeEnum;
import com.facishare.open.feishu.syncapi.result.data.UserGroupMemberData;
import com.facishare.open.feishu.syncapi.service.FeishuUserGroupService;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum;
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.ContactScopeData;
import com.facishare.open.feishu.syncapi.service.FeishuContactsService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("feishuContactsService")
public class FeishuContactsServiceImpl implements FeishuContactsService {

    @Resource
    private ProxyHttpClient proxyHttpClient;

    @Resource
    private FeishuAppManager feishuAppManager;

    @Resource
    private FeishuUserGroupService feishuUserGroupService;
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private UrlManager urlManager;

    @Override
    public Result<ContactScopeData> getContactScopeData(String appId,String tenantKey) {
        return getContactScopeData(appId, tenantKey,UserIdTypeEnum.open_id,DepartmentIdTypeEnum.open_department_id,null,100);
    }

    @Override
    public Result<ContactScopeData> getContactScopeData(String appId,
                                                        String tenantKey,
                                                        UserIdTypeEnum userIdType,
                                                        DepartmentIdTypeEnum departmentIdType,
                                                        String pageToken,
                                                        Integer pageSize) {
        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        ContactScopeData scopeData = new ContactScopeData();
        List<String> departmentIds = new LinkedList<>();
        List<String> groupIds = new LinkedList<>();
        List<String> userIds = new LinkedList<>();
        boolean hasMore = Boolean.FALSE;
        do {
            String url = urlManager.getFeishuUrl(tenantKey,appId, FeishuUrlEnum.contact_v3_scopes.getUrl());
            url += "?page_size=" + (pageSize == null ? 100 : pageSize);
            url += "&user_id_type=" + userIdType;
            url += "&department_id_type=" + departmentIdType;
            if (StringUtils.isNotEmpty(pageToken)) {
                url += "&page_token=" + pageToken;
            }

            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
            headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.contact_v3_scopes.getName(), appId, tenantKey));
            headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
            Result<ContactScopeData> result = proxyHttpClient.getUrl(url, headerMap, new TypeReference<Result<ContactScopeData>>() {
            });
            if(result.isSuccess() && ObjectUtils.isNotEmpty(result.getData())) {
                if(CollectionUtils.isNotEmpty(result.getData().getDepartmentIds())) {
                    departmentIds.addAll(result.getData().getDepartmentIds());
                }
                if(CollectionUtils.isNotEmpty(result.getData().getGroupIds())) {
                    groupIds.addAll(result.getData().getGroupIds());
                }
                if(CollectionUtils.isNotEmpty(result.getData().getUserIds())) {
                    userIds.addAll(result.getData().getUserIds());
                }
                pageToken = result.getData().getPageToken();
                hasMore = result.getData().isHasMore();
            } else {
                return result;
            }
        } while(hasMore);
        if(CollectionUtils.isNotEmpty(departmentIds)) {
            scopeData.setDepartmentIds(departmentIds);
        }
        if(CollectionUtils.isNotEmpty(groupIds)) {
            scopeData.setGroupIds(groupIds);
        }
        if(CollectionUtils.isNotEmpty(userIds)) {
            scopeData.setUserIds(userIds);
        }
        if(CollectionUtils.isEmpty(scopeData.getUserIds())) {
            scopeData.setUserIds(new ArrayList<>());
        }
        if(CollectionUtils.isEmpty(scopeData.getDepartmentIds())) {
            scopeData.setDepartmentIds(new ArrayList<>());
        }
        LogUtils.info("getContactScopeData,scopeData={}",scopeData);
        if(CollectionUtils.isNotEmpty(scopeData.getGroupIds())) {
            LogUtils.info("getContactScopeData,groupIds={}",scopeData.getGroupIds());
            for(String groupId : scopeData.getGroupIds()) {
                Result<List<UserGroupMemberData>> result = feishuUserGroupService.getUserGroupMemberList(appId,
                        tenantKey,
                        groupId,
                        MemberTypeEnum.user);
                LogUtils.info("getContactScopeData,getUserGroupMemberList,user,groupId={},result={}",groupId,result);
                if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                    List<String> openUserIdList = result.getData().stream()
                            .map(UserGroupMemberData::getMemberId)
                            .collect(Collectors.toList());
                    scopeData.getUserIds().addAll(openUserIdList);
                }

                result = feishuUserGroupService.getUserGroupMemberList(appId,
                        tenantKey,
                        groupId,
                        MemberTypeEnum.department);
                LogUtils.info("getContactScopeData,getUserGroupMemberList,department,groupId={},result={}",groupId,result);
                if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                    List<String> openDepIdList = result.getData().stream()
                            .map(UserGroupMemberData::getMemberId)
                            .collect(Collectors.toList());
                    scopeData.getDepartmentIds().addAll(openDepIdList);
                }
            }
        }
        LogUtils.info("getContactScopeData,scopeData2={}",scopeData);
        return Result.newSuccess(scopeData);
    }
}
