package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.AppInfoManager;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.AppService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("appService")
public class AppServiceImpl implements AppService {
    @Resource
    private AppInfoManager appInfoManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Override
    public Result<Integer> updateAppInfo(AppInfoEntity entity) {
        return new Result<>(appInfoManager.updateAppInfo(entity));
    }

    @Override
    public Result<OuterOaAppInfoEntity> getAppInfo(String outEa, String appId) {
        OuterOaAppInfoParams outerOaAppInfoParams=OuterOaAppInfoParams.builder().outEa(outEa).appId(appId).build();
        List<OuterOaAppInfoEntity> entities = outerOaAppInfoManager.getEntities(outerOaAppInfoParams);
        if(CollectionUtils.isNotEmpty(entities)){
            OuterOaAppInfoEntity outerOaAppInfoEntity=entities.get(0);
            return new Result<>(outerOaAppInfoEntity);
        }
        return Result.newSuccess();
    }
}
