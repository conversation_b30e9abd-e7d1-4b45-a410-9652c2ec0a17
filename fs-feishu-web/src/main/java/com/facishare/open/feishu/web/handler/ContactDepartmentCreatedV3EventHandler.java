package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactDepartmentCreatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserUpdatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 部门创建事件处理器
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class ContactDepartmentCreatedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    private String FEISHU_DEPT_LOCK="FEISHU_DEPT_LOCK_%s";
    @Override
    public String getSupportEventType() {
        return "contact.department.created_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactDepartmentCreatedV3EventHandler.handle,eventData={}",eventData);
        FeishuContactDepartmentCreatedV3Event event = JSON.parseObject(eventData, FeishuContactDepartmentCreatedV3Event.class);
        LogUtils.info("ContactDepartmentCreatedV3EventHandler.handle,event={}",event);

        // 查询企业绑定状态
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(header.getTenantKey())
                .appId(header.getAppId()).bindStatus(BindStatusEnum.normal).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);

        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : enterpriseBindList) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            if(settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.manual||settingAccountRulesModel.getSyncTypeEnum()== EnterpriseConfigAccountSyncTypeEnum.accountBind){
                log.info("ContactDepartmentCreatedV3EventHandler.handle,outEa={},账号设置手动绑定，不支持新增部门：{}",header.getTenantKey(),outerOaEnterpriseBindEntity.getFsEa());
                contactsService.saveOrUpdateContactDepartment(header.getEventType(), outerOaEnterpriseBindEntity,
                        event.getObject(), null, eventData);
            }else{
                String openDepartmentId = event.getObject().getOpenDepartmentId();
                RLock oaLock = redissonClient.getLock(String.format(FEISHU_DEPT_LOCK, openDepartmentId));
                try {
                    boolean lockValue = oaLock.tryLock(10, TimeUnit.SECONDS);
                    if(lockValue){
                        try {
                            Result<Void> result = contactsService.addDepList(header.getAppId(),
                                    header.getTenantKey(),
                                    Lists.newArrayList(event.getObject()),outerOaEnterpriseBindEntity);
                            LogUtils.info("ContactDepartmentCreatedV3EventHandler.handle,addDepList,result={}",result);
                        } catch (Exception e) {
                            log.error("department create fail:{}",e.getMessage());
                        } finally {
                            oaLock.unlock();
                        }
                    }
                } catch (Exception e) {
                    log.error("contact dept get lock fail:{}",e.getMessage());
                }
            }
        }



        return SUCCESS;
    }
}
