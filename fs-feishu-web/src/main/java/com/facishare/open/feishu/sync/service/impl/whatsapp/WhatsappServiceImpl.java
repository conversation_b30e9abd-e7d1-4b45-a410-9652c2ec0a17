package com.facishare.open.feishu.sync.service.impl.whatsapp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.PhoneManager;
import com.facishare.open.feishu.sync.utils.WhatsappMD5Utils;
import com.facishare.open.feishu.syncapi.consts.OutUrlConsts;
import com.facishare.open.feishu.syncapi.data.whatsapp.*;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.WhatsappActionEnum;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.whatsapp.*;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.limiter.SpeedLimiter;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.params.WhatsAppConnectParams;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service("whatsappService")
public class WhatsappServiceImpl implements WhatsappService {
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource(name = "okHttpSupport")
    private OkHttpSupport okHttpSupport;
    @Resource
    private SpeedLimiter speedLimiter;
    @Resource
    private PhoneManager phoneManager;

    @Override
    public Result<Object> getWhatsappPhone(WhatsappBindInfo whatsappBindInfo) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", whatsappBindInfo.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.getPhone.toString());

        Map<String, Object> body = new HashMap<>();
        body.put("appkey", whatsappBindInfo.getAppKey());
        body.put("messaging_product", ChannelEnum.whatsapp.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(body), whatsappBindInfo.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + whatsappBindInfo.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<Object> getPhoneResult = proxyHttpClient.postUrl(OutUrlConsts.getPhone, body, header, new TypeReference<Result<Object>>() {
        });
        if(getPhoneResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return getPhoneResult;
    }

    @Override
    public Result<WhatsappUploadFileResult> whatsappUploadFile(EnterpriseBindEntity enterpriseBindEntity, WhatsappUploadMedia uploadMedia) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.uploadMedia.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, null, enterpriseBindEntity.getAccessSecret());

        // 创建RequestBody，添加byte数组和其他表单数据
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("file", uploadMedia.getFileName(),
                        RequestBody.create(uploadMedia.getFile(), MediaType.parse(uploadMedia.getType())))
                .addFormDataPart("messaging_product", ChannelEnum.whatsapp.toString())
                .addFormDataPart("type", uploadMedia.getType())
                .addFormDataPart("business_phone", uploadMedia.getBusiness_phone())
                .build();

        // 创建请求
        Request request = new Request.Builder()
                .url(OutUrlConsts.uploadMedia)
                .method("POST", body)
                .addHeader("accessKey", enterpriseBindEntity.getAccessKey())
                .addHeader("ts", String.valueOf(tm))
                .addHeader("bizType", "2")
                .addHeader("action", WhatsappActionEnum.uploadMedia.toString())
                .addHeader("sign", md5Hash)
                // 注意：'Content-Type' 不需要手动设置，OkHttp会为你处理
                .build();
        //限速
        boolean isAllow = speedLimiter.isAllow((GlobalValue.outLimit + "media_file"), 10L, 1L, 10);
        if(!isAllow) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        Result<WhatsappUploadFileResult> uploadFileResultResult = this.okHttpSupport.parseObject(request, new TypeReference<Result<WhatsappUploadFileResult>>() {
        });
        return uploadFileResultResult;
    }

    @Override
    public Result<WhatsappSendMsgResult> whatsappSendMsg(EnterpriseBindEntity enterpriseBindEntity, WhatsappSendMsg sendMsg) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.mt.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(sendMsg), enterpriseBindEntity.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + enterpriseBindEntity.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<WhatsappSendMsgResult> whatsappSendMsgResultResult = proxyHttpClient.postUrl(OutUrlConsts.sendMsg, sendMsg, header, new TypeReference<Result<WhatsappSendMsgResult>>() {
        });
        if(whatsappSendMsgResultResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return whatsappSendMsgResultResult;
    }

    @Override
    public Result<WhatsappGetTemplateResult> whatsappGetTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappGetTemplate template) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.getTemplate.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(template), enterpriseBindEntity.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + enterpriseBindEntity.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<WhatsappGetTemplateResult> templateResult = proxyHttpClient.postUrl(OutUrlConsts.getTemplate, template, header, new TypeReference<Result<WhatsappGetTemplateResult>>() {
        });
        if(templateResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return templateResult;
    }

    @Override
    public Result<WhatsappCreateTemplateResult> whatsappCreateTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappCreateTemplate template) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.createTemplate.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(template), enterpriseBindEntity.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + enterpriseBindEntity.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<WhatsappCreateTemplateResult> createTemplateResultResult = proxyHttpClient.postUrl(OutUrlConsts.createTemplate, template, header, new TypeReference<Result<WhatsappCreateTemplateResult>>() {
        });
        if(createTemplateResultResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return createTemplateResultResult;
    }

    @Override
    public Result<WhatsappUploadFileResult> whatsappUploadTemplateFile(EnterpriseBindEntity enterpriseBindEntity, WhatsappUploadMedia uploadMedia) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.uploadTemplateFile.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, null, enterpriseBindEntity.getAccessSecret());

        // 创建RequestBody，添加byte数组和其他表单数据
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("file", uploadMedia.getFileName(),
                        RequestBody.create(uploadMedia.getFile(), MediaType.parse(uploadMedia.getType())))
                .addFormDataPart("messaging_product", ChannelEnum.whatsapp.toString())
                .addFormDataPart("type", uploadMedia.getType())
                .addFormDataPart("business_phone", uploadMedia.getBusiness_phone())
                .build();

        // 创建请求
        Request request = new Request.Builder()
                .url(OutUrlConsts.uploadTemplateFile)
                .method("POST", body)
                .addHeader("accessKey", enterpriseBindEntity.getAccessKey())
                .addHeader("ts", String.valueOf(tm))
                .addHeader("bizType", "2")
                .addHeader("action", WhatsappActionEnum.uploadTemplateFile.toString())
                .addHeader("sign", md5Hash)
                // 注意：'Content-Type' 不需要手动设置，OkHttp会为你处理
                .build();
        //限速
        boolean isAllow = speedLimiter.isAllow((GlobalValue.outLimit + "media_file"), 10L, 1L, 10);
        if(!isAllow) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        Result<WhatsappUploadFileResult> uploadFileResultResult = this.okHttpSupport.parseObject(request, new TypeReference<Result<WhatsappUploadFileResult>>() {
        });
        return uploadFileResultResult;
    }

    @Override
    public Result<WhatsappUpdateTemplateResult> whatsappUpdateTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappUpdateTemplate template) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.updateTemplate.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(template), enterpriseBindEntity.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + enterpriseBindEntity.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<WhatsappUpdateTemplateResult> templateResult = proxyHttpClient.postUrl(OutUrlConsts.updateTemplate, template, header, new TypeReference<Result<WhatsappUpdateTemplateResult>>() {
        });
        if(templateResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return templateResult;
    }

    @Override
    public Result<WhatsappDeleteTemplateResult> whatsappDeleteTemplate(EnterpriseBindEntity enterpriseBindEntity, WhatsappDeleteTemplate template) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.deleteTemplate.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(template), enterpriseBindEntity.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + enterpriseBindEntity.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<WhatsappDeleteTemplateResult> templateResult = proxyHttpClient.postUrl(OutUrlConsts.deleteTemplate, template, header, new TypeReference<Result<WhatsappDeleteTemplateResult>>() {
        });
        if(templateResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return templateResult;
    }

    @Override
    public Result<WhatsappGetFileResult> whatsappGetMedia(EnterpriseBindEntity enterpriseBindEntity, WhatsappGetMedia template) {
        long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", enterpriseBindEntity.getAccessKey());
        header.put("ts", String.valueOf(tm));
        header.put("bizType", "2");
        header.put("action", WhatsappActionEnum.getMedia.toString());

        String md5Hash = WhatsappMD5Utils.sign(header, JSON.toJSONString(template), enterpriseBindEntity.getAccessSecret());
        header.put("sign", md5Hash);
        header.put("out-limit", (GlobalValue.outLimit + enterpriseBindEntity.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");
        Result<WhatsappGetFileResult> getFileResultResult = proxyHttpClient.postUrl(OutUrlConsts.getMedia, template, header, new TypeReference<Result<WhatsappGetFileResult>>() {
        });
        if(getFileResultResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        return getFileResultResult;
    }

    @Override
    public Result<WhatsappBalanceResult> whatsappGetBalance(WhatsappBindInfo whatsappBindInfo) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        header.put("out-limit", (GlobalValue.outLimit + whatsappBindInfo.getAppKey()));
        header.put("out-limit-times", "20");
        header.put("out-limit-speed-seconds", "1");

        String body = "appkey=" + whatsappBindInfo.getNxAppKey() + "&secretkey=" + whatsappBindInfo.getNxSecretKey();

        String httpResult = proxyHttpClient.postUrl(OutUrlConsts.getBalance, body, header);
        if(httpResult == null) {
            return Result.newError(ResultCodeEnum.OUT_SPEED_LIMITER);
        }
        WhatsappBalanceResult balanceResult = JSONObject.parseObject(httpResult, WhatsappBalanceResult.class);
        return Result.newSuccess(balanceResult);
    }
}
