package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.ExternalApprovalsTemplateMapper;
import com.facishare.open.feishu.syncapi.entity.ExternalApprovalsTemplateEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ExternalApprovalsTemplateManager {
    @Resource
    private ExternalApprovalsTemplateMapper externalApprovalsTemplateMapper;

    public Integer insert(ExternalApprovalsTemplateEntity entity) {
        int count = externalApprovalsTemplateMapper.insert(entity);
        LogUtils.info("ExternalApprovalsTemplateManager.insert,count={}",count);
        return count;
    }

    public Integer update(ExternalApprovalsTemplateEntity entity) {
        int count = externalApprovalsTemplateMapper.updateById(entity);
        LogUtils.info("ExternalApprovalsTemplateManager.update,count={}",count);
        return count;
    }

    public ExternalApprovalsTemplateEntity queryEntity(ChannelEnum channel, String outEa) {
        LambdaQueryWrapper<ExternalApprovalsTemplateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalApprovalsTemplateEntity::getOutEa, outEa);
        wrapper.eq(ExternalApprovalsTemplateEntity::getChannel, channel);
        return externalApprovalsTemplateMapper.selectOne(wrapper);
    }
}
