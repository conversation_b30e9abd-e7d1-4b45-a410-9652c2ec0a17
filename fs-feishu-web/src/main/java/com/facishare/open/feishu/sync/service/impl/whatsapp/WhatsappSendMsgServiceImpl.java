package com.facishare.open.feishu.sync.service.impl.whatsapp;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.FileTableManager;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappSendMsg;
import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappUploadMedia;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.FileEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappSendMsgResult;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappUploadFileResult;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappSendMsgService;
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("whatsappSendMsgService")
public class WhatsappSendMsgServiceImpl implements WhatsappSendMsgService {
    @Resource
    private WhatsappService whatsappService;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private FileTableManager fileTableManager;
    @Resource
    private NFileStorageService nFileStorageService;

    private static final Long max_image_size = 5 * 1024 * 1024L;
    private static final Long max_video_file_size = 16 * 1024 * 1024L;
    private static final Long max_audio_file_size = 16 * 1024 * 1024L;
    private static final Long max_document_file_size = 100 * 1024 * 1024L;
    private static final Long max_sticker_file_size = 100L;

    @Override
    public Result<WhatsappSendMsgResult> whatsappSendMsg(WhatsappSendMsg sendMsg) {
        //查看是否有绑定关系
        if(StringUtils.isAnyEmpty(sendMsg.getFsEa(), sendMsg.getBusiness_phone())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.whatsapp, sendMsg.getFsEa(), null);
        if(ObjectUtils.isEmpty(entity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        WhatsappSendMsg whatsappSendMsg = new WhatsappSendMsg();
        BeanUtils.copyProperties(sendMsg, whatsappSendMsg);
        whatsappSendMsg.setFsEa(null);
        whatsappSendMsg.setAppkey(entity.getAppKey());

        //替换
        Result<WhatsappSendMsg> whatsappSendMsgResult = switchMsgFile(entity, whatsappSendMsg);
        if(!whatsappSendMsgResult.isSuccess()) {
            return Result.newError(whatsappSendMsgResult.getCode(), whatsappSendMsgResult.getMsg());
        }

        //发送消息
        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappSendMsgResult> sendMsgResultResult = whatsappService.whatsappSendMsg(entity, whatsappSendMsgResult.getData());

        if(!sendMsgResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(sendMsgResultResult.getCode()), ResultCodeEnum.SEND_MESSAGE_ERROR.getCode()), sendMsgResultResult.getMessage());
        }
        return Result.newSuccess(sendMsgResultResult.getData());
    }

    private Result<WhatsappSendMsg> switchMsgFile(EnterpriseBindEntity entity, WhatsappSendMsg whatsappSendMsg) {
        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getImage()) && StringUtils.isNotEmpty(whatsappSendMsg.getImage().getNpath())) {
            Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getImage().getNpath(), whatsappSendMsg.getImage().getType(), whatsappSendMsg.getImage().getFilename(), whatsappSendMsg.getBusiness_phone());
            if(!mediaIdResult.isSuccess()) {
                return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
            }
            whatsappSendMsg.getImage().setId(mediaIdResult.getData());
            whatsappSendMsg.getImage().setNpath(null);
            whatsappSendMsg.getImage().setFilename(null);
            whatsappSendMsg.getImage().setType(null);
        }

        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getVideo()) && StringUtils.isNotEmpty(whatsappSendMsg.getVideo().getNpath())) {
            Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getVideo().getNpath(), whatsappSendMsg.getVideo().getType(), whatsappSendMsg.getVideo().getFilename(), whatsappSendMsg.getBusiness_phone());
            if(!mediaIdResult.isSuccess()) {
                return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
            }
            whatsappSendMsg.getVideo().setId(mediaIdResult.getData());
            whatsappSendMsg.getVideo().setNpath(null);
            whatsappSendMsg.getVideo().setFilename(null);
            whatsappSendMsg.getVideo().setType(null);
        }

        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getAudio()) && StringUtils.isNotEmpty(whatsappSendMsg.getAudio().getNpath())) {
            Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getAudio().getNpath(), whatsappSendMsg.getAudio().getType(), whatsappSendMsg.getAudio().getFilename(), whatsappSendMsg.getBusiness_phone());
            if(!mediaIdResult.isSuccess()) {
                return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
            }
            whatsappSendMsg.getAudio().setId(mediaIdResult.getData());
            whatsappSendMsg.getAudio().setNpath(null);
            whatsappSendMsg.getAudio().setFilename(null);
            whatsappSendMsg.getAudio().setType(null);
        }

        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getDocument()) && StringUtils.isNotEmpty(whatsappSendMsg.getDocument().getNpath())) {
            Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getDocument().getNpath(), whatsappSendMsg.getDocument().getType(), whatsappSendMsg.getDocument().getFilename(), whatsappSendMsg.getBusiness_phone());
            if(!mediaIdResult.isSuccess()) {
                return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
            }
            whatsappSendMsg.getDocument().setId(mediaIdResult.getData());
            whatsappSendMsg.getDocument().setNpath(null);
            whatsappSendMsg.getDocument().setType(null);
        }

        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getSticker()) && StringUtils.isNotEmpty(whatsappSendMsg.getSticker().getNpath())) {
            Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getSticker().getNpath(), whatsappSendMsg.getSticker().getType(), whatsappSendMsg.getSticker().getFilename(), whatsappSendMsg.getBusiness_phone());
            if(!mediaIdResult.isSuccess()) {
                return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
            }
            whatsappSendMsg.getSticker().setId(mediaIdResult.getData());
            whatsappSendMsg.getSticker().setNpath(null);
            whatsappSendMsg.getSticker().setFilename(null);
            whatsappSendMsg.getSticker().setType(null);
        }

        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getTemplate())) {
            if(CollectionUtils.isNotEmpty(whatsappSendMsg.getTemplate().getComponents())) {
                for(WhatsappSendMsg.TemplateModel.Component component : whatsappSendMsg.getTemplate().getComponents()) {
                    if(CollectionUtils.isNotEmpty(component.getParameters())) {
                        for(WhatsappSendMsg.TemplateModel.Component.Parameter parameter : component.getParameters()) {
                            if(ObjectUtils.isNotEmpty(parameter.getImage()) && StringUtils.isNotEmpty(parameter.getImage().getNpath())) {
                                Result<String> mediaIdResult = getMediaId(entity, parameter.getImage().getNpath(), parameter.getImage().getType(), parameter.getImage().getFilename(), whatsappSendMsg.getBusiness_phone());
                                if(!mediaIdResult.isSuccess()) {
                                    return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
                                }
                                parameter.getImage().setId(mediaIdResult.getData());
                                whatsappSendMsg.getImage().setNpath(null);
                                parameter.getImage().setFilename(null);
                                parameter.getImage().setType(null);
                            }

                            if(ObjectUtils.isNotEmpty(parameter.getVideo()) && StringUtils.isNotEmpty(parameter.getVideo().getNpath())) {
                                Result<String> mediaIdResult = getMediaId(entity, parameter.getVideo().getNpath(), parameter.getVideo().getType(), parameter.getVideo().getFilename(), whatsappSendMsg.getBusiness_phone());
                                if(!mediaIdResult.isSuccess()) {
                                    return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
                                }
                                parameter.getVideo().setId(mediaIdResult.getData());
                                whatsappSendMsg.getVideo().setNpath(null);
                                parameter.getVideo().setFilename(null);
                                parameter.getVideo().setType(null);
                            }

                            if(ObjectUtils.isNotEmpty(parameter.getDocument()) && StringUtils.isNotEmpty(parameter.getDocument().getNpath())) {
                                Result<String> mediaIdResult = getMediaId(entity, parameter.getDocument().getNpath(), parameter.getDocument().getType(), parameter.getDocument().getFilename(), whatsappSendMsg.getBusiness_phone());
                                if(!mediaIdResult.isSuccess()) {
                                    return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
                                }
                                parameter.getDocument().setId(mediaIdResult.getData());
                                whatsappSendMsg.getDocument().setNpath(null);
                                parameter.getDocument().setType(null);
                            }
                        }
                    }
                }
            }
        }

        if(ObjectUtils.isNotEmpty(whatsappSendMsg.getInteractive())) {
            if(ObjectUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader())) {
                if(ObjectUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader().getImage()) && StringUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader().getImage().getNpath())) {
                    Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getInteractive().getHeader().getImage().getNpath(), whatsappSendMsg.getInteractive().getHeader().getImage().getType(), whatsappSendMsg.getInteractive().getHeader().getImage().getFilename(), whatsappSendMsg.getBusiness_phone());
                    if(!mediaIdResult.isSuccess()) {
                        return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
                    }
                    whatsappSendMsg.getInteractive().getHeader().getImage().setId(mediaIdResult.getData());
                    whatsappSendMsg.getInteractive().getHeader().getImage().setNpath(null);
                    whatsappSendMsg.getInteractive().getHeader().getImage().setType(null);
                    whatsappSendMsg.getInteractive().getHeader().getImage().setFilename(null);
                }

                if(ObjectUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader().getVideo()) && StringUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader().getVideo().getNpath())) {
                    Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getInteractive().getHeader().getVideo().getNpath(), whatsappSendMsg.getInteractive().getHeader().getVideo().getType(), whatsappSendMsg.getInteractive().getHeader().getVideo().getFilename(), whatsappSendMsg.getBusiness_phone());
                    if(!mediaIdResult.isSuccess()) {
                        return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
                    }
                    whatsappSendMsg.getInteractive().getHeader().getVideo().setId(mediaIdResult.getData());
                    whatsappSendMsg.getInteractive().getHeader().getVideo().setNpath(null);
                    whatsappSendMsg.getInteractive().getHeader().getVideo().setType(null);
                    whatsappSendMsg.getInteractive().getHeader().getVideo().setFilename(null);
                }

                if(ObjectUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader().getDocument()) && StringUtils.isNotEmpty(whatsappSendMsg.getInteractive().getHeader().getDocument().getNpath())) {
                    Result<String> mediaIdResult = getMediaId(entity, whatsappSendMsg.getInteractive().getHeader().getDocument().getNpath(), whatsappSendMsg.getInteractive().getHeader().getDocument().getType(), whatsappSendMsg.getInteractive().getHeader().getDocument().getFilename(), whatsappSendMsg.getBusiness_phone());
                    if(!mediaIdResult.isSuccess()) {
                        return Result.newError(mediaIdResult.getCode(), mediaIdResult.getMsg());
                    }
                    whatsappSendMsg.getInteractive().getHeader().getDocument().setId(mediaIdResult.getData());
                    whatsappSendMsg.getInteractive().getHeader().getDocument().setNpath(null);
                    whatsappSendMsg.getInteractive().getHeader().getDocument().setType(null);
                }
            }
        }

        return Result.newSuccess(whatsappSendMsg);
    }

    private Result<String> getMediaId(EnterpriseBindEntity entity, String npath, String type, String filename, String businessPhone) {
        LogUtils.info("WhatsappSendMsgServiceImpl.getMediaId.npath={},type={}，filename={}", npath, type, filename);
        List<FileEntity> fileEntities = fileTableManager.queryEntities(entity.getFsEa(), npath, null);
        if(CollectionUtils.isNotEmpty(fileEntities)) {
            return Result.newSuccess(fileEntities.get(0).getMediaId());
        }

        //上传
        WhatsappUploadMedia uploadMedia = new WhatsappUploadMedia();
        uploadMedia.setBusiness_phone(businessPhone);
        uploadMedia.setType(type);

        //上传文件
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
        arg.setEa(entity.getFsEa());
        arg.setFileName(npath);
        NGetFileMetaData.Result fileMetaData = nFileStorageService.nGetFileMetaData(arg, entity.getFsEa());
        if(ObjectUtils.isEmpty(fileMetaData)) {
            return Result.newError(ResultCodeEnum.GET_FILE_META_DATA_FAILED.getCode(),ResultCodeEnum.GET_FILE_META_DATA_FAILED.getMsg());
        }

        uploadMedia.setFileName(StringUtils.isNotEmpty(filename) ? filename : fileMetaData.getFileName());

        if(type.equals("image/webp") && fileMetaData.getSize() > max_sticker_file_size) {
            return Result.newError(ResultCodeEnum.STICKER_OVERSIZE.getCode(),ResultCodeEnum.STICKER_OVERSIZE.getMsg());
        } else if(StringUtils.containsIgnoreCase(type, "image") && fileMetaData.getSize() > max_image_size) {
            return Result.newError(ResultCodeEnum.IMAGE_OVERSIZE.getCode(),ResultCodeEnum.GET_FILE_META_DATA_FAILED.getMsg());
        } else if(StringUtils.containsIgnoreCase(type, "audio") && fileMetaData.getSize() > max_audio_file_size) {
            return Result.newError(ResultCodeEnum.AUDIO_OVERSIZE.getCode(),ResultCodeEnum.AUDIO_OVERSIZE.getMsg());
        } else if(StringUtils.containsIgnoreCase(type, "video") && fileMetaData.getSize() > max_video_file_size) {
            return Result.newError(ResultCodeEnum.VIDEO_OVERSIZE.getCode(),ResultCodeEnum.VIDEO_OVERSIZE.getMsg());
        } else if(fileMetaData.getSize() > max_document_file_size) {
            return Result.newError(ResultCodeEnum.DOCUMENT_OVERSIZE.getCode(),ResultCodeEnum.DOCUMENT_OVERSIZE.getMsg());
        }

        NDownloadFile.Arg downloadFile = new NDownloadFile.Arg();
        downloadFile.setEa(entity.getFsEa());
        downloadFile.setnPath(npath);
        downloadFile.setDownloadSecurityGroup(fileMetaData.getSecurityGroup());
        NDownloadFile.Result downloadFileMetaData = nFileStorageService.nDownloadFile(downloadFile, entity.getFsEa());
        uploadMedia.setFile(downloadFileMetaData.getData());

        com.facishare.open.feishu.syncapi.result.whatsapp.Result<WhatsappUploadFileResult> whatsappUploadFileResultResult = whatsappService.whatsappUploadFile(entity, uploadMedia);
        if(!whatsappUploadFileResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(whatsappUploadFileResultResult.getCode()), ResultCodeEnum.SEND_MESSAGE_ERROR.getCode()), whatsappUploadFileResultResult.getMessage());
        }
        FileEntity fileEntity = new FileEntity();
        fileEntity.setFsEa(entity.getFsEa());
        fileEntity.setMediaId(whatsappUploadFileResultResult.getData().getId());
        fileEntity.setNpath(npath);
        fileEntity.setType(type);
        fileEntity.setFileSize(fileMetaData.getSize());
        Integer count = fileTableManager.insert(fileEntity);
        LogUtils.info("WhatsappSendMsgServiceImpl.getMediaId.count={}", count);
        return Result.newSuccess(whatsappUploadFileResultResult.getData().getId());
    }
}
