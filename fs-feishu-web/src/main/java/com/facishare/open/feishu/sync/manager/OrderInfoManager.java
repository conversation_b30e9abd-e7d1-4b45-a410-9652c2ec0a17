package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.OrderInfoMapper;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaOrderInfoMapper;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * tb_order_info表管理器类
 * 
 * <AUTHOR>
 * @date 20220722
 */
@Component
@Deprecated
public class OrderInfoManager {
    @Autowired
    private OrderInfoMapper orderInfoMapper;;

    public OrderInfoEntity getEntity(String orderId) {
        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getOrderId, orderId);

        return orderInfoMapper.selectOne(wrapper);
    }

    /**
     * 获取最新的订单
     * 
     * @param corpId
     * @return
     */
    public OrderInfoEntity getLatestOrder(String corpId) {
        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getPaidCorpId, corpId);
        wrapper.orderByDesc(OrderInfoEntity::getEndTime);
        List<OrderInfoEntity> orderInfoEntityList = orderInfoMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(orderInfoEntityList)) {
            return null;
        }
        return orderInfoEntityList.get(0);
    }

    /**
     * 插入或更新订单信息表
     * 
     * @param entity
     * @return
     */
    public Integer insertOrUpdateOrderInfo(OrderInfoEntity entity) {
        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getPaidCorpId, entity.getPaidCorpId());
        wrapper.eq(OrderInfoEntity::getAppId, entity.getAppId());
        wrapper.eq(OrderInfoEntity::getOrderId, entity.getOrderId());
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectOne(wrapper);
        int count = 0;
        if (orderInfoEntity != null) {
            BeanUtils.copyProperties(entity, orderInfoEntity, "id");
            count = orderInfoMapper.updateById(orderInfoEntity);
            LogUtils.info("OrderInfoManager.updateOrderInfo,updateById,count={}", count);
        } else {
            count = orderInfoMapper.insert(entity);
            LogUtils.info("OrderInfoManager.updateOrderInfo,insert,count={}", count);
        }
        return count;
    }

    /**
     * 根据企业ID查询该企业的所有订单信息
     * 
     * @param corpId 企业ID
     * @return 该企业的所有订单列表，如果没有数据返回空列表
     */
    public List<OrderInfoEntity> getAllOrdersByCorpId(String corpId) {
        if (corpId == null || corpId.trim().isEmpty()) {
            LogUtils.warn("OrderInfoManager.getAllOrdersByCorpId,corpId is empty");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OrderInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInfoEntity::getPaidCorpId, corpId);
        // 按结束时间倒序排序，最新的订单在前面
//        wrapper.orderByDesc(OrderInfoEntity::getEndTime);

        List<OrderInfoEntity> orderList = orderInfoMapper.selectList(wrapper);
        LogUtils.info("OrderInfoManager.getAllOrdersByCorpId,corpId={},orderCount={}", corpId,
                orderList != null ? orderList.size() : 0);

        return orderList != null ? orderList : Collections.emptyList();
    }
}
