package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.UserDataList;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum;
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.PageUserData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.FeishuUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


@Slf4j
@Service("feishuUserService")
public class FeishuUserServiceImpl implements FeishuUserService {
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource
    private FeishuAppManager feishuAppManager;
    @Autowired
    private UrlManager urlManager;

    private int maxPageSize = 50; //飞书最大分页大小为50，只能调小，不能调大

    @Override
    public Result<UserData.User> createUser(String appId, UserData.User user) {
        //TODO 没有地方用，后续透传tenantid
        String url = "https://open.feishu.cn/open-apis/contact/v3/users";
        url+="?department_id_type=department_id&user_id_type=user_id";

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, null);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        Result<UserData> result = proxyHttpClient.postUrl(url, user, headerMap, new TypeReference<Result<UserData>>() {
        });

        if(result.isSuccess()) {
            return Result.newSuccess(result.getData().getUser());
        }
        return Result.newError(result.getCode(),result.getMsg());
    }

    @Override
    public Result<Void> deleteUser(String appId, String userId) {
        String url = "https://open.feishu.cn/open-apis/contact/v3/users/"+userId;
        url+="?user_id_type=user_id";

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, null);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        Result<Void> result = proxyHttpClient.deleteByJson(url, null, headerMap, new TypeReference<Result<Void>>() {
        });

        if(result.isSuccess()) {
            return Result.newSuccess();
        }
        return Result.newError(result.getCode(),result.getMsg());
    }

    @Override
    public Result<UserData.User> getUserInfo(String appId, String tenantKey, String userId) {
        Result<UserData.User> userInfo = getUserInfo(appId,
                tenantKey,
                userId,
                UserIdTypeEnum.open_id,
                DepartmentIdTypeEnum.open_department_id);
        if(userInfo.isSuccess()&&userInfo.getData()!=null){
            if(userInfo.getData().getMobile()!=null){
                String mobile=userInfo.getData().getMobile();
                userInfo.getData().setMobile(mobile.replace("+86", ""));
            }
        }
        return userInfo;
    }

    @Override
    public Result<UserData.User> getUserInfo(String appId, String tenantKey, String userId, UserIdTypeEnum userIdType, DepartmentIdTypeEnum departmentIdType) {

        String url = urlManager.getFeishuUrl(tenantKey, appId, FeishuUrlEnum.contact_v3_users_user_id.getUrl()) + userId;
        //替换url
        url=urlManager.getFeishuUrl(tenantKey,appId,url);
        url += "?user_id_type=" + userIdType;
        url += "&department_id_type=" + departmentIdType;

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.contact_v3_users_user_id.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        Result<UserData> result = proxyHttpClient.getUrl(url, headerMap, new TypeReference<Result<UserData>>() {
        });

        if(result.isSuccess()) {
            return Result.newSuccess(result.getData().getUser());
        }
        return Result.newError(result.getCode(),result.getMsg());
    }

    /**
     * 分页查询结果封装
     */
    public static class PageResult<T> {
        private List<T> items;
        private String pageToken;
        private boolean hasMore;

        public PageResult(List<T> items, String pageToken, boolean hasMore) {
            this.items = items;
            this.pageToken = pageToken;
            this.hasMore = hasMore;
        }

        public List<T> getItems() {
            return items;
        }

        public String getPageToken() {
            return pageToken;
        }

        public boolean isHasMore() {
            return hasMore;
        }
    }


    @Override
    public Result<List<UserData.User>> getUserList(String appId,
                                                   String tenantKey,
                                                   String departmentId,
                                                   UserIdTypeEnum userIdType,
                                                   DepartmentIdTypeEnum departmentIdType,
                                                   Integer pageSize) {
        Result<PageResult<UserData.User>> result = getUserListWithPage(appId,
                tenantKey,
                departmentId,
                userIdType,
                departmentIdType,
                pageSize,
                null);

        return result.isSuccess() ? Result.newSuccess(result.getData().getItems()) : Result.newError(result.getCode(), result.getMsg());
    }

    /**
     * 获取部门下的用户列表，支持分页查询，返回分页信息
     */
    public Result<PageResult<UserData.User>> getUserListWithPage(String appId,
                                                         String tenantKey,
                                                         String departmentId,
                                                         UserIdTypeEnum userIdType,
                                                         DepartmentIdTypeEnum departmentIdType,
                                                         Integer pageSize,
                                                         String pageToken) {

        // 构建URL
        String url = urlManager.getFeishuUrl(tenantKey, appId, FeishuUrlEnum.contact_v3_users_find_by_department.getUrl());
        url = urlManager.getFeishuUrl(tenantKey, appId, url);
        url += "?department_id=" + departmentId;
        url += "&user_id_type=" + (userIdType != null ? userIdType : UserIdTypeEnum.open_id);
        url += "&department_id_type=" + (departmentIdType != null ? departmentIdType : DepartmentIdTypeEnum.open_department_id);
        url += "&page_size=" + pageSize;
        if (pageToken != null && !pageToken.isEmpty()) {
            url += "&page_token=" + pageToken;
        }

        // 获取数据
        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if (!tenantAccessToken.isSuccess()) {
            log.error("Failed to get tenant access token, appId={}, tenantKey={}, error={}", appId, tenantKey, tenantAccessToken);
            return Result.newError(tenantAccessToken.getCode(), tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit,
                String.format(FeishuUrlEnum.contact_v3_users_find_by_department.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);

        Result<PageUserData> result = proxyHttpClient.getUrl(url, headerMap, new TypeReference<Result<PageUserData>>() {});
        if (!result.isSuccess()) {
            log.error("Failed to get user list, url={}, error={}", url, result);
            return Result.newError(result.getCode(), result.getMsg());
        }

        PageUserData pageData = result.getData();
        if (pageData == null) {
            log.error("PageUserData is null, url={}", url);
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
        }

        List<UserData.User> items = pageData.getItems();
        if (items == null) {
            items = new ArrayList<>();
        }

        return Result.newSuccess(new PageResult<>(
            items,
            pageData.getPageToken(),
            pageData.isHasMore()
        ));
    }

    /**
     * 获取部门下的所有用户（自动处理分页）
     */
    public Result<List<UserData.User>> getAllUserList(String appId,
                                                  String tenantKey,
                                                  String departmentId,
                                                  UserIdTypeEnum userIdType,
                                                  DepartmentIdTypeEnum departmentIdType) {


        List<UserData.User> allUsers = new ArrayList<>();
        String nextPageToken = null;
        int totalFetched = 0;

        
        do {
            try {
                Result<PageResult<UserData.User>> result = getUserListWithPage(appId,
                        tenantKey,
                        departmentId,
                        userIdType,
                        departmentIdType,
                        maxPageSize,
                        nextPageToken);
                if(!result.isSuccess()){
                    log.info("fail to page data:{}",result);
                   nextPageToken=null;
                }else{
                    PageResult<UserData.User> pageResult = result.getData();
                    if (pageResult.getItems() != null) {
                        allUsers.addAll(pageResult.getItems());
                        totalFetched += pageResult.getItems().size();
                        log.info("Fetched {} users from department {}, total fetched: {}",
                                pageResult.getItems().size(), departmentId, totalFetched);
                    }
                    nextPageToken = pageResult.isHasMore() ? pageResult.getPageToken() : null;
                }
            } catch (Exception e) {
                log.error("Unexpected error while fetching users, appId={}, departmentId={}", appId, departmentId, e);
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR);
            }
        } while (nextPageToken != null);
        
        log.info("Successfully fetched all {} users from department {}", totalFetched, departmentId);
        return Result.newSuccess(allUsers);
    }

    /**
     * 获取部门下的所有用户（简化版本，自动使用默认参数）
     */
    @Override
    public Result<List<UserData.User>> getAllDepartmentUsers(String appId, String tenantKey, String departmentId) {
        return getAllUserList(
            appId,
            tenantKey,
            departmentId,
            UserIdTypeEnum.open_id,
            DepartmentIdTypeEnum.open_department_id
        );
    }


    @Override
    public Result<List<UserData.User>> batchGetUserIds(String appId,
                                                       String tenantKey,
                                                       List<String> emails,
                                                       List<String> mobiles) {
        return batchGetUserIds(appId, tenantKey, UserIdTypeEnum.open_id, emails, mobiles);
    }

    @Override
    public Result<List<UserData.User>> batchGetUserIds(String appId,
                                                       String tenantKey,
                                                       UserIdTypeEnum userIdType,
                                                       List<String> emails,
                                                       List<String> mobiles) {
        String url = urlManager.getFeishuUrl(tenantKey,appId, FeishuUrlEnum.contact_v3_users_batch_get_id.getUrl());
        url += "?user_id_type=" + userIdType;

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.contact_v3_users_batch_get_id.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        HashMap<String, Object> bodyMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(emails)) {
            bodyMap.put("emails", emails);
        }
        if(CollectionUtils.isNotEmpty(mobiles)) {
            bodyMap.put("mobiles", mobiles);
        }
        Result<UserDataList> result = proxyHttpClient.postUrl(url, bodyMap, headerMap, new TypeReference<Result<UserDataList>>() {
        });

        if(result.isSuccess()) {
            return Result.newSuccess(result.getData().getUserList());
        }
        return Result.newError(result.getCode(),result.getMsg());
    }
}
