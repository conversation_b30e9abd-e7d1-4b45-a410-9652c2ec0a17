package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.mapper.CalendarEventMapper;
import com.facishare.open.feishu.syncapi.entity.CalendarEventEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CalendarEventManager {
    @Resource
    private CalendarEventMapper calendarEventMapper;

    public Integer insert(CalendarEventEntity entity) {
        int count = calendarEventMapper.insert(entity);
        LogUtils.info("CalendarEventManager.insert,count={}", count);
        return count;
    }

    public Integer update(CalendarEventEntity entity) {
        int count = calendarEventMapper.updateById(entity);
        LogUtils.info("CalendarEventManager.update,count={}", count);
        return count;
    }

    public CalendarEventEntity queryEntity(ChannelEnum channel, String fsEa, String objectId) {
        LambdaQueryWrapper<CalendarEventEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalendarEventEntity::getChannel, channel);
        wrapper.eq(CalendarEventEntity::getFsEa, fsEa);
        wrapper.eq(CalendarEventEntity::getObjectId, objectId);

        return calendarEventMapper.selectOne(wrapper);
    }

    public CalendarEventEntity queryEntity(ChannelEnum channel, String fsEa, String outEa, String objectId) {
        LambdaQueryWrapper<CalendarEventEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalendarEventEntity::getChannel, channel);
        wrapper.eq(CalendarEventEntity::getFsEa, fsEa);
        wrapper.eq(CalendarEventEntity::getOutEa, outEa);
        wrapper.eq(CalendarEventEntity::getObjectId, objectId);

        return calendarEventMapper.selectOne(wrapper);
    }

    public Integer deleteByObjectId(String channel, String fsEa, String objectId) {
        LambdaQueryWrapper<CalendarEventEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CalendarEventEntity::getChannel, channel);
        wrapper.eq(CalendarEventEntity::getFsEa, fsEa);
        wrapper.eq(CalendarEventEntity::getObjectId, objectId);
        int count = calendarEventMapper.delete(wrapper);
        LogUtils.info("CalendarEventManager.deleteByObjectId,count={}", count);
        return count;
    }

    /**
     * 分页查询日历事件
     * 

     * @param fsEa     企业账号
     * @param pageNum  页码，从1开始
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public Page<CalendarEventEntity> queryPage( String fsEa, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<CalendarEventEntity> wrapper = new LambdaQueryWrapper<>();

        if (fsEa != null) {
            wrapper.eq(CalendarEventEntity::getFsEa, fsEa);
        }


        Page<CalendarEventEntity> page = new Page<>(pageNum, pageSize);
        Page<CalendarEventEntity> result = calendarEventMapper.selectPage(page, wrapper);
        LogUtils.info("CalendarEventManager.queryPage,total={},current={},size={}", result.getTotal(),
                result.getCurrent(), result.getSize());
        return result;
    }

    /**
     * 高级分页查询日历事件
     * 
     * @param channel  渠道
     * @param fsEa     企业账号
     * @param outEa    外部企业账号
     * @param pageNum  页码，从1开始
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public Page<CalendarEventEntity> queryPageAdvanced(ChannelEnum channel, String fsEa, String outEa, Integer pageNum,
            Integer pageSize) {
        LambdaQueryWrapper<CalendarEventEntity> wrapper = new LambdaQueryWrapper<>();
        if (channel != null) {
            wrapper.eq(CalendarEventEntity::getChannel, channel);
        }
        if (fsEa != null) {
            wrapper.eq(CalendarEventEntity::getFsEa, fsEa);
        }
        if (outEa != null) {
            wrapper.eq(CalendarEventEntity::getOutEa, outEa);
        }

        // 按创建时间倒序排序
        wrapper.orderByDesc(CalendarEventEntity::getCreateTime);

        Page<CalendarEventEntity> page = new Page<>(pageNum, pageSize);
        Page<CalendarEventEntity> result = calendarEventMapper.selectPage(page, wrapper);
        LogUtils.info("CalendarEventManager.queryPageAdvanced,total={},current={},size={}", result.getTotal(),
                result.getCurrent(), result.getSize());
        return result;
    }
}
