package com.facishare.open.feishu.sync.job;


import com.facishare.open.feishu.sync.threadpool.ThreadPoolHelper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@JobHander(value = "RetryTodoHandler")
@Component
@Slf4j
public class RetryTodoHandler extends IJobHandler {

    @Resource
    private ExternalTodoMsgService externalTodoMsgService;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            log.info("scan RetryTodoHandler start the job!");
            //查询所有的企业
            OuterOaEnterpriseBindParams outerOaEnterpriseBindParams= OuterOaEnterpriseBindParams.builder().bindStatus(BindStatusEnum.normal).build();
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
            for(OuterOaEnterpriseBindEntity entity : enterpriseBindEntities) {
                log.info("scan RetryTodoHandler entity={}", entity);
                ThreadPoolHelper.retryTodoThreadPool.submit(() -> {
                    externalTodoMsgService.retryCrmTodo(entity);
                });
            }

            log.info("scan RetryTodoHandler end the job!");
            return new ReturnT(ReturnT.SUCCESS_CODE, "定时任务调用成功"); //ignorei18n
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }
}