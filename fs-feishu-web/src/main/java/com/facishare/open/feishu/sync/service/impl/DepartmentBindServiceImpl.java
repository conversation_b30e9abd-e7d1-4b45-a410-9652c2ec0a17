package com.facishare.open.feishu.sync.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.DepartmentBindManager;
import com.facishare.open.feishu.sync.manager.FsDepartmentInfoManager;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsDepartmentInfoDoc;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.DepartmentBindService;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.type.DepartmentStatus;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.mongodb.bulk.BulkWriteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.HashMap;

@Service("departmentBindService")
public class DepartmentBindServiceImpl implements DepartmentBindService {
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private FsDepartmentInfoManager fsDepartmentInfoManager;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private DepartmentBindManager departmentBindManager;
    @Resource
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;

    private final String FS_DEPARTMENT_DATA_CACHE_KEY = "fs_department_data_cache_key_";

    @Override
    public Result<List<DepartmentDto>> fsDepartmentInfoCache(String fsEa, DepartmentDto departmentDto) {
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<DepartmentDto> departments = null;
        //判断是否初次同步
        Long count = fsDepartmentInfoManager.countDocuments(fsEa);
        LogUtils.info("DepartmentBindServiceImpl.FsDepartmentInfoCache,count={}",count);
        if(ObjectUtils.isEmpty(departmentDto) || (count == null || count == 0)) {
            RLock rLock = redissonClient.getLock(FS_DEPARTMENT_DATA_CACHE_KEY + fsEa);
            //分布式锁
            try {
            boolean isGetLock = rLock.tryLock(60 * 5, 60 * 5, TimeUnit.SECONDS);
            LogUtils.info("DepartmentBindServiceImpl.fsDepartmentInfoCache,Asynchronous threading,isGetLock={}", isGetLock);
            if(isGetLock) {
                try {
                    //同步
                    com.facishare.open.order.contacts.proxy.api.result.Result<List<DepartmentDto>> allDepartmentDtoResult = fsDepartmentServiceProxy.getAllDepartmentDto(ei);
                    if(!allDepartmentDtoResult.isSuccess() || CollectionUtils.isEmpty(allDepartmentDtoResult.getData())) {
                        return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                    }
                    departments = allDepartmentDtoResult.getData();
                    //插入
                    List<FsDepartmentInfoDoc> docs = new LinkedList<>();
                    for(DepartmentDto dto : departments) {
                        FsDepartmentInfoDoc doc = new FsDepartmentInfoDoc();
                        doc.setFsEa(fsEa);
                        doc.setFsDepartmentId(dto.getDepartmentId());
                        doc.setStatus(dto.getStatus().getValue());
                        doc.setFsDepartmentInfo(new Gson().toJson(dto));
                        doc.setCreateTime(System.currentTimeMillis());
                        doc.setUpdateTime(System.currentTimeMillis());
                        docs.add(doc);
                    }
                    BulkWriteResult bulkWriteResult = fsDepartmentInfoManager.batchReplace(docs);
                    LogUtils.info("DepartmentBindServiceImpl.FsDepartmentInfoCache,batchReplace,bulkWriteResult={}",bulkWriteResult);
                } finally {
                    rLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
            return Result.newSuccess(departments);
        }
        //更新数据
        departments = new LinkedList<>();
        if(!departmentDto.getStatus().equals(DepartmentStatus.DELETE)
                && (StringUtils.isEmpty(departmentDto.getName())
                || ObjectUtils.isEmpty(departmentDto.getStatus()))) {
            com.facishare.open.order.contacts.proxy.api.result.Result<DepartmentDto> dtoResult = fsDepartmentServiceProxy.getDepartmentDto(ei, departmentDto.getDepartmentId());
            departmentDto = dtoResult.getData();
        }
        FsDepartmentInfoDoc doc = new FsDepartmentInfoDoc();
        doc.setFsEa(fsEa);
        doc.setFsDepartmentId(departmentDto.getDepartmentId());
        doc.setStatus(departmentDto.getStatus().getValue());
        doc.setFsDepartmentInfo(new Gson().toJson(departmentDto));
        doc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = fsDepartmentInfoManager.batchReplace(Lists.newArrayList(doc));
        LogUtils.info("DepartmentBindServiceImpl.FsDepartmentInfoCache,replace,bulkWriteResult={}",bulkWriteResult);
        departments.add(departmentDto);
        return Result.newSuccess(departments);
    }

    @Override
    public Result<OuterOaDepartmentBindEntity> queryDepData(ChannelEnum channel, String fsEa, String fsDepId, String outEa, String outDepId) {
        OuterOaDepartmentBindParams params = new OuterOaDepartmentBindParams();
        params.setChannel(channel);
        params.setFsEa(fsEa);
        params.setFsDepId(fsDepId);
        params.setOutEa(outEa);
        params.setOutDepId(outDepId);
        List<OuterOaDepartmentBindEntity> entities = outerOaDepartmentBindManager.getEntities(params);
        //TODO 补充appid
        if(CollectionUtils.isNotEmpty(entities)){
            return new Result<>(entities.get(0));
        }
        return new Result<>();
    }

    @Override
    public Result<Integer> insertDepData(DepartmentBindEntity entity) {
        departmentBindManager.insert(entity);
        return new Result<>(departmentBindManager.insert(entity));
        
    }
}
