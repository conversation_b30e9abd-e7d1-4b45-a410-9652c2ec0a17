package com.facishare.open.feishu.sync.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.syncapi.model.connect.AppConnectParams;
import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.Token.AppAccessTokenResult;
import com.facishare.open.feishu.syncapi.result.Token.TenantAccessTokenResult;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("feishuSelfBuildAppManager")
public class FeishuSelfBuildAppManager {

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private UrlManager urlManager;

    @ReloadableProperty("feishu_app_info")
    private String appInfo;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    /**
     * 自建应用获取token
     * @param appId
     * @return
     */
    public Result<String> getTenantAccessToken(String appId,String outEa) {
        String key = "feishu-" + appId + "-tenantAccessToken";
        String tenantAccessToken = redisDataSource.getRedisClient().get(key);
        if (tenantAccessToken != null){
            return Result.newSuccess(tenantAccessToken);
        }
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams= OuterOaEnterpriseBindParams.builder()
                .outEa(outEa)
                .appId(appId)
                .build();
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager.selectEntityLimitOne(outerOaEnterpriseBindParams);

        if(ObjectUtils.isNotEmpty(outerOaEnterpriseBindEntity)&&outerOaEnterpriseBindEntity.getConnectInfo()!=null){
            FeiShuConnectorVo feiShuConnectorVo=JSONObject.parseObject(outerOaEnterpriseBindEntity.getConnectInfo(),FeiShuConnectorVo.class);
            String appSecret = feiShuConnectorVo.getAppSecret();
            String url = urlManager.getFeishuUrl(outerOaEnterpriseBindEntity.getOutEa(), appId, FeishuUrlEnum.auth_v3_tenant_access_token_internal.getUrl());
            TenantAccessTokenResult result = getTenantAccessTokenResult(appId, appSecret, url);
            if(result.getCode()!=0) {
                return new Result<>(result.getCode(),result.getMsg(),null);
            }
            String newTenantAccessToken = result.getTenant_access_token();
            redisDataSource.getRedisClient().set(key, newTenantAccessToken);

            //access token有效期是2小时，提前2分钟获取token
            long expire = result.getExpire() - 2 * 60L;
            if (expire < 0) {
                expire = result.getExpire();
            }
            redisDataSource.getRedisClient().expire(key, expire);
            return Result.newSuccess(newTenantAccessToken);
        }
        return Result.newErrorByI18n(ResultCodeEnum.SYSTEM_ERROR);


    }

    private TenantAccessTokenResult getTenantAccessTokenResult(String appId, String appSecret, String url) {
        Map<String,String> body = new HashMap<>();
        body.put("app_id", appId);
        body.put("app_secret", appSecret);

        Map<String,String> headerMap = new HashMap<>();

        TenantAccessTokenResult result = proxyHttpClient.postUrl(url,body, headerMap, new TypeReference<TenantAccessTokenResult>() {
        });
        return result;
    }

    public Result<String> initTenantToken(String appId,String appSecret,String domain) {
        String url = urlManager.getFeishuUrlByPointDomain( FeishuUrlEnum.auth_v3_tenant_access_token_internal.getUrl(),domain);
        TenantAccessTokenResult tenantAccessTokenResult = getTenantAccessTokenResult(appId, appSecret, url);
        if(tenantAccessTokenResult.getCode()!=0){
            return new Result<>(tenantAccessTokenResult.getCode(),tenantAccessTokenResult.getMsg(),null);
        }
        return Result.newSuccess(tenantAccessTokenResult.getTenant_access_token());
    }


    /**
     * dataCenterId可能为空
     * @param appId
     * @param dataCenterId
     * @return
     */
    public Result<String> getAppAccessToken(String appId,String dataCenterId) {
        String key = "feishu-" + appId + "-appAccessToken";
        String tenantAccessToken = redisDataSource.getRedisClient().get(key);
        if (tenantAccessToken != null){
            return Result.newSuccess(tenantAccessToken);
        }
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams= OuterOaEnterpriseBindParams.builder()
                .appId(appId)
                .id(dataCenterId)
                .build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(CollectionUtils.isEmpty(entities)){
            return Result.newError(ResultCodeEnum.IDENTITY_CHECK_ERROR);
        }
        OuterOaEnterpriseBindEntity enterpriseBindByAppId = entities.get(0);
        if(ObjectUtils.isNotEmpty(enterpriseBindByAppId)&&enterpriseBindByAppId.getConnectInfo()!=null){
            OuterOaAppInfoParams outerOaAppInfoParams=OuterOaAppInfoParams.builder()
                    .outEa(enterpriseBindByAppId.getOutEa())
                    .appId(appId)
                    .build();
            OuterOaAppInfoEntity outerOaAppInfoEntity = outerOaAppInfoManager.getEntity(outerOaAppInfoParams);
            if(outerOaAppInfoEntity==null){
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }
            AppConnectParams appConnectParams= JSONObject.parseObject(outerOaAppInfoEntity.getAppInfo(), AppConnectParams.class);
            String appSecret = appConnectParams.getAppSecret();

            String url = urlManager.getFeishuUrl(null, appId, FeishuUrlEnum.auth_v3_app_access_token_internal.getUrl());
            Map<String,String> body = new HashMap<>();
            body.put("app_id",appId);
            body.put("app_secret",appSecret);

            Map<String,String> headerMap = new HashMap<>();

            AppAccessTokenResult result = proxyHttpClient.postUrl(url,body, headerMap, new TypeReference<AppAccessTokenResult>() {
            });
            if(result.getCode()!=0) {
                return new Result<>(result.getCode(),result.getMsg(),null);
            }
            String newAppAccessToken = result.getApp_access_token();
            redisDataSource.getRedisClient().set(key, newAppAccessToken);

            //access token有效期是2小时，提前2分钟获取token
            long expire = result.getExpire() - 2 * 60L;
            if (expire < 0) {
                expire = result.getExpire();
            }
            redisDataSource.getRedisClient().expire(key, expire);
            return Result.newSuccess(newAppAccessToken);
        }
        return Result.newErrorByI18n(ResultCodeEnum.SYSTEM_ERROR);
    }

    public void clearTenantAccessToken(String appId,String tenantKey) {
        String key = "feishu-" + appId + "-tenantAccessToken";
        redisDataSource.getRedisClient().del(key);
    }
}
