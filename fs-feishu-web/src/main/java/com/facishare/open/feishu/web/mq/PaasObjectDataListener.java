package com.facishare.open.feishu.web.mq;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.data.ObjectDataMqData;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.feishu.web.template.inner.calendar.FeishuCreateCalendarTemplate;
import com.facishare.open.feishu.web.template.inner.calendar.FeishuDeleteCalendarTemplate;
import com.facishare.open.feishu.web.template.inner.calendar.FeishuUpdateCalendarTemplate;
import com.facishare.open.feishu.web.template.model.CalendarObjModel;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.consumer.listener.*;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("paasObjectDataListener")
public class PaasObjectDataListener implements MessageListenerOrderly {
    @Resource
    private FeishuCreateCalendarTemplate feishuCreateCalendarTemplate;
    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private FeishuUpdateCalendarTemplate feishuUpdateCalendarTemplate;
    @Resource
    private FeishuDeleteCalendarTemplate feishuDeleteCalendarTemplate;

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
        for (MessageExt msg : list) {
            try {
                if(msg.getTags()==null) {
                    LogUtils.info("PaasObjectDataListener.consumeMessage,msg.getTags()={}",msg.getTags());
                    continue;
                }
                if(msg.getTags().equals("object-data-tag") || msg.getTags().equals("batch-object-data-tag")){
                    ObjectDataMqData objectDataMqData = JSON.parseObject(msg.getBody(), ObjectDataMqData.class);

                    if(objectDataMqData==null || objectDataMqData.getBody()==null) {
                        LogUtils.info("PaasObjectDataListener.consumeMessage,objectDataMqData={}",objectDataMqData);
                        continue;
                    }

                    for(ObjectDataMqData.EventObject eventObject : objectDataMqData.getBody()) {
                        String objName = eventObject.getEntityId();
                        String fsEi = eventObject.getContext().getTenantId();
                        TraceUtils.initTraceId(msg.getMsgId() + "_" + msg.getQueueId() + "_" + fsEi);
                        String objectId = eventObject.getObjectId();

                        switch (objName) {
                            case "ScheduleObj":
                                int ei = Integer.parseInt(fsEi);
                                String fsEa = eieaConverter.enterpriseIdToAccount(ei);

                                //查询所有绑定关系
                                List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), fsEa,null);
                                if (CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
                                    break;
                                }

                                for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByFsEaChanelEnums) {
                                    BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
                                    if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_SCHEDULE)){
                                        LogUtils.info("enterprise not CRM_SCHEDULE support todo:{}",enterpriseBindEntity.getFsEa());
                                        continue;
                                    }

                                    CalendarObjModel calendarObjModel = new CalendarObjModel();
                                    calendarObjModel.setEnterpriseBindEntity(enterpriseBindEntity);
                                    calendarObjModel.setObjectId(objectId);
                                    calendarObjModel.setEventType(objectDataMqData.getOp());
                                    try {
                                        if (objectDataMqData.isInsterOp()) {
                                            feishuCreateCalendarTemplate.execute(calendarObjModel);
                                        } else if (objectDataMqData.isUpdateOp()) {
                                            feishuUpdateCalendarTemplate.execute(calendarObjModel);
                                        } else if (objectDataMqData.isInvalidOp() || objectDataMqData.isDeleteOp()) {
                                            feishuDeleteCalendarTemplate.execute(calendarObjModel);
                                        } else {
                                            LogUtils.info("PaasObjectDataListener.consumeMessage,fsEa={},op={}", fsEa, objectDataMqData.getOp());
                                        }
                                    } catch (Exception e) {
                                        LogUtils.info("PaasObjectDataListener.consumeMessage,exception={}", e.getMessage(), e);
                                    }
                                }
                            default:
                                break;
                        }
                    }
                }
            } catch (Exception e) {
                LogUtils.info("PaasObjectDataListener.consumeMessage,exception={}", e.getMessage(), e);
            }
        }
        return ConsumeOrderlyStatus.SUCCESS;
    }
}
