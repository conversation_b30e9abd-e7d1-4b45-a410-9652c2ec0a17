package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.mapper.ExternalTodoInstanceMapper;
import com.facishare.open.feishu.syncapi.entity.ExternalTodoInstanceEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
// IgnoreI18nFile
public class ExternalTodoInstanceManager {
    @Resource
    private ExternalTodoInstanceMapper externalTodoInstanceMapper;

    public Integer insert(ExternalTodoInstanceEntity entity) {
        int count = externalTodoInstanceMapper.insert(entity);
        LogUtils.info("ExternalTodoInstanceManager.insert,count={}", count);
        return count;
    }

    public Integer update(ExternalTodoInstanceEntity entity) {
        int count = externalTodoInstanceMapper.updateById(entity);
        LogUtils.info("ExternalTodoInstanceManager.update,count={}", count);
        return count;
    }

    public ExternalTodoInstanceEntity queryEntity(String fsEa, String outEa, String sourceId) {
        LambdaQueryWrapper<ExternalTodoInstanceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalTodoInstanceEntity::getFsEa, fsEa);
        wrapper.eq(ExternalTodoInstanceEntity::getOutEa, outEa);
        wrapper.eq(ExternalTodoInstanceEntity::getSourceId, sourceId);
        return externalTodoInstanceMapper.selectOne(wrapper);
    }

    /**
     * 分页查询外部待办实例
     *
     * @param fsEa     纷享企业账号
     * @param pageNo   页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public IPage<ExternalTodoInstanceEntity> pageQuery(String fsEa, Integer pageNo, Integer pageSize) {
        if (StringUtils.isEmpty(fsEa)) {
            throw new IllegalArgumentException("fsEa不能为空");
        }
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 构建分页对象
        Page<ExternalTodoInstanceEntity> page = new Page<>(pageNo, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<ExternalTodoInstanceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalTodoInstanceEntity::getFsEa, fsEa);

        // 按创建时间倒序排序
        wrapper.orderByDesc(ExternalTodoInstanceEntity::getCreateTime);

        try {
            // 执行分页查询
            IPage<ExternalTodoInstanceEntity> result = externalTodoInstanceMapper.selectPage(page, wrapper);
            LogUtils.info("ExternalTodoInstanceManager.pageQuery,fsEa={},pageNo={},pageSize={},total={}", fsEa, pageNo,
                    pageSize, result.getTotal());
            return result;
        } catch (Exception e) {
            LogUtils.error("分页查询外部待办实例失败", e, "fsEa", fsEa, "pageNo", pageNo, "pageSize", pageSize);
            throw new RuntimeException("分页查询外部待办实例失败", e);
        }
    }

    /**
     * 分页查询外部待办实例
     *
     * @param fsEa       纷享企业账号
     * @param pageNo     页码（从1开始）
     * @param pageSize   每页大小
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 分页结果
     */
    public IPage<ExternalTodoInstanceEntity> pageQueryBetweenTime(String fsEa, Integer pageNo, Integer pageSize, 
            Date startTime, Date endTime) {
        if (StringUtils.isEmpty(fsEa)) {
            throw new IllegalArgumentException("fsEa不能为空");
        }
        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (startTime == null) {
            throw new IllegalArgumentException("startTime不能为空");
        }
        if (endTime == null) {
            throw new IllegalArgumentException("endTime不能为空");
        }

        // 构建分页对象
        Page<ExternalTodoInstanceEntity> page = new Page<>(pageNo, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<ExternalTodoInstanceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExternalTodoInstanceEntity::getFsEa, fsEa);
        
        // 添加时间范围条件
        wrapper.ge(ExternalTodoInstanceEntity::getCreateTime, startTime);
        wrapper.le(ExternalTodoInstanceEntity::getCreateTime, endTime);

        // 按创建时间倒序排序
        wrapper.orderByDesc(ExternalTodoInstanceEntity::getCreateTime);

        try {
            // 执行分页查询
            IPage<ExternalTodoInstanceEntity> result = externalTodoInstanceMapper.selectPage(page, wrapper);
            LogUtils.info("ExternalTodoInstanceManager.pageQueryBetweenTime,fsEa={},pageNo={},pageSize={},startTime={},endTime={},total={}", 
                    fsEa, pageNo, pageSize, startTime, endTime, result.getTotal());
            return result;
        } catch (Exception e) {
            LogUtils.error("分页查询外部待办实例失败", e, "fsEa", fsEa, "pageNo", pageNo, "pageSize", pageSize,
                    "startTime", startTime, "endTime", endTime);
            throw new RuntimeException("分页查询外部待办实例失败", e);
        }
    }
}
