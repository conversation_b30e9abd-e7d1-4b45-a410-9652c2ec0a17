package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarDetail;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEvent;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEventAddAttendeeDetail;
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEventDelAttendeeDetail;
import com.facishare.open.feishu.syncapi.result.*;
import com.facishare.open.feishu.syncapi.service.ExternalCalendarService;
import com.facishare.open.feishu.syncapi.service.FeishuCalendarService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaCalendarTemplateEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaScheduleBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaCalendarTemplateManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaScheduleBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaCalendarTemplateParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaCalendarTemplateStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaScheduleBindStatusEnum;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("externalCalendarService")
public class ExternalCalendarServiceImpl implements ExternalCalendarService {

    @Resource
    private FeishuCalendarService feishuCalendarService;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaCalendarTemplateManager outerOaCalendarTemplateManager;
    @Autowired
    private OuterOaScheduleBindManager outerOaScheduleBindManager;

    @Override
    public Result<Void> createExternalCalendar(OuterOaEnterpriseBindEntity enterpriseBindEntity, ObjectData objectData) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        String objectId = objectData.getId();
        ChannelEnum channelEnum= enterpriseBindEntity.getChannel();

        @SuppressWarnings("unchecked")
        Map<String, Object> participants = (Map<String, Object>) objectData.get("participants");
        // 现在获取键为"employees"的值，这应该是一个List<String>
        @SuppressWarnings("unchecked")
        List<String> employees = (List<String>) participants.get("employees");

        //人员是否有绑定关系
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByDcId(enterpriseBindEntity.getId(), employees, null);

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            return Result.newSuccess();
        }
        //是否已经保存了日历
        OuterOaCalendarTemplateParams outerOaCalendarTemplateParams= new OuterOaCalendarTemplateParams();
        outerOaCalendarTemplateParams.setOutEa(outEa);
        outerOaCalendarTemplateParams.setChannel(channelEnum);
        List<OuterOaCalendarTemplateEntity> entities = outerOaCalendarTemplateManager.getEntities(outerOaCalendarTemplateParams);
        OuterOaCalendarTemplateEntity calendarEntity = null;
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendar2,calendarEntity={}", entities);
        if(ObjectUtils.isEmpty(entities)) {
            Result<OuterOaCalendarTemplateEntity> calendarEntityResult = createExternalCalendarDetail(outEa, appId,channelEnum);
            if(!calendarEntityResult.isSuccess()) {
                LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendar2,calendarEntityResult={}", calendarEntityResult);
                return Result.newError(calendarEntityResult.getCode(), calendarEntityResult.getMsg());
            }
            calendarEntity = calendarEntityResult.getData();
        }else{
            calendarEntity = entities.get(0);
        }
        String calendarId = calendarEntity.getCalendarId();
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendar2,calendarId={}", calendarId);
        //查看是否有日程绑定
//        CalendarEventEntity calendarEventEntity = calendarEventManager.queryEntity(enterpriseBindEntity.getChannel(), fsEa, outEa, objectId);
        OuterOaScheduleBindEntity outerOaScheduleBindEntity = outerOaScheduleBindManager.queryOutScheduleId(fsEa, outEa, objectId);
        if(ObjectUtils.isNotEmpty(outerOaScheduleBindEntity)) {
            return updateExternalCalendar(enterpriseBindEntity, objectData);
        }

        //新增日程
        ExternalCalendarEvent externalCalendarEvent = new ExternalCalendarEvent();
        @SuppressWarnings("unchecked")
        Map<String, Object> content = (Map<String, Object>) objectData.get("content");
        String summary = null;
        String description = null;
        if(content.containsKey("text")) {
            summary = String.valueOf(content.get("text"));
            if(summary.length() >= 100) {
                summary = summary.substring(0, 97) + "...";
            }
            description = String.valueOf(content.get("text"));
            if(description.length() >= 40960) {
                description = description.substring(0, 40957) + "...";
            }
        }
        externalCalendarEvent.setSummary(summary);
        externalCalendarEvent.setDescription(description);
        String beginTimeString = String.valueOf(objectData.get("begin_time"));
        long beginTime = Long.parseLong(beginTimeString);
        Long startTime = beginTime / 1000;
        ExternalCalendarEvent.TimeInfo startTimeInfo = new ExternalCalendarEvent.TimeInfo();
        startTimeInfo.setTimestamp(String.valueOf(startTime));
        externalCalendarEvent.setStartTime(startTimeInfo);
        Long endTime = Long.parseLong(String.valueOf(objectData.get("end_time"))) / 1000;
        ExternalCalendarEvent.TimeInfo endTimeInfo = new ExternalCalendarEvent.TimeInfo();
        endTimeInfo.setTimestamp(String.valueOf(endTime));
        externalCalendarEvent.setEndTime(endTimeInfo);
        externalCalendarEvent.setAttendeeAbility(CalendarEventAttendeeAbilityEnum.CAN_SEE_OTHERS.getCode());
//        ExternalCalendarEvent.Location location = new ExternalCalendarEvent.Location();
//        location.setName("大冲冲");
//        externalCalendarEvent.setLocation(location);
        externalCalendarEvent.setColor(-1);
        ExternalCalendarEvent.VChat vChat = new ExternalCalendarEvent.VChat();
        vChat.setVcType(CalendarEventVchatVcTypeEnum.NO_MEETING.getCode());
        externalCalendarEvent.setVchat(vChat);
//        Boolean isPrivate = Boolean.valueOf(String.valueOf(objectData.get("is_private")));
//        String visibility = CalendarEventVisibilityEnum.PUBLIC.getCode();
//        if(isPrivate) {
//            visibility = CalendarEventVisibilityEnum.PRIVATE.getCode();
//        }
        externalCalendarEvent.setVisibility(CalendarEventVisibilityEnum.PRIVATE.getCode());
        externalCalendarEvent.setFreeBusyStatus(CalendarEventFreeBusyStatusEnum.BUSY.getCode());
//        if(ObjectUtils.isNotEmpty(objectData.get("remind_info"))) {
//            @SuppressWarnings("unchecked")
//            List<Map<String, Object>> remindSettings = (List<Map<String, Object>>) objectData.get("remind_info");
//            if(CollectionUtils.isNotEmpty(remindSettings)) {
//                List<ExternalCalendarEvent.Reminder> reminders = new LinkedList<>();
//                for(Map<String, Object> remindSetting : remindSettings) {
//                    long tValue = (long) remindSetting.get("T");
//                    long time = beginTime - tValue;
//                    if(time != 0) {
//                        time = time / (1000 * 60);
//                    }
//                    ExternalCalendarEvent.Reminder reminder = new ExternalCalendarEvent.Reminder();
//
//                    reminder.setMinutes((int)time);
//                    reminders.add(reminder);
//                }
//                externalCalendarEvent.setReminders(reminders);
//            }
//        }
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,externalCalendarEvent={}", externalCalendarEvent);
        Result<ExternalCalendarEventResult> serviceCalendarEventResult = feishuCalendarService.createCalendarEvent(appId, outEa, calendarId, externalCalendarEvent);
        if(!serviceCalendarEventResult.isSuccess()) {
            LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,serviceCalendarEventResult={}", serviceCalendarEventResult);
            return Result.newError(ResultCodeEnum.CREATE_CALENDAR_EVENT_ERROR.getCode(), serviceCalendarEventResult.getMsg());
        }

        //保存日程
        String eventId = serviceCalendarEventResult.getData().getEvent().getEventId();
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,calendarEvent,eventId={}", eventId);

        //添加参与人
        @SuppressWarnings("unchecked")
        // 假设列表确实包含字符串
        List<String> ownerList = (List<String>) objectData.get("owner");

        Map<String, String> usersMap = employeeBindEntities.stream()
                .collect(Collectors.toMap(
                        OuterOaEmployeeBindEntity::getOutEmpId,
                        OuterOaEmployeeBindEntity::getFsEmpId,
                        (existingValue, newValue) -> existingValue));

        for (int i = 0; i < employeeBindEntities.size(); i += 1000) {
            int end = Math.min(employeeBindEntities.size(), i + 1000);
            List<OuterOaEmployeeBindEntity> batch = employeeBindEntities.subList(i, end);
            ExternalCalendarEventAddAttendeeDetail externalCalendarEventAddAttendeeDetail = new ExternalCalendarEventAddAttendeeDetail();
            List<ExternalCalendarEventAddAttendeeDetail.Attendee> attendees = new LinkedList<>();
            for(OuterOaEmployeeBindEntity employeeBindEntity : batch) {
                ExternalCalendarEventAddAttendeeDetail.Attendee attendee = new ExternalCalendarEventAddAttendeeDetail.Attendee();
                attendee.setType(CalendarEventAttendeeTypeEnum.USER.getCode());
                attendee.setUserId(employeeBindEntity.getOutEmpId());
                if(ownerList.contains(employeeBindEntity.getFsEmpId())) {
                    attendee.setOperateId(employeeBindEntity.getOutEmpId());
                }
                attendees.add(attendee);
            }
            externalCalendarEventAddAttendeeDetail.setAttendees(attendees);
            LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,calendarEvent,externalCalendarEventAddAttendeeDetail={}", externalCalendarEventAddAttendeeDetail);
            Result<ExternalCalendarEventAddAttendeeResult> externalCalendarEventAddAttendeeResultResult = feishuCalendarService.addCalendarEventAttendees(appId, outEa, calendarId, eventId, externalCalendarEventAddAttendeeDetail);
            if(!externalCalendarEventAddAttendeeResultResult.isSuccess()) {
                LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,calendarEvent,externalCalendarEventAddAttendeeResultResult={}", externalCalendarEventAddAttendeeResultResult);
                return Result.newError(ResultCodeEnum.ADD_CALENDAR_EVENT_ATTENDEE_ERROR.getCode(), externalCalendarEventAddAttendeeResultResult.getMsg());
            }

            //保存日程参与人
            //飞书直接返回全部的人员，最后一次再插入
            if(employeeBindEntities.size() == end) {
                List<OuterOaScheduleBindEntity> scheduleBindEntities=Lists.newArrayList();
                for(ExternalCalendarEventAddAttendeeResult.Attendee attendee : externalCalendarEventAddAttendeeResultResult.getData().getAttendees()) {
                    OuterOaScheduleBindEntity scheduleBindEntity=new OuterOaScheduleBindEntity();
                    scheduleBindEntity.setId(IdGenerator.get());
                    scheduleBindEntity.setOutScheduleId(eventId);
                    scheduleBindEntity.setOutEa(outEa);
                    scheduleBindEntity.setFsEa(fsEa);
                    scheduleBindEntity.setChannel(channelEnum);
                    scheduleBindEntity.setFsScheduleId(objectId);
                    scheduleBindEntity.setAppId(appId);
                    scheduleBindEntity.setDcId(enterpriseBindEntity.getId());
                    scheduleBindEntity.setCreateTime(System.currentTimeMillis());
                    scheduleBindEntity.setUpdateTime(System.currentTimeMillis());
                    scheduleBindEntity.setOutUserId(attendee.getUserId());
                    scheduleBindEntity.setStatus(OuterOaScheduleBindStatusEnum.normal);
                    scheduleBindEntities.add(scheduleBindEntity);


                }
                Integer attendeeCount = outerOaScheduleBindManager.batchUpsert(scheduleBindEntities);
                LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,calendarEvent,attendeeCount={}", attendeeCount);
            }
        }

        return Result.newSuccess();
    }

    private Result<OuterOaCalendarTemplateEntity> createExternalCalendarDetail(String outEa, String appId,ChannelEnum channelEnum) {
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail,outEa={},appId={}", outEa, appId);
        //创建日历
        ExternalCalendarDetail externalCalendarDetail = new ExternalCalendarDetail();
        externalCalendarDetail.setSummary("纷享销客日历"); //ignorei18n
        externalCalendarDetail.setColor(-1);
        externalCalendarDetail.setPermissions(CalendarPermissionEnum.PRIVATE.getCode());
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail,externalCalendarDetail={}", externalCalendarDetail);
        Result<ExternalCalendarResult> calendarResultResult = feishuCalendarService.createCalendars(appId, outEa, externalCalendarDetail);
        if(!calendarResultResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.CREATE_CALENDAR_ERROR.getCode(), calendarResultResult.getMsg());
        }
        //保存数据
        OuterOaCalendarTemplateEntity entity = new OuterOaCalendarTemplateEntity();
        entity.setChannel(channelEnum);
        entity.setOutEa(outEa);
        entity.setCalendarId(calendarResultResult.getData().getCalendar().getCalendarId());
        entity.setStatus(OuterOaCalendarTemplateStatusEnum.normal);
        entity.setId(IdGenerator.get());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setOutUserId("0");
        Integer count = outerOaCalendarTemplateManager.upsert(entity);
        LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail,count={}", count);
        return Result.newSuccess(entity);
    }

    @Override
    public Result<Void> updateExternalCalendar(OuterOaEnterpriseBindEntity enterpriseBindEntity, ObjectData objectData) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        String objectId = objectData.getId();
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();

        //查看是否有日程绑定
//        CalendarEventEntity calendarEventEntity = calendarEventManager.queryEntity(ChannelEnum.feishu, fsEa, outEa, objectId);
        OuterOaScheduleBindEntity outerOaScheduleBindEntity = outerOaScheduleBindManager.queryOutScheduleId(fsEa, outEa, objectId);

        if(ObjectUtils.isEmpty(outerOaScheduleBindEntity)) {
            return createExternalCalendar(enterpriseBindEntity, objectData);
        }

        String calendarEventId = outerOaScheduleBindEntity.getOutScheduleId();

        //查看是否已被删除
        boolean isDeleted = (Boolean) objectData.get("is_deleted");
        if(isDeleted) {
            LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,isDeleted={}", isDeleted);
            return Result.newSuccess();
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> participants = (Map<String, Object>) objectData.get("participants");
        // 现在获取键为"employees"的值，这应该是一个List<String>
        @SuppressWarnings("unchecked")
        List<String> employees = (List<String>) participants.get("employees");

        //人员是否有绑定关系
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByDcId(enterpriseBindEntity.getId(), employees, null);

        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            //无绑定关系，把现有人员全部删除，日程不删

            return Result.newSuccess();
        }
        //是否已经保存了日历
//        CalendarEntity calendarEntity = calendarManager.queryEntity(ChannelEnum.feishu, outEa);
        OuterOaCalendarTemplateParams outerOaCalendarTemplateParams= new OuterOaCalendarTemplateParams();
        outerOaCalendarTemplateParams.setOutEa(outEa);
        outerOaCalendarTemplateParams.setChannel(channelEnum);
        List<OuterOaCalendarTemplateEntity> entities = outerOaCalendarTemplateManager.getEntities(outerOaCalendarTemplateParams);

        LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarEntity={}", entities);
        if(ObjectUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.UPDATE_CALENDAR_EVENT_ERROR);
        }
        String calendarId = entities.get(0).getCalendarId();
        LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarId={}", calendarId);
        //新增日程
        ExternalCalendarEvent externalCalendarEvent = new ExternalCalendarEvent();
        @SuppressWarnings("unchecked")
        Map<String, Object> content = (Map<String, Object>) objectData.get("content");
        String summary = null;
        String description = null;
        if(content.containsKey("text")) {
            summary = String.valueOf(content.get("text"));
            if(summary.length() >= 100) {
                summary = summary.substring(0, 97) + "...";
            }
            description = String.valueOf(content.get("text"));
            if(description.length() >= 40960) {
                description = description.substring(0, 40957) + "...";
            }
        }
        externalCalendarEvent.setSummary(summary);
        externalCalendarEvent.setDescription(description);
        String beginTimeString = String.valueOf(objectData.get("begin_time"));
        long beginTime = Long.parseLong(beginTimeString);
        Long startTime = beginTime / 1000;
        ExternalCalendarEvent.TimeInfo startTimeInfo = new ExternalCalendarEvent.TimeInfo();
        startTimeInfo.setTimestamp(String.valueOf(startTime));
        externalCalendarEvent.setStartTime(startTimeInfo);
        Long endTime = Long.parseLong(String.valueOf(objectData.get("end_time"))) / 1000;
        ExternalCalendarEvent.TimeInfo endTimeInfo = new ExternalCalendarEvent.TimeInfo();
        endTimeInfo.setTimestamp(String.valueOf(endTime));
        externalCalendarEvent.setEndTime(endTimeInfo);
        externalCalendarEvent.setAttendeeAbility(CalendarEventAttendeeAbilityEnum.CAN_SEE_OTHERS.getCode());
//        ExternalCalendarEvent.Location location = new ExternalCalendarEvent.Location();
//        location.setName("大冲冲");
//        externalCalendarEvent.setLocation(location);
        externalCalendarEvent.setColor(-1);
        ExternalCalendarEvent.VChat vChat = new ExternalCalendarEvent.VChat();
        vChat.setVcType(CalendarEventVchatVcTypeEnum.NO_MEETING.getCode());
        externalCalendarEvent.setVchat(vChat);
//        Boolean isPrivate = Boolean.valueOf(String.valueOf(objectDataResult.getData().get("is_private")));
//        String visibility = CalendarEventVisibilityEnum.PUBLIC.getCode();
//        if(isPrivate) {
//            visibility = CalendarEventVisibilityEnum.PRIVATE.getCode();
//        }
        externalCalendarEvent.setVisibility(CalendarEventVisibilityEnum.PRIVATE.getCode());
        externalCalendarEvent.setFreeBusyStatus(CalendarEventFreeBusyStatusEnum.BUSY.getCode());
//        if(ObjectUtils.isNotEmpty(objectDataResult.getData().get("remind_info"))) {
//            @SuppressWarnings("unchecked")
//            List<Map<String, Object>> remindSettings = (List<Map<String, Object>>) objectDataResult.getData().get("remind_info");
//            if(CollectionUtils.isNotEmpty(remindSettings)) {
//                List<ExternalCalendarEvent.Reminder> reminders = new LinkedList<>();
//                for(Map<String, Object> remindSetting : remindSettings) {
//                    long tValue = (long) remindSetting.get("T");
//                    long time = beginTime - tValue;
//                    if(time != 0) {
//                        time = time / (1000 * 60);
//                    }
//                    ExternalCalendarEvent.Reminder reminder = new ExternalCalendarEvent.Reminder();
//
//                    reminder.setMinutes((int)time);
//                    reminders.add(reminder);
//                }
//                externalCalendarEvent.setReminders(reminders);
//            }
//        }
        LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,externalCalendarEvent={}", externalCalendarEvent);
        Result<ExternalCalendarEventResult> serviceCalendarEventResult = feishuCalendarService.updateCalendarEvent(appId, outEa, calendarId, calendarEventId, externalCalendarEvent);
        if(!serviceCalendarEventResult.isSuccess()) {
            LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,serviceCalendarEventResult={}", serviceCalendarEventResult);
            return Result.newError(ResultCodeEnum.CREATE_CALENDAR_EVENT_ERROR.getCode(), serviceCalendarEventResult.getMsg());
        }

        //查询人员
        List<OuterOaScheduleBindEntity> scheduleBindEntities = outerOaScheduleBindManager.queryListByOutScheduleId(fsEa, outEa, calendarEventId);
        Map<String, OuterOaScheduleBindEntity> calendarEventAttendeeMap = scheduleBindEntities.stream()
                .collect(Collectors.toMap(OuterOaScheduleBindEntity::getOutUserId, Function.identity(), (existing, replacement) -> existing));
        //新增人员
        List<OuterOaEmployeeBindEntity> addEmployeeBindEntities = new LinkedList<>();
        for(OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            if(calendarEventAttendeeMap.containsKey(employeeBindEntity.getOutEmpId())) {
                calendarEventAttendeeMap.remove(employeeBindEntity.getOutEmpId());
                continue;
            }
            addEmployeeBindEntities.add(employeeBindEntity);
        }
        if(CollectionUtils.isNotEmpty(addEmployeeBindEntities)) {
            LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,addEmployeeBindEntities={}", addEmployeeBindEntities);
            //添加参与人
            @SuppressWarnings("unchecked") // 假设列表确实包含字符串
            List<String> ownerList = (List<String>) objectData.get("owner");

            //参与人处理
            Map<String, String> usersMap = employeeBindEntities.stream()
                    .collect(Collectors.toMap(
                            OuterOaEmployeeBindEntity::getOutEmpId,
                            OuterOaEmployeeBindEntity::getFsEmpId,
                            (existingValue, newValue) -> existingValue));

            for (int i = 0; i < addEmployeeBindEntities.size(); i += 1000) {
                int end = Math.min(addEmployeeBindEntities.size(), i + 1000);
                List<OuterOaEmployeeBindEntity> batch = addEmployeeBindEntities.subList(i, end);
                ExternalCalendarEventAddAttendeeDetail externalCalendarEventAddAttendeeDetail = new ExternalCalendarEventAddAttendeeDetail();
                List<ExternalCalendarEventAddAttendeeDetail.Attendee> attendees = new LinkedList<>();

                for(OuterOaEmployeeBindEntity employeeBindEntity : batch) {
                    ExternalCalendarEventAddAttendeeDetail.Attendee attendee = new ExternalCalendarEventAddAttendeeDetail.Attendee();
                    attendee.setType(CalendarEventAttendeeTypeEnum.USER.getCode());
                    attendee.setUserId(employeeBindEntity.getOutEmpId());
                    if(ownerList.contains(employeeBindEntity.getFsEmpId())) {
                        attendee.setOperateId(employeeBindEntity.getOutEmpId());
                    }
                    attendees.add(attendee);
                }
                externalCalendarEventAddAttendeeDetail.setAttendees(attendees);
                LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarEvent,externalCalendarEventAddAttendeeDetail={}", externalCalendarEventAddAttendeeDetail);
                Result<ExternalCalendarEventAddAttendeeResult> externalCalendarEventAddAttendeeResultResult = feishuCalendarService.addCalendarEventAttendees(appId, outEa, calendarId, calendarEventId, externalCalendarEventAddAttendeeDetail);
                if(!externalCalendarEventAddAttendeeResultResult.isSuccess()) {
                    LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarEvent,externalCalendarEventAddAttendeeResultResult={}", externalCalendarEventAddAttendeeResultResult);
                    return Result.newError(ResultCodeEnum.ADD_CALENDAR_EVENT_ATTENDEE_ERROR.getCode(), externalCalendarEventAddAttendeeResultResult.getMsg());
                }
                //飞书直接返回全部的人员，最后一次再插入
                if(addEmployeeBindEntities.size() == end) {
                    Map<String, OuterOaEmployeeBindEntity> employeeBindEntityMap = addEmployeeBindEntities.stream()
                            .collect(Collectors.toMap(OuterOaEmployeeBindEntity::getOutEmpId, Function.identity(), (existing, replacement) -> existing));
                    for(ExternalCalendarEventAddAttendeeResult.Attendee attendee : externalCalendarEventAddAttendeeResultResult.getData().getAttendees()) {
                        if(!employeeBindEntityMap.containsKey(attendee.getUserId())) {
                            continue;
                        }
                        OuterOaScheduleBindEntity scheduleBindEntity=new OuterOaScheduleBindEntity();
                        scheduleBindEntity.setId(IdGenerator.get());
                        scheduleBindEntity.setOutScheduleId(calendarEventId);
                        scheduleBindEntity.setOutEa(outEa);
                        scheduleBindEntity.setFsEa(fsEa);
                        scheduleBindEntity.setChannel(channelEnum);
                        scheduleBindEntity.setFsScheduleId(objectId);
                        scheduleBindEntity.setAppId(appId);
                        scheduleBindEntity.setDcId(enterpriseBindEntity.getId());
                        scheduleBindEntity.setCreateTime(System.currentTimeMillis());
                        scheduleBindEntity.setUpdateTime(System.currentTimeMillis());
                        scheduleBindEntity.setOutUserId(attendee.getUserId());
                        scheduleBindEntity.setStatus(OuterOaScheduleBindStatusEnum.normal);
                        scheduleBindEntities.add(scheduleBindEntity);
                    }
                    Integer attendeeCount = outerOaScheduleBindManager.batchUpsert(scheduleBindEntities);
                    LogUtils.info("ExternalCalendarServiceImpl.createExternalCalendarDetail2,calendarEvent,attendeeCount={}", attendeeCount);

                }
            }
        }
        //删除人员
        if(!calendarEventAttendeeMap.isEmpty()) {
            LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarEventAttendeeMap={}", calendarEventAttendeeMap);
            List<String> deleteOutUserIds = new LinkedList<>(calendarEventAttendeeMap.keySet());

            if(CollectionUtils.isEmpty(deleteOutUserIds)) {
                return Result.newSuccess();
            }
            ExternalCalendarEventDelAttendeeDetail externalCalendarEventDelAttendeeDetail = new ExternalCalendarEventDelAttendeeDetail();
//            externalCalendarEventDelAttendeeDetail.setAttendeeIds(deleteOutUserIds);
            List<ExternalCalendarEventDelAttendeeDetail.DeleteId> deleteIds = new LinkedList<>();
            for (int i = 0; i < deleteOutUserIds.size(); i += 1000) {
                int end = Math.min(deleteOutUserIds.size(), i + 1000);
                List<String> batch = deleteOutUserIds.subList(i, end);
                for(String deleteOutUserId : batch) {
                    ExternalCalendarEventDelAttendeeDetail.DeleteId deleteId = new ExternalCalendarEventDelAttendeeDetail.DeleteId();
                    deleteId.setType(CalendarEventAttendeeTypeEnum.USER.getCode());
                    deleteId.setUserId(deleteOutUserId);
                    deleteIds.add(deleteId);
                }
                externalCalendarEventDelAttendeeDetail.setDeleteIds(deleteIds);
                LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,externalCalendarEventDelAttendeeDetail={}", externalCalendarEventDelAttendeeDetail);
                Result<Void> externalCalendarEventAddAttendeeResultResult = feishuCalendarService.deleteCalendarEventAttendees(appId, outEa, calendarId, calendarEventId, externalCalendarEventDelAttendeeDetail);
                if(!externalCalendarEventAddAttendeeResultResult.isSuccess()) {
                    LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarEvent,externalCalendarEventAddAttendeeResultResult={}", externalCalendarEventAddAttendeeResultResult);
                    return Result.newError(ResultCodeEnum.DELETE_CALENDAR_EVENT_ATTENDEE_ERROR.getCode(), externalCalendarEventAddAttendeeResultResult.getMsg());
                }
            }

            Integer attendeeCount = outerOaScheduleBindManager.deleteByOutUserIds(fsEa, outEa, calendarEventId, deleteOutUserIds);
            LogUtils.info("ExternalCalendarServiceImpl.updateExternalCalendar2,calendarEvent,attendeeCount={}", attendeeCount);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteExternalCalendar(OuterOaEnterpriseBindEntity enterpriseBindEntity, String objectId) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        ChannelEnum channelEnum=enterpriseBindEntity.getChannel();
        //查看是否有日程绑定
        OuterOaScheduleBindEntity outerOaScheduleBindEntity = outerOaScheduleBindManager.queryOutScheduleId(fsEa, outEa, objectId);

        if(ObjectUtils.isEmpty(outerOaScheduleBindEntity)) {
            LogUtils.info("ExternalTodoMsgServiceImpl.deleteExternalCalendar,calendarEventEntity is null,objectId={}", objectId);
            return Result.newSuccess();
        }
        String calendarEventId = outerOaScheduleBindEntity.getOutScheduleId();

        //是否已经保存了日历
        OuterOaCalendarTemplateParams outerOaCalendarTemplateParams= new OuterOaCalendarTemplateParams();
        outerOaCalendarTemplateParams.setOutEa(outEa);
        outerOaCalendarTemplateParams.setChannel(channelEnum);
        List<OuterOaCalendarTemplateEntity> entities = outerOaCalendarTemplateManager.getEntities(outerOaCalendarTemplateParams);

        LogUtils.info("ExternalCalendarServiceImpl.deleteExternalCalendar,calendarEntity={}", entities);
        if(ObjectUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.UPDATE_CALENDAR_EVENT_ERROR);
        }
        String calendarId = entities.get(0).getCalendarId();
        LogUtils.info("ExternalCalendarServiceImpl.deleteExternalCalendar,calendarId={}", calendarId);

        //只删除日程
        Result<Void> deleteCalendarEvent = feishuCalendarService.deleteCalendarEvent(appId, outEa, calendarId, calendarEventId);
        LogUtils.info("ExternalCalendarServiceImpl,deleteExternalCalendar,deleteCalendarEvent={}", deleteCalendarEvent);
        if(!deleteCalendarEvent.isSuccess()) {
            return Result.newError(ResultCodeEnum.DELETE_CALENDAR_EVENT_ERROR);
        }
        //更新绑定关系
        Integer count = outerOaScheduleBindManager.updateStatusByScheduleId(fsEa, outEa, calendarEventId, OuterOaScheduleBindStatusEnum.deleted);
        LogUtils.info("ExternalCalendarServiceImpl,deleteExternalCalendar,count={}", count);
        return Result.newSuccess();
    }

    @Override
    public Result<String> queryExternalCalendarId(String fsEa) {
        //企业是否有绑定关系
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntityByFsEa(fsEa, BindStatusEnum.normal);

        if(ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String outEa = enterpriseBindEntity.getOutEa();
        OuterOaCalendarTemplateParams outerOaCalendarTemplateParams= new OuterOaCalendarTemplateParams();
        outerOaCalendarTemplateParams.setOutEa(outEa);
        outerOaCalendarTemplateParams.setChannel(enterpriseBindEntity.getChannel());
        List<OuterOaCalendarTemplateEntity> entities = outerOaCalendarTemplateManager.getEntities(outerOaCalendarTemplateParams);
        if(ObjectUtils.isNotEmpty(entities)) {
            return Result.newSuccess(entities.get(0).getCalendarId());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> queryExternalCalendarEventId(String fsEa, String objectId) {
        //企业是否有绑定关系
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntityByFsEa(fsEa, BindStatusEnum.normal);
        if(ObjectUtils.isEmpty(enterpriseBindEntity)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        //查看是否有日程绑定
        OuterOaScheduleBindEntity outerOaScheduleBindEntity = outerOaScheduleBindManager.queryOutScheduleId(fsEa, enterpriseBindEntity.getOutEa(), objectId);
        if(ObjectUtils.isNotEmpty(outerOaScheduleBindEntity)) {
            return Result.newSuccess(outerOaScheduleBindEntity.getOutScheduleId());
        }
        return Result.newSuccess();
    }
}
