package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserCreatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 员工入职处理器
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class ContactUserCreatedV3EventHandler extends FeishuEventHandler {
    @Resource
    private ContactsService contactsService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private RedissonClient redissonClient;
    private String FEISHU_USER_LOCK = "FEISHU_USER_LOCK_%s";
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @Override
    public String getSupportEventType() {
        return "contact.user.created_v3";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("ContactUserCreatedV3EventHandler.handle,eventData={}", eventData);
        FeishuContactUserCreatedV3Event event = JSON.parseObject(eventData, FeishuContactUserCreatedV3Event.class);
        LogUtils.info("ContactUserCreatedV3EventHandler.handle,event={}", event);
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(header.getTenantKey())
                .appId(header.getAppId()).bindStatus(BindStatusEnum.normal).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : enterpriseBindList) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
            SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            //刷新临时库数据
            contactsService.saveOrUpdateContactUser(header.getEventType(), outerOaEnterpriseBindEntity, outerOaEnterpriseBindEntity.getChannel(),
                    event.getObject(), null, eventData);
            if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
                //绑定人员
                LogUtils.info("save auto bind user");
                String openUserId = event.getObject().getOpenId();
                RLock oaLock = redissonClient.getLock(String.format(FEISHU_USER_LOCK, openUserId));
                try {
                    boolean lockValue = oaLock.tryLock(10, TimeUnit.SECONDS);
                    //避免处理失败，没有解锁
                    if (lockValue) {
                        try {
                            Result<Void> result = contactsService.addUserList(header.getAppId(), outerOaEnterpriseBindEntity,
                                    header.getTenantKey(),
                                    Lists.newArrayList(event.getObject()));
                            LogUtils.info("ContactUserCreatedV3EventHandler.handle,addUserList,result={}", result);
                        } catch (Exception e) {
                            log.error("user update fail:{}", e);
                        } finally {
                            oaLock.unlock();
                        }
                    }

                } catch (Exception e) {
                    log.error("contact user get lock fail:{}", e.getMessage());

                }
            }

            }
        return SUCCESS;

    }
}

