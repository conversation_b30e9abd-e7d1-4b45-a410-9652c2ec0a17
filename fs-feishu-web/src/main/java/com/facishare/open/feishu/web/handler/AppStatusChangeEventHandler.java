package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppStatusChangeEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.web.template.outer.event.app.FeishuAppEventTemplate;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 应用停启用事件处理器
 */
@Component
public class AppStatusChangeEventHandler extends FeishuEventHandler {
//    @Resource
//    private AppService appService;
//    @Resource
//    private CorpService corpService;
    @Resource
    private FeishuAppEventTemplate feishuAppEventTemplate;

    @Override
    public String getSupportEventType() {
        return "app_status_change";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        LogUtils.info("AppStatusChangeEventHandler.handle,eventData={}",eventData);
        FeishuAppStatusChangeEvent event = JSON.parseObject(eventData, FeishuAppStatusChangeEvent.class);
        LogUtils.info("AppStatusChangeEventHandler.handle,event={}",event);

        MethodContext context = MethodContext.newInstance(event);
        feishuAppEventTemplate.onAppStatusChange(context);

        return SUCCESS;
    }
}
