package com.facishare.open.feishu.sync.service.impl;

import com.facishare.open.feishu.sync.manager.AppInfoManager;
import com.facishare.open.feishu.sync.manager.EmployeeBindManager;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.FeishuMessageService;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEventService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Deprecated
@Service("fsEventService")
public class FsEventServiceImpl implements FsEventService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private AppInfoManager appInfoManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private ContactsService contactsService;
    @Resource
    private FeishuMessageService feishuMessageService;

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
       LogUtils.error("interface not support ea:{}",ea);
       return Result.newSuccess();
    }

    @Override
    public Result<Void> onEnterpriseOpened(Integer ei, String ea, String enterpriseName) {
        LogUtils.info("FsEventServiceImpl.onEnterpriseOpened,ei={},ea={},enterpriseName={}", ei,ea,enterpriseName);
        LogUtils.error("interface not support ea:{}",ea);
        return Result.newSuccess();
    }
}
