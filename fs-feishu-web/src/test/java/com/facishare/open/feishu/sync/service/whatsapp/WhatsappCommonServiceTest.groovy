package com.facishare.open.feishu.sync.service.whatsapp


import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappCommonService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class WhatsappCommonServiceTest extends Specification {
    @Resource
    private WhatsappCommonService whatsappCommonService;
    def "getBalance"() {
        expect:
        def a = whatsappCommonService.getBalance("88146")
        print(a)
    }
}
