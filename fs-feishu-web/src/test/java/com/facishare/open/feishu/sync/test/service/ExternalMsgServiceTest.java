package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.service.ExternalMsgService;
import com.facishare.open.feishu.web.handler.ExternalMessageHandler;
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class ExternalMsgServiceTest extends BaseTest {
    @Autowired
    private ExternalMsgService externalMsgService;
    @Autowired
    private ExternalMessageHandler externalMessageHandler;

    @Test
    public void testSendTextMessage() {
        SendTextMessageArg arg = new SendTextMessageArg();
        arg.setGenerateUrlType(0);
        arg.setEa("90429");
        arg.setEi(90429);
        arg.setReceiverIds(Lists.newArrayList(1000, 1002, 1003));
        arg.setMessageContent("【转移】执行完毕， 执行人:超人不会飞\n" +
                "本次操作覆盖 63 条 【怪兽线索】，执行成功 63 条， 失败 0 条。 详细执行结果请点击查看详情");
        arg.setReceiverChannelType(1);
        Map<String, String> channel = new HashMap<>();
        channel.put("appId", "crmNotify");
        arg.setReceiverChannelData(JSON.toJSONString(channel));
        externalMessageHandler.dealSendTextMessageHandler(arg);
    }

    @Test
    public void testSendTextCardMessage() {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setGenerateUrlType(1);
        arg.setEa("83998");
        arg.setEi(83998);
        arg.setReceiverIds(Lists.newArrayList(1000, 300411010, 1001, 300405619));
        arg.setMessageContent("长沙齐名国际物流有限公司，订单金额：2371.00元，下单日期：2022-10-25");
        arg.setReceiverChannelType(1);
        arg.setTitle("销售订单被修改");
        Map<String, String> channel = new HashMap<>();
        channel.put("appId", "WJTZ");
        arg.setUrl("https://crm.ceshi112.com/dps/preview/bypath?path=N_202402_19_a052ba0914534774af84c1baec32ec7b.xlsx&showHeader=1");
        arg.setReceiverChannelData(JSON.toJSONString(channel));
        List<KeyValueItem> form = new LinkedList<>();
        KeyValueItem valueItem = new KeyValueItem("啊行", "啊对");
        KeyValueItem valueItem1 = new KeyValueItem("啊行吧", "啊对吧");
        form.add(valueItem);
        form.add(valueItem1);
        arg.setForm(form);
        Map<String, String> extraDataMap = new HashMap<>();
        extraDataMap.put("activityId", "1.xlsx");
        extraDataMap.put("objectApiName", "AccountObj");
        extraDataMap.put("applicantId", "1000");
        extraDataMap.put("workflowInstanceId", "66cec436913d0a6c46fc6dd2");
        extraDataMap.put("objectId", "66cec4347c3e710007b21d6e");
        extraDataMap.put("taskId", "66cec4366201a634fe236b3e");
        arg.setExtraDataMap(extraDataMap);
        arg.setAppId("FSAID_11490d9e");
        externalMessageHandler.dealSendTextCardMessageHandler(arg);
    }
}
