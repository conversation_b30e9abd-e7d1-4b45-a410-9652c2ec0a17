package com.facishare.open.feishu.sync.service

import com.facishare.open.feishu.sync.manager.EmployeeBindManager
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager
import com.facishare.open.feishu.sync.manager.ExternalTodoManager
import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum
import com.facishare.open.feishu.syncapi.enums.OutOaMsgEventTypeEnum
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class ExternalTodoManagerTest extends Specification {
    @Resource
    private ExternalTodoManager externalTodoManager
    @Resource
    private EmployeeBindManager employeeBindManager
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;
    def "createExternalTodoTask"() {
        given:
        CreateTodoArg arg = new CreateTodoArg();
        arg.setGenerateUrlType(1)
        arg.setEa("90429")
        arg.setEi(90429)
        arg.setReceiverIds(Lists.newArrayList(1000))
        arg.setContent("客户(2024-08-01 16:29)")
        arg.setTitle("待处理的审批流程")
        arg.setBizType("452")
        List<KeyValueItem> form = new LinkedList<>()
        KeyValueItem valueItem = new KeyValueItem("流程主题", "客户(2024-08-01 16:29)")
        KeyValueItem valueItem1 = new KeyValueItem("客户名称", "啦啦啦-客户009")
        KeyValueItem valueItem2 = new KeyValueItem("1级行业", "")
        KeyValueItem valueItem3 = new KeyValueItem("客户级别", "")
        KeyValueItem valueItem4 = new KeyValueItem("成交状态", "未成交")
        KeyValueItem valueItem5 = new KeyValueItem("负责人", "陈宗鑫")
        KeyValueItem valueItem6 = new KeyValueItem("多余的", "飞书只允许写5行，这个看下会不会展示，还是说只展示后5行")
        form.add(valueItem)
        form.add(valueItem1)
        form.add(valueItem2)
        form.add(valueItem3)
        form.add(valueItem4)
        form.add(valueItem5)
        form.add(valueItem6)
        arg.setForm(form)
        arg.setUrl("todo?apiname=AccountObj&id=66ab47657da2360008c79f41&ea=90429")
        arg.setSourceId("66ab476634b8a63a89792bff")
        Map<String, String> extraDataMap = new HashMap<>()
        extraDataMap.put("workflowInstanceId", "66ab47668431dc45ef2343f1")
        extraDataMap.put("activityId", "1")
        extraDataMap.put("objectApiName", "AccountObj")
        extraDataMap.put("objectId", "66ab47657da2360008c79f41")
        extraDataMap.put("taskId", "66ab476634b8a63a89792bff")
        extraDataMap.put("applicantId", "10000")
        arg.setExtraDataMap(extraDataMap)
        List<EmployeeBindEntity> employeeBindEntities = employeeBindManager.getEntityList("90429", Lists.newArrayList("1000"))

        CreateTodoPushArg createTodoPushArg = new CreateTodoPushArg();
        createTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
        //查询所有绑定关系
        List<EnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntityList(arg.getEa(), BindStatusEnum.normal);
        for(EnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            createTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
            createTodoPushArg.setCreateTodoArg(arg);
            def a = externalTodoManager.createExternalTodoTask(createTodoPushArg, employeeBindEntities)
            print(a)
        }
    }
}
