package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.feishu.syncapi.enums.BuyTypeEnum;
import com.facishare.open.feishu.syncapi.enums.OrderStatusEnum;
import com.facishare.open.feishu.syncapi.enums.PricePlanTypeEnum;
import com.facishare.open.feishu.syncapi.model.config.VersionModel;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.arg.QueryCustomerArg;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import org.junit.Test;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public class FsOrderServiceProxyTest extends BaseTest {
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;

    @Test
    public void createCustomer() {
        CreateCustomerArg arg = CreateCustomerArg.builder()
                .enterpriseAccount("test_10010")
                .enterpriseName("test_10010")
                .outEid("test_10010_eid")
                .source("3")
                .managerName("wubb")
                .build();
        Result<Void> result = fsOrderServiceProxy.createCustomer(arg);
        System.out.println(result);
    }

    @Test
    public void createCrmOrder() {
        String fsEa = "fsfxcs1283";
        long time = new Date().getTime();
        OrderInfoEntity entity = OrderInfoEntity.builder()
                .appId("cli_a20192f6afb8d00c")
                .orderId("order_100000000010013")
                .paidCorpId("11b1a8c266da9758")
                .orderCorpId("11b1a8c266da9758")
                .buyType(BuyTypeEnum.buy)
                .editionId("price_a25f5f67a172d00e")
                .channel(ChannelEnum.feishu)
                .orderDays(365)
                .userCount(30)
                .pricePlanType(PricePlanTypeEnum.per_seat_per_year)
                .price(100000)
                .payPrice(100000)
                .orderStatus(OrderStatusEnum.normal)
                .orderTime(new Timestamp(time))
                .payTime(new Timestamp(time))
                .beginTime(new Timestamp(time))
                .endTime(new Timestamp(time + 365 * 24 * 60 * 60 * 1000))
                .build();

        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                .enterpriseAccount(fsEa)
                .orderId(entity.getOrderId())
                .orderTime(entity.getPayTime().getTime())
                .orderTpye(entity.getPricePlanType() == PricePlanTypeEnum.trial ? CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_TRY : CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_BUY)
                .build();

        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(entity.getAppId(), entity.getEditionId());

        //订单金额单位是 分，需要转换成 元
        Integer orderAmount = entity.getPayPrice() / 100;
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                .beginTime(entity.getBeginTime().getTime())
                .endTime(entity.getEndTime().getTime())
                .quantity(1)
                .allResourceCount(entity.getUserCount())
                .orderAmount(orderAmount + "")
                .productId(versionModel.getProductId())
                .build();

        CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                .crmOrderDetailInfo(orderDetailInfo)
                .crmOrderProductInfo(orderProductInfo)
                .build();

        Result<Void> result = fsOrderServiceProxy.createCrmOrder(orderArg);
        System.out.println(result);
    }

    @Test
    public void createCustomer2() {
//        FsCustomerObjectData customerObject = new FsCustomerObjectData();
//        customerObject.setEnterpriseName("新客户对象001");
//        customerObject.setName("新客户对象001");
//        customerObject.setEnterpriseAccount("xkhdx001");
//        customerObject.setManagerName("张三");
//        customerObject.setManagerMobile("***********");
//        HashMap<String, String> crmProductIdMap = JSON.parseObject(ConfigCenter.crm_product_ids, new TypeToken<HashMap<String, String>>() {
//        }.getType());
//        customerObject.setSource(crmProductIdMap.get("WE_CHAT"));
//        customerObject.setOutEid("xkhdx001");
//        customerObject.setOwner(Lists.newArrayList("-10000"));
        CreateCustomerArg arg = CreateCustomerArg.builder()
                .enterpriseAccount("xkhdx001")
                .enterpriseName("新客户对象001")
                .managerMobile("***********")
                .managerName("张三")
                .outEid("xkhdx001")
                .channel("qywx")
                .build();

        Result<String> result = fsOrderServiceProxy.createCustomer2(arg);
        System.out.println(result);
    }

    @Test
    public void queryCustomerBySearchTemplate() {
        QueryCustomerArg arg = new QueryCustomerArg();
        arg.setEnterpriseAccount("xkhdx001");
        arg.setOutEid("xkhdx001");
        Result<List<ObjectData>> result = fsOrderServiceProxy.queryCustomerBySearchTemplate(arg);
        System.out.println(result);
    }

    @Test
    public void queryOrderBySearchTemplate() {

//        Result<List<ObjectData>> result = fsOrderServiceProxy.queryBySearchTemplate("SalesOrderObj", searchTemplateQuery);
//        System.out.println(result);
    }
}
