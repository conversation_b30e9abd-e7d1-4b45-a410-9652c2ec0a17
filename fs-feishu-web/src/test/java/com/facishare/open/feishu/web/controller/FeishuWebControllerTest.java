package com.facishare.open.feishu.web.controller;

import com.facishare.open.outer.oa.connector.common.api.info.EmployeeBindModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.arg.QueryOutUnbindArg;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuWebController;
import com.facishare.open.outer.oa.connector.common.api.info.PageModel;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class FeishuWebControllerTest extends BaseTest {
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private FeishuWebController feishuWebController;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;



    @Test
    public void queryOutUnbind2() {
        QueryOutUnbindArg arg = new QueryOutUnbindArg();
        arg.setPageSize(100);
        String result = feishuWebController.queryOutUnbind2(arg,null);
        System.out.println(result);
    }

    @Test
    public void getEnterpriseData() {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount("84883");
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        System.out.println(result);
    }
}
