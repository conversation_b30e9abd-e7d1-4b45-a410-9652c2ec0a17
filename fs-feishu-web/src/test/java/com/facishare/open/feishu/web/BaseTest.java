package com.facishare.open.feishu.web;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-test/applicationContext-test.xml"})
public class BaseTest {
    protected String testAppId = "cli_a3ddeb52763b100c";
    protected String testTenantKey = "100d7a94d39e575e";

    @BeforeClass
    public static void SetUp(){
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name","feishu-web");
    }

    @Test
    public void emptyTest() {
        System.out.println("test passed");
    }
}