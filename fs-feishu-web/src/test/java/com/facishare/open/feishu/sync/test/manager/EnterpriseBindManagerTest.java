package com.facishare.open.feishu.sync.test.manager;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import org.junit.Test;

import javax.annotation.Resource;

public class EnterpriseBindManagerTest extends BaseTest {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;

    @Test
    public void updateBindStatus() {
        int status = enterpriseBindManager.updateBindStatus("fsea",null, BindStatusEnum.stop);
        System.out.println(status);
    }

}
