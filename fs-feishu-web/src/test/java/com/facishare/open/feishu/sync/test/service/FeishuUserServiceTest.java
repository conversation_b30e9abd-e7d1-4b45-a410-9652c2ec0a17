package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.ExternalTodoManager;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.enums.ApprovalStatusEnum;
import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum;
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskUpdateDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalTaskResult;
import com.facishare.open.feishu.syncapi.result.ExternalInstancesDetailResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.feishu.syncapi.service.FeishuUserService;
import com.facishare.open.feishu.syncapi.vo.InstanceVo.ExternalMessageInstanceVo;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindMsgTypeEnum;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

public class FeishuUserServiceTest extends BaseTest {
    @Autowired
    private FeishuUserService feishuUserService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ExternalTodoManager externalTodoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private ExternalApprovalsService externalApprovalsService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Resource
    private ExternalTodoMsgService externalTodoMsgService;

    @Test
    public void createUser() {
//        for(int i=2;i<=10000;i++) {
            UserData.User user = new UserData.User();
            user.setName("测试用户100001");
            long mobile = 18926584799L;
            user.setMobile(mobile+"");
            user.setDepartment_ids(Lists.newArrayList("26e86g59467b3c1a"));
            user.setEmployee_type(1);
            Result<UserData.User> result = feishuUserService.createUser("cli_a20193041afcd00e", user);
            System.out.println(result);
//        }
    }

    @Test
    public void deleteUser() {
        try {
            Result<List<UserData.User>> listResult = feishuUserService.getUserList("cli_a20193041afcd00e",
                    null,
                    "26e86g59467b3c1a",
                    UserIdTypeEnum.user_id,
                    DepartmentIdTypeEnum.department_id,
                    50);
            if(listResult.isSuccess()) {
                for(UserData.User user : listResult.getData()) {
                    Result<Void> result = feishuUserService.deleteUser("cli_a20193041afcd00e", user.getUserId());
                    System.out.println(result);
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void getAllUserByDepartment(){
        Result<List<UserData.User>> allUserByDepartment = feishuUserService.getAllDepartmentUsers("cli_a3ddeb52763b100c",
                "100d08b69448975d",
                "0");
        System.out.println(allUserByDepartment.getData());
    }

    @Test
    public void getSingleUserInfo() {
        int tenantid= eieaConverter.enterpriseAccountToId("fssdbd2464");
        Result<UserData.User> result = feishuUserService.getUserInfo("cli_a3ddeb52763b100c",
                "100d08b69448975d",
                "ou_98edcf880b9b275a32d96fee73bbb0a1");
        System.out.println(result);
    }


    @Test
    public void testTodoFix(){
        String ea="91449";
        Long startTime=1742407200000L;
        Long endTime=System.currentTimeMillis();
        LogUtils.info("fixTodo，tenantId：{}，start_time：{}，end_time：{}", ea, startTime, endTime);
        Integer tenantId=eieaConverter.enterpriseAccountToId(ea);
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, ea, null, null);

        try {
            // 创建查询参数
            OuterOaMessageBindParams params = OuterOaMessageBindParams.builder()
                    .channel(ChannelEnum.feishu)
                    .fsEa(ea)
                    .build();

            // 使用自定义方法查询带时间范围的消息数据
            List<OuterOaMessageBindEntity> messages = outerOaMessageBindManager.getEntitiesByTimeRange(params, startTime, endTime);
            for (OuterOaMessageBindEntity message : messages) {
                //需要拿到待办的消息
                try {
                    List<ObjectData> objectData = queryApprovalTaskData(tenantId, message.getSourceId());
                    if (ObjectUtils.isNotEmpty(objectData)) {
                        ObjectData itemData = objectData.get(0);
                        String state = String.valueOf(itemData.get("state"));
                        if (!state.equals("in_progress")) {
                            /**
                             * (data=DealTodoPushArg(msgType=commonMsg, upstreamEa=null,
                             * enterpriseBindEntity=OuterOaEnterpriseBindEntity(id=6803c9d8afceeb0001ab19b6, channel=feishu,
                             * fsEa=dcg000034, outEa=2e9d0b8ea98f5654, appId=cli_a20192f6afb8d00c, connectInfo={"alertConfig":true,
                             * "alertTypes":["CRM_TODO","CRM_NOTIFICATION"],"appId":"cli_a20192f6afb8d00c","appType":"isv",
                             * "channel":"feishu","connectorName":"飞书连接器","corpId":"2e9d0b8ea98f5654","dataCenterName":"神州数码",
                             * "displayId":"F245228343","enterpriseName":"神州数码","isFirstLand":true,"isRetainInformation":false},
                             * bindType=manual, bindStatus=normal, createTime=1745078744317, updateTime=1745142343858),
                             * dealTodoArg=DealTodoArg(super=BaseExternalArg(generateUrlType=0, extraDataMap={}, groupKeys=[feishu],
                             * appId=null), ea=dcg000034, ei=650993, operators=[5696],
                             * sourceId=6805e8ad1cc251641812d167, bizType=452, handleUserIds=[5696],
                             * taskStatusEnum=null, extDataMap={opinions=[{"reply_user":["5696"],
                             * "action_type":"agree","reply_time":1745228006328,"id":"680610e60696b117fa311c8a","opinion":""}], state=pass})),
                             * result=TemplateResult(code=0, msg=null, data=null))
                             */
                            //in_progress 审批中的数据
                            //需要将审批人改为已处理
                            if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.todo)) {
                                //需要根据detail，重新推送
                                ExternalMessageInstanceVo externalTodoInstanceEntity = convertMessageInfo(message.getMessageInfo());
                                ExternalInstancesDetail externalInstancesDetail = externalTodoInstanceEntity.getTodoDetail();
                                // 组装数据
                                String curTime = System.currentTimeMillis() + "";
                                externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), message.getAppId(), externalInstancesDetail);
                                LogUtils.info("external approval taskids:{}", instancesDetailResultResult);
                            } else {
                                if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.bot)) {
                                    //bot消息
                                    ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
                                    externalApprovalsTaskUpdateDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                    externalApprovalsTaskUpdateDetail.setMessageId(message.getTaskId());
                                    Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(message.getOutEa(), message.getAppId(), externalApprovalsTaskUpdateDetail);
                                    LogUtils.info("external approval taskids:{}", externalApprovalTaskResultResult);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    LogUtils.warn("fix error approval", e);
                }
            }

        } catch (Exception e) {
            LogUtils.error("fixTodo执行异常", e);
        }
    }
    //兼容之前的数据
    private ExternalMessageInstanceVo convertMessageInfo(String messageInfo){
        ExternalMessageInstanceVo externalMessageInstanceVo = JSONObject.parseObject(messageInfo, new TypeReference<ExternalMessageInstanceVo>() {
        });
        //兼容之前的数据
        ExternalInstancesDetail externalInstancesDetail=null;
        if(ObjectUtils.isEmpty(externalMessageInstanceVo)||ObjectUtils.isEmpty(externalMessageInstanceVo.getTodoDetail())){
            externalMessageInstanceVo=new ExternalMessageInstanceVo();
            externalInstancesDetail = JSONObject.parseObject(messageInfo, new TypeReference<ExternalInstancesDetail>() {
            });
            externalMessageInstanceVo.setTodoDetail(externalInstancesDetail);

        }else{
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        }
        return externalMessageInstanceVo;
    }
    /**
     * 查询crm审批数据
     *
     * @param tenantId
     * @param dataId
     * @return
     */
    public List<ObjectData> queryApprovalTaskData(Integer tenantId, String dataId) {

        HeaderObj headerObj =HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        String erpOrgObj = "ApprovalTaskObj";
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(dataId);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        LogUtils.info("ApprovalTaskManager data={}", dataListRes);
        if (!dataListRes.isSuccess()) {
            LogUtils.warn("list erp org obj failed,tenantId:{},res:{}", tenantId, dataListRes);
            return null;
        }
        return dataListRes.getData().getDataList();
    }






    @Test
    public void batchGetUserIds() {
        String plainText="{\"action_type\":\"APPROVE\",\"action_context\":\"{\\\"MsgType\\\":\\\"commonMsg\\\",\\\"fsEa\\\":\\\"91449\\\",\\\"outEa\\\":\\\"142f6d8a511b975e\\\",\\\"fsUserId\\\":\\\"1000\\\",\\\"taskId\\\":\\\"6806561a4d60e337dab12121\\\"}\",\"user_id\":\"3dacfa1a\",\"message_id\":7495773338493517827,\"token\":\"****6wa7yhYe6nvXNcZBOe0qix46kgW****\"}";
        ExternalDealTodoEvent externalDealTodoEvent = JSON.parseObject(plainText, ExternalDealTodoEvent.class);

        Result<Void> dealCrmTodoResult = externalTodoMsgService.dealCrmTodo(externalDealTodoEvent);


        String data="{\n" +
                "      \"super\": {\n" +
                "        \"generateUrlType\": 0,\n" +
                "        \"extraDataMap\": {},\n" +
                "        \"groupKeys\": [],\n" +
                "        \"appId\": null\n" +
                "      },\n" +
                "      \"ea\": \"91449\",\n" +
                "      \"ei\": \"91449\",\n" +
                "      \"operators\": [\"1201\", \"1000\"],\n" +
                "      \"sourceId\": \"6805e6bf1b8db756d65d3ab9\",\n" +
                "      \"bizType\": 452,\n" +
                "      \"handleUserIds\": [\"1000\"],\n" +
                "      \"taskStatusEnum\": null,\n" +
                "      \"extDataMap\": {\n" +
                "        \"opinions\": [\n" +
                "          {\n" +
                "            \"reply_user\": [\"1000\"],\n" +
                "            \"feedId\": \"100000017\",\n" +
                "            \"action_type\": \"agree\",\n" +
                "            \"reply_time\": 1745219178381,\n" +
                "            \"replyId\": \"100000003\",\n" +
                "            \"id\": \"6805ee6a1b8db756d65d3b66\",\n" +
                "            \"opinion\": \"测试测试\"\n" +
                "          }\n" +
                "        ],\n" +
                "        \"state\": \"pass\"\n" +
                "      }\n" +
                "    }";
        DealTodoArg dealTodoArg= JSONObject.parseObject(data,DealTodoArg.class);
        String employeeData="[\n" +
                "      {\n" +
                "        \"id\": \"6800c08d6982f800019e9ad7\",\n" +
                "        \"channel\": \"feishu\",\n" +
                "        \"dcId\": \"67f5ce157e1ac00001a4ae91\",\n" +
                "        \"fsEa\": \"91449\",\n" +
                "        \"outEa\": \"142f6d8a511b975e\",\n" +
                "        \"appId\": \"cli_a75815139bbc500e\",\n" +
                "        \"fsEmpId\": \"1000\",\n" +
                "        \"outEmpId\": \"ou_f77366adba55114b37945b1696dee4d9\",\n" +
                "        \"bindStatus\": \"normal\",\n" +
                "        \"createTime\": 1744879757955,\n" +
                "        \"updateTime\": 1744969382024\n" +
                "      }\n" +
                "    ]";
        List<OuterOaEmployeeBindEntity> employeeBindEntities= JSONArray.parseArray(employeeData,OuterOaEmployeeBindEntity.class);
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=JSONObject.parseObject("{\n" +
                "      \"id\": \"67f5ce157e1ac00001a4ae91\",\n" +
                "      \"channel\": \"feishu\",\n" +
                "      \"fsEa\": \"91449\",\n" +
                "      \"outEa\": \"142f6d8a511b975e\",\n" +
                "      \"appId\": \"cli_a75815139bbc500e\",\n" +
                "      \"connectInfo\": {\n" +
                "        \"alertConfig\": true,\n" +
                "        \"alertTypes\": [\"CRM_TODO\", \"CRM_NOTIFICATION\"],\n" +
                "        \"appId\": \"cli_a75815139bbc500e\",\n" +
                "        \"appSecret\": \"****9BHfCczQ4jeXlAsjkcXvzNAi****\",\n" +
                "        \"appType\": \"selfBuild\",\n" +
                "        \"bindStatus\": \"normal\",\n" +
                "        \"channel\": \"feishu\",\n" +
                "        \"connectorName\": \"飞书\",\n" +
                "        \"dataCenterId\": \"67f5ce157e1ac00001a4ae91\",\n" +
                "        \"dataCenterName\": \"112自建应用\",\n" +
                "        \"displayId\": \"FMZ984K4NZM\",\n" +
                "        \"enterpriseName\": \"AJMAN测试企业\"\n" +
                "      },\n" +
                "      \"bindType\": \"manual\",\n" +
                "      \"bindStatus\": \"normal\",\n" +
                "      \"createTime\": 1744162325150,\n" +
                "      \"updateTime\": 1744897409659\n" +
                "    }",OuterOaEnterpriseBindEntity.class);
        externalTodoManager.dealExternalApprovalTodoTask(dealTodoArg, employeeBindEntities, outerOaEnterpriseBindEntity);




        List<String> emails = new LinkedList<>();
        List<String> mobiles = new LinkedList<>();
//        for(int i = 0; i < 130; i++) {
//            int t = 10000;
//            emails.add(String.valueOf(t + i));
//            mobiles.add(String.valueOf(t + i));
//        }
        emails.add("<EMAIL>");
//        mobiles.add("13711911803");
        Result<List<UserData.User>> result =
                feishuUserService.batchGetUserIds("cli_a7fcf7b29af85003", "14121dcad548175a", emails, mobiles);
        System.out.println(result);
    }
}
