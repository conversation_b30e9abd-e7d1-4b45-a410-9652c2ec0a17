package com.facishare.open.feishu.sync.test.mongo;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorSyncEventDataMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.params.QueryOaConnectorSyncEventDataArg;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;

public class OaConnectorSyncEventDataMongoDaoTest extends BaseTest {
    @Autowired
    private OaConnectorSyncEventDataMongoDao oaConnectorSyncEventDataMongoDao;

    @Test
    public void batchReplace() {
        List<OaConnectorSyncEventDataDoc> docs = new LinkedList<>();
        OaConnectorSyncEventDataDoc doc = new OaConnectorSyncEventDataDoc();
        doc.setId(ObjectId.get());
        doc.setAppId("100d7a94d39e575e");
        doc.setChannel(ChannelEnum.feishu);
        doc.setEvent("{\"schema\":\"2.0\",\"header\":{\"event_id\":\"test1\",\"token\":\"test1\",\"create_time\":\"1701853297000\",\"event_type\":\"contact.user.updated_v3\",\"tenant_key\":\"test1\",\"app_id\":\"test1\"},\"event\":{\"object\":{\"avatar\":{\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=240x240\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=640x640\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=72x72\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=noop\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\"},\"city\":\"\",\"country\":\"\",\"department_ids\":[\"test1\"],\"employee_no\":\"test1\",\"employee_type\":1,\"en_name\":\"\",\"enterprise_email\":\"\",\"gender\":1,\"job_title\":\"高级销售工程师\",\"join_time\":1588521600,\"leader_user_id\":\"test1\",\"name\":\"test1\",\"nickname\":\"\",\"open_id\":\"ou_5530671bf6f5535e515ce19b88b3a1e7\",\"orders\":[{\"department_id\":\"test1\",\"department_order\":0,\"is_primary_dept\":true,\"user_order\":0}],\"status\":{\"is_activated\":true,\"is_exited\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":false},\"union_id\":\"test1\",\"work_station\":\"\"},\"old_object\":{\"job_title\":\"204716\",\"open_id\":\"test1\",\"union_id\":\"test1\"}}}");
        doc.setCreateTime(System.currentTimeMillis());
        doc.setUpdateTime(System.currentTimeMillis());
        doc.setEventType("contact.scope.updated_v3");
        doc.setOutEa("100d7a94d39e575e");
        doc.setStatus(0);
        docs.add(doc);
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataMongoDao.batchReplace(docs);
        System.out.println(bulkWriteResult);
        doc.setEvent("{\"schema\":\"2.0\",\"header\":{\"event_id\":\"test2\",\"token\":\"test2\",\"create_time\":\"1701853297000\",\"event_type\":\"contact.user.updated_v3\",\"tenant_key\":\"test2\",\"app_id\":\"test2\"},\"event\":{\"object\":{\"avatar\":{\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=240x240\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=640x640\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=72x72\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_004s_545dd3d7-f18b-4c90-8931-f83c0baf71bg~?image_size=noop\\u0026cut_type=\\u0026quality=\\u0026format=png\\u0026sticker_format=.webp\"},\"city\":\"\",\"country\":\"\",\"department_ids\":[\"test2\"],\"employee_no\":\"test2\",\"employee_type\":1,\"en_name\":\"\",\"enterprise_email\":\"\",\"gender\":1,\"job_title\":\"高级销售工程师\",\"join_time\":1588521600,\"leader_user_id\":\"test2\",\"name\":\"test2\",\"nickname\":\"\",\"open_id\":\"test2\",\"orders\":[{\"department_id\":\"test2\",\"department_order\":0,\"is_primary_dept\":true,\"user_order\":0}],\"status\":{\"is_activated\":true,\"is_exited\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":false},\"union_id\":\"test2\",\"work_station\":\"\"},\"old_object\":{\"job_title\":\"204716\",\"open_id\":\"test2\",\"union_id\":\"test2\"}}}");
        doc.setCreateTime(System.currentTimeMillis());
        doc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult1 = oaConnectorSyncEventDataMongoDao.batchReplace(docs);
        System.out.println(bulkWriteResult1);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        QueryOaConnectorSyncEventDataArg arg = new QueryOaConnectorSyncEventDataArg();
        arg.setAppId("100d7a94d39e575e");
        arg.setChannel(ChannelEnum.feishu);
        arg.setStatus(0);
        DeleteResult deleteResult = oaConnectorSyncEventDataMongoDao.deleteTableDataByDelArg(arg);
        System.out.println(deleteResult);
    }
}
