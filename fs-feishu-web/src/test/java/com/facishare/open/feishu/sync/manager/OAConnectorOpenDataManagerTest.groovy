package com.facishare.open.feishu.sync.manager

import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataManager
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class OAConnectorOpenDataManagerTest extends Specification {
    @Resource
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    def "send"() {
        given:
        OAConnectorOpenDataModel model = new OAConnectorOpenDataModel()
        model.setAppId("cli_a3ddeb52763b100c")
        model.setOutUserId("ou_7b0c467a330fb01c6ca078478ae53581")
        model.setEa("fszdbd2575")
        model.setChannelId("feishu")
        model.setCorpId("16bdc45070d5975f")
        model.setDataTypeId("employeeLogin")
        model.setErrorCode(errorCode)
        expect:
        oaConnectorOpenDataManager.send(model)
        where:
        errorCode || result
        "100" || null
        "101" || null
        "102" || null
    }
}
