package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.ExternalTodoInstanceManager;
import com.facishare.open.feishu.syncapi.entity.ExternalTodoInstanceEntity;
import com.facishare.open.feishu.syncapi.enums.ApprovalStatusEnum;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskUpdateDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalTaskResult;
import com.facishare.open.feishu.syncapi.result.ExternalInstancesDetailResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import com.facishare.open.feishu.syncapi.vo.InstanceVo.ExternalMessageInstanceVo;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.controller.inner.feishu.FeishuInnerInternalController;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.google.common.collect.Lists;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindStatusEnum;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

public class ContactScopeUpdateV3EventHandlerTest extends BaseTest {
    @Resource
    private ContactScopeUpdateV3EventHandler contactScopeUpdateV3EventHandler;
    @Autowired
    private FeishuTenantService feishuTenantService;
    @Autowired
    private ContactUserCreatedV3EventHandler contactUserCreatedV3EventHandler;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private ContactDepartmentCreatedV3EventHandler contactDepartmentCreatedV3EventHandler;
    @Autowired
    private ExternalMessageHandler externalMessageHandler;
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ExternalTodoInstanceManager externalTodoInstanceManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private ExternalApprovalsService externalApprovalsService;

    @Test
    public void handle() {
        String msgText="";
        DeleteTodoArg deleteTodoArg
                = new DeleteTodoArg();
        deleteTodoArg.setEa("91449");
        deleteTodoArg.setEi(91449);
        deleteTodoArg.setSourceId("680efb0507983d740d654043");
        deleteTodoArg.setBizType("452");
        deleteTodoArg.setDeleteEmployeeIds(Lists.newArrayList(1000));
        LogUtils.info("externalMessageListener.consumeMessage.deleteTodoArg={}", deleteTodoArg);
        externalMessageHandler.dealDeleteTodoHandler(deleteTodoArg);





        processOriginData("85903", 1738068246000L, 1745082034108L);
        feishuTenantService.initQueryTenantInfo("cli_a75815139bbc500e","LZMQ9BHfCczQ4jeXlAsjkcXvzNAixLhg","feishu.cn");
        String eventData = "{\"removed\":{},\"added\":{\"user_groups\":[{\"name\":\"CRM\",\"member_count\":4,\"type\":1,\"user_group_id\":\"7837a79fc3c54bae\",\"status\":1}]}}";
        FeishuEventModel2.EventModelHeader header = new FeishuEventModel2.EventModelHeader();
        header.setAppId(testAppId);
        header.setTenantKey(testTenantKey);
        contactScopeUpdateV3EventHandler.handle(null,eventData);
    }

    /**
     * 处理单个企业的数据
     */
    private void processOriginData(String todoEa, Long startTime, Long endTime) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, todoEa, null, null);
        if (enterpriseBindEntity == null) {
            LogUtils.warn("企业绑定信息不存在, ea: {}", todoEa);
            return;
        }
        try {
            Date startDate = new Date(startTime);
            Date endDate = new Date(endTime);
            Integer tenantId = eieaConverter.enterpriseAccountToId(todoEa);
            // 创建查询参数
            // 待办事项处理
            Boolean isPageToDo = true;
            Integer pageToDoNum = 1;  // 从第1页开始
            Integer pageToDoSize = 20;  // 每页获取20条记录
            while (isPageToDo) {
                IPage<ExternalTodoInstanceEntity> todoPage = externalTodoInstanceManager.pageQueryBetweenTime(enterpriseBindEntity.getFsEa(),
                        pageToDoNum, pageToDoSize,startDate,endDate);
                List<ExternalTodoInstanceEntity> records = todoPage.getRecords();
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(records)) {
                    isPageToDo = false;
                    break;
                }
                List<OuterOaMessageBindEntity> messageBindEntities = Lists.newArrayList();
                for (ExternalTodoInstanceEntity record : records) {
                    if (record.getStatus().equals(0)) {
                        //需要处理的审批
                        LogUtils.info("message bind data sourceId:{}", record.getSourceId());
                        OuterOaMessageBindParams outerOaMessageBindParams = new OuterOaMessageBindParams();
                        outerOaMessageBindParams.setFsEa(record.getFsEa());
                        outerOaMessageBindParams.setChannel(ChannelEnum.feishu);
                        outerOaMessageBindParams.setSourceId(record.getSourceId());
                        List<OuterOaMessageBindEntity> entities = outerOaMessageBindManager.getEntities(outerOaMessageBindParams);
                        if (ObjectUtils.isNotEmpty(entities)) {
                            for (OuterOaMessageBindEntity message : entities) {
                                try {
                                    //需要拿到待办的消息
                                    List<ObjectData> objectData = queryApprovalTaskData(tenantId, message.getSourceId());
                                    if (ObjectUtils.isNotEmpty(objectData)) {
                                        ObjectData itemData = objectData.get(0);
                                        String state = String.valueOf(itemData.get("state"));
                                        if (!state.equals("in_progress")) {
                                            if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.todo)) {
                                                //需要根据detail，重新推送
                                                ExternalMessageInstanceVo externalMessageInstanceVo = convertMessageInfo(message.getMessageInfo());
                                                ExternalInstancesDetail externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
                                                for (ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
                                                    task.setStatus(ApprovalStatusEnum.APPROVED.name());
                                                }
                                                // 组装数据
                                                externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                                Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), message.getAppId(), externalInstancesDetail);
                                                LogUtils.info("external approval taskids:{}", instancesDetailResultResult);
                                                message.setMessageInfo(JSON.toJSONString(externalMessageInstanceVo));
                                                message.setStatus(OuterOaMessageBindStatusEnum.approved);
                                                message.setUpdateTime(System.currentTimeMillis());
                                                outerOaMessageBindManager.updateById(message);
                                            } else {
                                                if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.bot)) {
                                                    //bot消息
                                                    ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
                                                    externalApprovalsTaskUpdateDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                                    externalApprovalsTaskUpdateDetail.setMessageId(message.getTaskId());
                                                    Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(message.getOutEa(), message.getAppId(), externalApprovalsTaskUpdateDetail);
                                                    LogUtils.info("external approval taskids:{}", externalApprovalTaskResultResult);
                                                    message.setUpdateTime(System.currentTimeMillis());
                                                    message.setStatus(OuterOaMessageBindStatusEnum.approved);
                                                    outerOaMessageBindManager.updateById(message);
                                                }
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    LogUtils.warn("fix error approval for message: {}", message.getId(), e);
                                }
                            }
                        }

                    }
                }

                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(messageBindEntities)) {
                    Integer count = outerOaMessageBindManager.batchUpsertInfos(messageBindEntities);
                    LogUtils.info("批量更新待办事项成功，当前页：{}，更新数量：{}", pageToDoNum, count);
                }

                pageToDoNum++;
                if (pageToDoNum > todoPage.getPages()) {
                    isPageToDo = false;
                }
            }


        }catch (Exception e){
            LogUtils.error("fix error approval for message: {}", e);
        }
        return;
    }

    private ExternalMessageInstanceVo convertMessageInfo(String messageInfo){
        ExternalMessageInstanceVo externalMessageInstanceVo = JSONObject.parseObject(messageInfo, new TypeReference<ExternalMessageInstanceVo>() {
        });
        //兼容之前的数据
        ExternalInstancesDetail externalInstancesDetail=null;
        if(ObjectUtils.isEmpty(externalMessageInstanceVo)||ObjectUtils.isEmpty(externalMessageInstanceVo.getTodoDetail())){
            externalMessageInstanceVo=new ExternalMessageInstanceVo();
            externalInstancesDetail = JSONObject.parseObject(messageInfo, new TypeReference<ExternalInstancesDetail>() {
            });
            externalMessageInstanceVo.setTodoDetail(externalInstancesDetail);

        }else{
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        }
        return externalMessageInstanceVo;
    }
        /**
         * 查询crm审批数据
         *
         * @param tenantId
         * @param dataId
         * @return
         */
        public List<ObjectData> queryApprovalTaskData(Integer tenantId, String dataId) {

            HeaderObj headerObj =HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
            String erpOrgObj = "ApprovalTaskObj";
            ControllerListArg listArg = new ControllerListArg();
            List<String> fieldValues = Lists.newArrayList();
            fieldValues.add(dataId);
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(10);
            searchQuery.addFilter("_id", fieldValues, "In");
            listArg.setSearchQuery(searchQuery);
            com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
            LogUtils.info("ApprovalTaskManager data={}", dataListRes);
            if (!dataListRes.isSuccess()) {
                LogUtils.warn("list erp org obj failed,tenantId:{},res:{}", tenantId, dataListRes);
                return null;
            }
            return dataListRes.getData().getDataList();
        }

        @Test
    public void testUserCreate(){


//        OuterOaEmployeeBindParams outerOaEmployeeBindParams=OuterOaEmployeeBindParams.builder().outEa("142f6d8a511b975e").build();
//        Page<OuterOaEmployeeBindEntity> entityPage = outerOaEmployeeBindManager.getPageEntities(outerOaEmployeeBindParams);
        FeishuEventModel2.EventModelHeader header = new FeishuEventModel2.EventModelHeader();
        header.setAppId("cli_a75815139bbc500e");
        header.setTenantKey("142f6d8a511b975e");
        String eventData="{\n" +
                "    \"object\": {\n" +
                "        \"country\": \"\",\n" +
                "        \"work_station\": \"\",\n" +
                "        \"gender\": 0,\n" +
                "        \"city\": \"\",\n" +
                "        \"open_id\": \"ou_ff83f8e19f43c690a943f19045bcd3be\",\n" +
                "        \"mobile\": \"+8618909808800\",\n" +
                "        \"employee_no\": \"\",\n" +
                "        \"avatar\": {\n" +
                "            \"avatar_640\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "            \"avatar_origin\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "            \"avatar_72\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\n" +
                "            \"avatar_240\": \"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00lf_c1e71d93-2719-4c9f-9301-dfcd093808dg~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"\n" +
                "        },\n" +
                "        \"department_ids\": [\n" +
                "            \"od-993a12df001fa30db7461a7ce5651ca8\"\n" +
                "        ],\n" +
                "        \"enterprise_email\": \"\",\n" +
                "        \"join_time\": 1744934400,\n" +
                "        \"employee_type\": 1,\n" +
                "        \"name\": \"小南\",\n" +
                "        \"nickname\": \"\",\n" +
                "        \"union_id\": \"on_272b71006fa65ecb29d5aab107f82694\",\n" +
                "        \"en_name\": \"\",\n" +
                "        \"orders\": [\n" +
                "            {\n" +
                "                \"user_order\": 0,\n" +
                "                \"department_id\": \"od-993a12df001fa30db7461a7ce5651ca8\",\n" +
                "                \"is_primary_dept\": true,\n" +
                "                \"department_order\": 1\n" +
                "            }\n" +
                "        ],\n" +
                "        \"job_title\": \"\",\n" +
                "        \"status\": {\n" +
                "            \"is_activated\": false,\n" +
                "            \"is_frozen\": false,\n" +
                "            \"is_resigned\": false,\n" +
                "            \"is_unjoin\": true,\n" +
                "            \"is_exited\": false\n" +
                "        }\n" +
                "    }\n" +
                "}";
       // String eventData="{\"object\":{\"open_department_id\":\"od-618463f883fa12487ed2e08014e446ea\",\"department_id\":\"1a7bf7711f3c45ed\",\"name\":\"测试atuo\",\"order\":2000,\"parent_department_id\":\"od-6fea9c309df06c35e1247a9f68ee0783\",\"status\":{\"is_deleted\":false}}}";
        contactUserCreatedV3EventHandler.handle(header,eventData);
//        contactDepartmentCreatedV3EventHandler.handle(header,eventData);
    }


    @Test
    public void testScope(){
        String eventData="{\"removed\":{\"departments\":[{\"open_department_id\":\"od-bfab05e0b76cc9b1ab3f63d3e3cc5a2d\",\"department_id\":\"36dec7d99f97a739\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"销售中心\",\"member_count\":79,\"order\":\"8000\",\"parent_department_id\":\"0\",\"status\":{\"is_deleted\":false}}]},\"added\":{}}";
        FeishuEventModel2.EventModelHeader header = new FeishuEventModel2.EventModelHeader();
        header.setAppId("cli_a3ddeb52763b100c");
        header.setTenantKey("100d08b69448975d");
    contactScopeUpdateV3EventHandler.handle(header,eventData);
    }

}

