package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.ContactScopeData;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.service.FeishuContactsService;
import com.facishare.open.feishu.syncapi.service.FeishuDepartmentService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class FeishuContactsServiceTest extends BaseTest {
    @Resource
    private FeishuContactsService feishuContactsService;
    @Resource
    private FeishuDepartmentService feishuDepartmentService;

    @Test
    public void getContactScopeData(){
        Result<ContactScopeData> result = feishuContactsService.getContactScopeData("cli_a7fcf7b29af85003", "14121dcad548175a");
        List<DepartmentData.Department> dataList = new ArrayList<>();
        if(result.isSuccess()) {
            for(String depId : result.getData().getDepartmentIds()) {
                Result<DepartmentData.Department> deptInfo = feishuDepartmentService.getDeptInfo("cli_a20192f6afb8d00c",
                        "11b1a8c266da9758",
                        depId);
                dataList.add(deptInfo.getData());
                System.out.println(deptInfo);
                Result<List<DepartmentData.Department>> childDept = feishuDepartmentService.getChildDept("cli_a20192f6afb8d00c",
                        "11b1a8c266da9758", depId, true);
                dataList.addAll(childDept.getData());
                System.out.println(childDept);
            }
        }
        System.out.println(result);
    }
}
