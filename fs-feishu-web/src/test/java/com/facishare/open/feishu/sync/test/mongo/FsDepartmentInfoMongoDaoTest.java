package com.facishare.open.feishu.sync.test.mongo;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.oa.base.dbproxy.mongo.dao.FsDepartmentInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsDepartmentInfoDoc;
import com.mongodb.client.result.DeleteResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;

public class FsDepartmentInfoMongoDaoTest extends BaseTest {
    @Autowired
    private FsDepartmentInfoMongoDao fsDepartmentInfoMongoDao;

    @Test
    public void batchReplace() {

    }

    @Test
    public void countDocuments() {
        Long counts = fsDepartmentInfoMongoDao.countDocuments("fszdbd2575");
        System.out.println(counts);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        DeleteResult deleteResult = fsDepartmentInfoMongoDao.deleteNotInCollectionDocs("85903", new LinkedList<>());
        System.out.println(deleteResult);
    }

    @Test
    public void addIndex() {
        fsDepartmentInfoMongoDao.addIndex();
    }
}
