package com.facishare.open.feishu.sync.test.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.manager.OrderInfoManager;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaOrderInfoManager;
import org.junit.Test;

import javax.annotation.Resource;

public class OrderInfoManagerTest extends BaseTest {
    @Resource
    private OuterOaOrderInfoManager orderInfoManager;

    @Test
    public void insert() {
        String json = "{\"appId\":\"cli_a20192f6afb8d00c\",\"beginTime\":1659943371000,\"buyType\":\"buy\",\"channel\":\"feishu\",\"editionId\":\"price_a25f5f67a172d00e\",\"editionName\":\"FEISHU_PRO\",\"endTime\":1661239371000,\"orderCorpId\":\"11b6fe24b58e175d\",\"orderDays\":15,\"orderFrom\":\"customer\",\"orderId\":\"7129402494660526082\",\"orderStatus\":\"normal\",\"orderTime\":1659943372000,\"paidCorpId\":\"11b6fe24b58e175d\",\"payPrice\":0,\"payTime\":1659943371000,\"price\":0,\"pricePlanType\":\"trial\",\"srcOrderId\":\"\",\"userCount\":10000}";
        OuterOaOrderInfoEntity entity = JSONObject.parseObject(json,OuterOaOrderInfoEntity.class);
        int insert = orderInfoManager.insertOrUpdateOrderInfo(entity);
        System.out.println(insert);
    }

    @Test
    public void getLatestOrder() {
        OuterOaOrderInfoEntity entity = orderInfoManager.getLatestOrder("1062af3ebe60575d");
        System.out.println(entity);
    }
}
