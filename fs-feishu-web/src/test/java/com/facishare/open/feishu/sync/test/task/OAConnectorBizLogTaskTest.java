package com.facishare.open.feishu.sync.test.task;

import cn.hutool.core.thread.ThreadUtil;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.sync.manager.CustomUrlInfoManager;
import com.facishare.open.feishu.syncapi.entity.CustomUrlInfoEntity;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorBizLogTask;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.outer.oa.connector.common.api.enums.DataTypeEnum;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.model.login.LoginAuthModel;
import com.facishare.open.feishu.web.template.inner.login.FeishuLoginTemplate;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

public class OAConnectorBizLogTaskTest extends BaseTest {
    @Resource
    private OAConnectorBizLogTask oaConnectorBizLogTask;
    @Autowired
    private CustomUrlInfoManager customUrlInfoManager;
    @Autowired
    private FeishuLoginTemplate feishuLoginTemplate;


    @Test
    public void test() {
        OAConnectorOpenDataModel model = new OAConnectorOpenDataModel();
        model.setDataTypeId(DataTypeEnum.EMPLOYEE_LOGIN.getDataType());
        oaConnectorBizLogTask.add(model);
        ThreadUtil.waitForDie();
    }
   @Test
    public void testChannel(){
       String data = ChannelEnum.feishu.toString();
       System.out.println(data);
       List<CustomUrlInfoEntity> customUrlInfoEntities =
               customUrlInfoManager.queryEntities(ChannelEnum.feishu);
       System.out.println(customUrlInfoEntities);
   }

   @Test
    public void testConnectLogin(){
        String code="9bft2cc02ec4413aa2bce00be10e447k";
        String state="cli_a7c94d9508f85009";
       LoginAuthModel feishuAuthModel=new LoginAuthModel(code,state,null);

       MethodContext context = MethodContext.newInstance(feishuAuthModel);
       feishuLoginTemplate.getOutUserInfoByCode(context);
   }
}
