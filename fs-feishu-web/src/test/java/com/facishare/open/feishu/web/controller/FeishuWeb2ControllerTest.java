package com.facishare.open.feishu.web.controller;

import com.facishare.open.feishu.sync.service.impl.OrderServiceImpl;
import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.JsApiService;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuWeb2Controller;
import com.facishare.open.feishu.web.utils.SecurityUtil;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FeishuWeb2ControllerTest extends BaseTest {
    @Resource
    private FeishuWeb2Controller feishuWeb2Controller;
    @Resource
    private JsApiService jsApiService;
    @Autowired
    private OrderService orderService;


    @Test
    public void uploadEmployeesBindSyncFile() throws IOException {
        String filePath = "D:\\Temp\\employeeBindSyncFile.xlsx";
        MultipartFile multipartFile = convert(filePath);
        Result<Void> result = feishuWeb2Controller.uploadEmployeesBindSyncFile(multipartFile);
        System.out.println(result);
    }

    @Test
    public void getJsApiSignature() throws IOException {
        Result<JsApiSignatureModel> result = jsApiService.getJsApiSignature2("cli_a3ddeb52763b100c",
                "85903",
                null,
                "https://crm.ceshi112.com/hcrm/feishu");
        System.out.println(result);
    }

    private static MultipartFile convert(String filePath) throws IOException {
        File file = new File(filePath);
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file",
                file.getName(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", input);
        return multipartFile;
    }

    @Test
    public  void testCreateCustomer() throws UnsupportedEncodingException {
        String VER = "V1_";
        String userId="ou_3e6951e26a5e207c16c1e1c7870c72d3";
        String encode = URLEncoder.encode(SecurityUtil.encryptStr(VER + userId));
        String encodeValue="Quid5W3H5DQn0awS0ktdeNbWpZsfBNPw36QpkDfBksTrzsR7FwAKs0YmeAZ+9096";
        String decodeData = decodeData(URLDecoder.decode(encode, StandardCharsets.UTF_8.name()));

        String appId="xxxxxxxx";
        String channelName = Optional.ofNullable(ConfigCenter.FEISHU_LARK_APP_ID_CHANNEL)
                .map(map -> map.get(appId))
                .orElse(ChannelEnum.feishu.name());
//        String fsEa = orderService.genFsEa("LarkTest").getData();
//        CreateCustomerAndUpdateMappingArg createCustomerAndUpdateMappingArg=new CreateCustomerAndUpdateMappingArg();
//        createCustomerAndUpdateMappingArg.setAppId("cli_a7fcf7b29af85003");
//        createCustomerAndUpdateMappingArg.setEnterpriseName("LarkTest");
//        createCustomerAndUpdateMappingArg.setInstallerUserId("ou_36b3d195b1b2e5dfc9608e5c21ed49b1");
//        createCustomerAndUpdateMappingArg.setOutEa("14121dcad548175a");
//        createCustomerAndUpdateMappingArg.setOutEid("LJRN8OAGV0M");
//        createCustomerAndUpdateMappingArg.setInstallerName("测试用户");
//        createCustomerAndUpdateMappingArg.setFsEa(fsEa);
//        Result<Void> customerAndUpdateMapping = orderService.createCustomerAndUpdateMapping(createCustomerAndUpdateMappingArg);

        CreateOrderArg createOrderArg = new CreateOrderArg();
        createOrderArg.setOutEa("14121dcad548175a");
        createOrderArg.setOrderId("7157911302390729797");
        createOrderArg.setFsEa("feishu5044934");
        orderService.createOrder(createOrderArg);
    }
    private String decodeData(String data) {
        String VER = "V1_";
        Pattern pattern = Pattern.compile(VER+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }
}
