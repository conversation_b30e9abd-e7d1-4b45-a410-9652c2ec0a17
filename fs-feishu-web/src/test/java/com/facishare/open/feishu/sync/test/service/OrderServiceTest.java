package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.CrmOrderProductVo;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.OrderService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

public class OrderServiceTest extends BaseTest {
    @Resource
    private OrderService orderService;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

//    @Test
//    public void onOrderPaid() {
//        //String json = "{\"app_id\":\"cli_a3ddeb52763b100c\",\"buy_count\":1,\"buy_type\":\"buy\",\"create_time\":\"1666581096\",\"order_id\":\"7157911302390726660\",\"order_pay_price\":0,\"pay_time\":\"1666581096\",\"price_plan_id\":\"price_a3c531997577d013\",\"price_plan_type\":\"trial\",\"seats\":0,\"src_order_id\":\"\",\"tenant_key\":\"11c583e2e747575e\",\"type\":\"order_paid\"}";
//        //String json = "{\"tenant_key\":\"1708329cbe15975f\",\"buy_type\":\"buy\",\"create_time\":\"1693303123\",\"src_order_id\":\"\",\"buy_count\":1,\"type\":\"order_paid\",\"seats\":0,\"pay_time\":\"1693303123\",\"order_pay_price\":0,\"price_plan_id\":\"price_a467a17f0db41013\",\"price_plan_type\":\"active_end_date\",\"app_id\":\"cli_a20192f6afb8d00c\",\"order_id\":\"7272646247827652611\"}";
//        String json = "{\"tenant_key\":\"1661fd9cd07e975f\",\"buy_type\":\"buy\",\"create_time\":\"1694792797\",\"src_order_id\":\"\",\"buy_count\":1,\"type\":\"order_paid\",\"seats\":0,\"pay_time\":\"1694792797\",\"order_pay_price\":0,\"price_plan_id\":\"price_a47ad7b33f21100c\",\"price_plan_type\":\"trial\",\"app_id\":\"cli_a20192f6afb8d00c\",\"order_id\":\"7278989535207620609\"}";
//        FeishuOrderPaidEvent event = JSONObject.parseObject(json,FeishuOrderPaidEvent.class);
//        event.setPayTime(event.getPayTime() * 1000L);
//        event.setCreateTime(event.getCreateTime() * 1000L);
//        orderService.onOrderPaid(event);
//    }

    @Test
    public void getEnterpriseData() {
        List<CrmOrderProductVo> productVos = ConfigCenter.app_other_product_map.get("cli_a3ddeb52763b100c");
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> voidResult = orderService.buyConnectorConfigProductId("81961", "0", 1740827962000L, 1753787962000L, productVos.get(0).getProductId(), 1);
        System.out.println(voidResult);
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        //arg.setEnterpriseId(85268);
        arg.setEnterpriseAccount("aly6307547");
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        System.out.println(result);
    }

    @Test
    public void getEnterpriseTrialInfo() {

        Result<EnterpriseTrialInfo> result = orderService.getEnterpriseTrialInfo("84801",null);
        System.out.println(result);
    }
}
