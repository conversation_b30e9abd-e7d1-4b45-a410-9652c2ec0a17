package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.feishu.sync.manager.CalendarEventManager;
import com.facishare.open.feishu.sync.manager.CalendarManager;
import com.facishare.open.feishu.syncapi.entity.CalendarEventEntity;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.template.outer.event.order.FeishuOrderPaidEventHandlerTemplate;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

public class OrderPaidEventHandlerTest extends BaseTest {
    @Resource
    private OrderPaidEventHandler orderPaidEventHandler;
    @Resource
    private FeishuOrderPaidEventHandlerTemplate feishuOrderPaidEventHandlerTemplate;
    @Autowired
    private CalendarEventManager calendarEventManager;
    @Test
    public void handle(){

        Page<CalendarEventEntity> calendarEventEntityPage = calendarEventManager.queryPage("85903", 1, 10);


        String data="{\n" +
                "  \"orderId\": \"7494624514269052931\",\n" +
                "  \"pricePlanId\": \"price_a7e8c5803b56500d\",\n" +
                "  \"pricePlanType\": \"trial\",\n" +
                "  \"seats\": 0,\n" +
                "  \"buyCount\": 1,\n" +
                "  \"createTime\": 1744978748000,\n" +
                "  \"payTime\": 1744978748000,\n" +
                "  \"buyType\": \"buy\",\n" +
                "  \"srcOrderId\": \"\",\n" +
                "  \"orderPayPrice\": 0\n" +
                "}";
        FeishuOrderPaidEvent event = JSON.parseObject(data, FeishuOrderPaidEvent.class);
        event.setTenantKey("100d08b69448975d");
        event.setAppId("cli_a3ddeb52763b100c");
        event.setType("order_paid");
        feishuOrderPaidEventHandlerTemplate.execute(event).getDataOrMsg();
        String json = "{\"tenant_key\":\"11b6fe24b58e175d\",\"buy_type\":\"buy\",\"create_time\":\"1659943372\",\"src_order_id\":\"\",\"buy_count\":1,\"type\":\"order_paid\",\"seats\":0,\"pay_time\":\"1659943371\",\"order_pay_price\":0,\"price_plan_id\":\"price_a25f5f67a172d00e\",\"price_plan_type\":\"trial\",\"app_id\":\"cli_a20192f6afb8d00c\",\"order_id\":\"7129402494660526082\"}";
        String handle = orderPaidEventHandler.handle(null, json);
        System.out.println(handle);
    }
}
