package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.model.ContactScopeModel;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactDepartmentUpdateV3Event;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

public class ContactsServiceTest extends BaseTest {
    @Resource
    private ContactsService contactsService;
    @Autowired
    private ExternalTodoMsgService externalTodoMsgService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Test
    public void initContactsAsync() {
        Result<Void> result = contactsService.initContactsAsync("fsfxcs8780");
        System.out.println(result);
    }



    @Test
    public void addDepList() {

        String data="{\"old_object\":{},\"object\":{\"open_department_id\":\"od-b4bd8ed1341b1d1e850c38bc4058c033\",\"department_id\":\"5c2d6511fb368879\",\"name\":\"重试二级\",\"chat_id\":\"oc_3eed83c0e2e5e9153a442cf2b51d06e1\",\"order\":2000,\"parent_department_id\":\"od-8d91d09bf1e9c5e3ae9545409dfc15ae\",\"status\":{\"is_deleted\":false}}}";
        FeishuContactDepartmentUpdateV3Event feishuContactDepartmentUpdateV3Event=JSONObject.parseObject(data, FeishuContactDepartmentUpdateV3Event.class);
        List<DepartmentData.Department> departmentDataList = Lists.newArrayList(feishuContactDepartmentUpdateV3Event.getObject());
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=outerOaEnterpriseBindManager.getEntityById("67f5ce157e1ac00001a4ae91");
        Result<Void> result = contactsService.addDepList("cli_a75815139bbc500e","142f6d8a511b975e",departmentDataList,outerOaEnterpriseBindEntity);
        System.out.println(result);
    }

    @Test
    public void removeDepList() {
        String json = "[{\"open_department_id\":\"od-2210d8bfd7675447e7edfcb7e4051bb7\",\"department_id\":\"51ed62ff56fd38cc\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"开发2部\",\"ja_jp\":\"\"},\"name\":\"开发2部\",\"leader_user_id\":\"ou_d9292ea3f4405f4663083a15ffd9a333\",\"member_count\":2,\"chat_id\":\"oc_00ec955f1fe959c4c7b029c461d5e27c\",\"order\":\"4000\",\"parent_department_id\":\"0\",\"status\":{\"is_deleted\":false}}]";
        List<DepartmentData.Department> departmentDataList = JSONObject.parseArray(json, DepartmentData.Department.class);
        Result<Void> result = contactsService.removeDepList("cli_a20192f6afb8d00c","11b1a8c266da9758",departmentDataList,null);
        System.out.println(result);
    }

//    @Test
//    public void resetAdminRole() {
//        Result<Void> result = contactsService.resetAdminRole("cli_a20192f6afb8d00c","1062af3ebe60575d");
//        System.out.println(result);
//    }


    @Test
    public void autoGetContactData() {
        Result<Void> result = contactsService.autoGetContactData();
        System.out.println(result);
    }

    @Test
    public void saveOrUpdateContactData() {
        Result<ContactScopeModel> result = contactsService.saveOrUpdateContactData("cli_a7c94d9508f85009","146a0bcf64d7175a",ChannelEnum.feishu);
        System.out.println(result);
    }

    @Test
    public void refreshContactScopeDataCacheAsync() {
        Result<Void> result = contactsService.refreshContactScopeDataCacheAsync("contact.scope.updated_v3","cli_a3ddeb52763b100c","100d08b69448975d","{test}",ChannelEnum.feishu);
        System.out.println(result);
    }

    @Test
    public void saveOrUpdateContactUser() {
//        Result<Void> result = contactsService.saveOrUpdateContactUser("contact.user.created_v3","cli_a3ddeb52763b100c","100d08b69448975d",ChannelEnum.feishu,null, null, "");
//        System.out.println(result);
    }

    @Test
    public void deleteContactUser() {
        UserData.User user = new Gson().fromJson("{\"name\":\"小田\",\"en_name\":\"\",\"mobile_visible\":true,\"gender\":0,\"avatar\":{\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003d240x240\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\",\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003d640x640\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003d72x72\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v2_45f0085d-60f2-4b9f-894b-56a1d392e19g~?image_size\\u003dnoop\\u0026cut_type\\u003d\\u0026quality\\u003d\\u0026format\\u003dpng\\u0026sticker_format\\u003d.webp\"},\"status\":{\"is_activated\":false,\"is_exited\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":false},\"department_ids\":[\"od-23f0a91fc17c969a39d8aa43a6cd8361\"],\"city\":\"\",\"country\":\"\",\"work_station\":\"\",\"join_time\":\"Jan 1, 1970 8:00:00 AM\",\"is_tenant_manager\":true,\"employee_no\":\"\",\"employee_type\":1,\"orders\":[{\"department_id\":\"od-23f0a91fc17c969a39d8aa43a6cd8361\",\"department_order\":1,\"user_order\":0}],\"job_title\":\"\",\"unionId\":\"on_3e92a0c7d8a7c44bd29bec93e1b0ed4d\",\"openId\":\"ou_c83b9f3b92d1b1e4a9677e90c131d80a\"}", UserData.User.class);
        Result<Void> result = contactsService.deleteContactUser(ChannelEnum.feishu,"cli_a3ddeb52763b100c","100d08b69448975d", user);
        System.out.println(result);
    }

    @Test
    public void deleteContactDepartment() {

        String plainText="{\n" +
                "    \"action_type\": \"APPROVE\",\n" +
                "    \"action_context\": \"{\\\"MsgType\\\":\\\"commonMsg\\\",\\\"fsEa\\\":\\\"91449\\\",\\\"outEa\\\":\\\"100d08b69448975d\\\",\\\"fsUserId\\\":\\\"1207\\\",\\\"taskId\\\":\\\"67fd02c58566925888a53a46\\\"}\",\n" +
                "    \"user_id\": \"28dgbc55\",\n" +
                "    \"message_id\": 7493149079577313299,\n" +
                "    \"token\": \"BiyJ6wa7yhYe6nvXNcZBOe0qix46kgWO001\"\n" +
                "}";
        ExternalDealTodoEvent externalDealTodoEvent = JSON.parseObject(plainText, ExternalDealTodoEvent.class);
        Result<Void> voidResult = externalTodoMsgService.dealCrmTodo(externalDealTodoEvent);
        DepartmentData.Department department = new Gson().fromJson("{\"name\":\"测试部\",\"parentDepartmentId\":\"0\",\"departmentId\":\"gffee66da84e3cca\",\"openDepartmentId\":\"od-a8d4f07729fbc2d30e6947c5dcdc23cf\",\"chatId\":\"oc_ab305ebbdc20c7469c65dd36d9465d7a\",\"order\":\"2000\",\"memberCount\":2,\"i18nName\":{\"zhCn\":\"\",\"jaJp\":\"\",\"enUs\":\"\"},\"status\":{\"isDeleted\":false}}", DepartmentData.Department.class);
        Result<Void> result = contactsService.deleteContactDepartment(ChannelEnum.feishu,"cli_a3ddeb52763b100c","100d08b69448975d", department);
        System.out.println(result);
    }


}
