package com.facishare.open.feishu.sync.service.whatsapp


import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappSendMsg
import com.facishare.open.feishu.syncapi.result.Result
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappSendMsgResult
import com.facishare.open.feishu.syncapi.service.whatsapp.WhatsappSendMsgService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class WhatsappSendMsgServiceTest extends Specification {
    @Resource
    private WhatsappSendMsgService whatsappSendMsgService;

    def "WhatsappGetMedia"() {
        expect:
        WhatsappSendMsg sendMsg = new WhatsappSendMsg()
        sendMsg.setFsEa("88146")
        sendMsg.setBusiness_phone("8613520614030")
        sendMsg.setTo("8617704079433")
//        sendMsg.setType("text")
//        WhatsappSendMsg.TextModel text = new WhatsappSendMsg.TextModel()
//        text.setBody("nihaohaode")
//        sendMsg.setText(text)
        sendMsg.setType("image")
        WhatsappSendMsg.ImageModel image = new WhatsappSendMsg.ImageModel()
        image.setNpath("N_202401_16_07bb27ba1c7749deb74504a4fc2ca80d")
        image.setFilename("test001.png")
        image.setType("image/png")
        sendMsg.setImage(image)

//        String x = "{\"fsEa\":\"84883\",\"business_phone\":\"8613520614030\",\"to\":\"8617704079433\",\"type\":\"template\",\"template\":{\"name\":\"event_invitation\",\"language\":{\"code\":\"zh_CN\",\"policy\":\"deterministic\"},\"components\":[{\"type\":\"body\",\"parameters\":[{\"type\":\"text\",\"text\":\"David\"},{\"type\":\"text\",\"text\":\"深圳\"}]}]}}";
//        WhatsappSendMsg sendMsg = JSONObject.parseObject(x, WhatsappSendMsg.class);
        Result<WhatsappSendMsgResult> a = whatsappSendMsgService.whatsappSendMsg(sendMsg)
        print(a)
    }
}
