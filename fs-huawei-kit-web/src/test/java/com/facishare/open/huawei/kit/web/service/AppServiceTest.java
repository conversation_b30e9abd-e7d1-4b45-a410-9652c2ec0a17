package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.huawei.kit.web.BaseTest;
import com.facishare.open.huawei.kit.web.entity.AppInfoEntity;
import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

public class AppServiceTest extends BaseTest {
    @Resource
    private AppService appService;
    @Autowired
    private FsOrderServiceProxy fsOrderServiceProxy;
//    @Test
//    public void updateAppInfo() {
//        AppInfoEntity entity = new AppInfoEntity();
//        entity.setAppId("appid2");
//        entity.setTenantKey("tenant key");
//        entity.setApplicants("1111");
//        entity.setOperator("operator");
//        Result<Integer> result = appService.updateAppInfo(entity);
//        System.out.println(result);
//    }

    @Test
    public void createCustomer2() {
        CreateCustomerArg customerObject = new CreateCustomerArg();
        customerObject.setEnterpriseName("企微转译问题");
        customerObject.setEnterpriseAccount("xkhdx002");
        customerObject.setManagerName("张三");
        customerObject.setManagerMobile("***********");
        customerObject.setChannel(ChannelEnum.qywx.name());
        customerObject.setOutEid("xkhdx002");


        com.facishare.open.order.contacts.proxy.api.result.Result<String> result = fsOrderServiceProxy.createCustomer2(customerObject);
        System.out.println(result);
    }
}
