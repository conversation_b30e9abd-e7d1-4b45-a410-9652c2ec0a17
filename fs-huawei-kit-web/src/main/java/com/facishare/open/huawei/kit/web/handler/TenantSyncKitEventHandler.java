package com.facishare.open.huawei.kit.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.CorpService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.oa.base.dbproxy.pg.entity.HuaweiInstanceIdBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.HuaweiInstanceIdBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.HuaweiInstanceIdBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.admin.HuaweiEnterpriseConnectVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户同步同步事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class TenantSyncKitEventHandler extends HuaweiEventHandler {

    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Resource
    private CorpService corpService;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private HuaweiInstanceIdBindManager huaweiInstanceIdBindManager;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.TENANT_SYNC;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        List<HuaweiInstanceIdBindEntity> instanceIdBindEntities = huaweiInstanceIdBindManager.getEntities(HuaweiInstanceIdBindParams.builder()
                .instanceId(kitVerifyTemplateData.getIsvProduceReq().get("instanceId").toString())
                .build());
        if (CollectionUtils.isEmpty(instanceIdBindEntities)) {
            HuaweiInstanceIdBindEntity instanceIdBindEntity = new HuaweiInstanceIdBindEntity();
            instanceIdBindEntity.setOutEa(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
            instanceIdBindEntity.setInstanceId(kitVerifyTemplateData.getIsvProduceReq().get("instanceId").toString());
            instanceIdBindEntity.setCreateTime(System.currentTimeMillis());
            instanceIdBindEntity.setUpdateTime(System.currentTimeMillis());
            Integer count = huaweiInstanceIdBindManager.insert(instanceIdBindEntity);
            log.info("TenantSyncKitEventHandler.handle,instanceIdBindEntity={},insert,count={}", instanceIdBindEntity, count);
        }

        //更新企业连接信息
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder()
                .channel(ChannelEnum.huawei)
                .outEa(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString())
                .appId(ConfigCenter.HUAWEI_APP_ID)
                .build());

        if (CollectionUtils.isNotEmpty(enterpriseBindEntities)) {
            OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
            HuaweiEnterpriseConnectVo enterpriseConnectParams = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), HuaweiEnterpriseConnectVo.class);
            enterpriseConnectParams.setOutDomain(kitVerifyTemplateData.getIsvProduceReq().get("domainName").toString());
            enterpriseConnectParams.setOutEnterpriseName(kitVerifyTemplateData.getIsvProduceReq().get("name").toString());
            enterpriseBindEntity.setConnectInfo(JSON.toJSONString(enterpriseConnectParams));
            enterpriseBindEntity.setBindStatus(BindStatusEnum.normal);
            enterpriseBindEntity.setUpdateTime(System.currentTimeMillis());
            Integer count = outerOaEnterpriseBindManager.updateById(enterpriseBindEntity);
            log.info("TenantSyncKitEventHandler.handle,enterpriseBindEntity={},count={}", enterpriseBindEntity, count);
        }
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        return TemplateResult.newSuccess(Result2.newSuccess());
    }
}
