package com.facishare.open.huawei.kit.web.utils;

import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;

import java.util.Map;

import static com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant.INTERNATIONAL_PROTOCOL;
import static com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant.SIGNATURE_ALGORITHM_SDK_HMAC_SHA256;

@Slf4j
public class HuaweiApiUtil {

    /**
     * 执行华为云API的GET方法，使用AK/SK签名认证
     * @param url
     * @param requestParams
     * @return
     * @throws Exception
     */
    public static String doGET(String url, Map<String, String> requestParams) throws Exception {

        // Create a new request.
        com.cloud.apigateway.sdk.utils.Request httpClientRequest = new com.cloud.apigateway.sdk.utils.Request();
        try {
            httpClientRequest.setKey(ConfigCenter.HUAWEICLOUD_SDK_AK);
            httpClientRequest.setSecret(ConfigCenter.HUAWEICLOUD_SDK_SK);
            httpClientRequest.setMethod("GET");
            httpClientRequest.setUrl(url);
             httpClientRequest.addHeader("Content-Type", "application/json");
            requestParams.forEach(httpClientRequest::addQueryStringParam);
        } catch (Exception e) {
            log.info("HuaweiApiUtil doGET error! ", e);
            return null;
        }
        CloseableHttpClient client = null;
        try {
            // Sign the request.
            HttpRequestBase signedRequest = com.cloud.apigateway.sdk.utils.Client.sign(httpClientRequest, SIGNATURE_ALGORITHM_SDK_HMAC_SHA256);
            if (ConfigCenter.DO_VERIFY) {
                // creat httpClient and verify ssl certificate
                HostName.setUrlHostName(httpClientRequest.getHost());
                client = (CloseableHttpClient) SSLCipherSuiteUtil.createHttpClientWithVerify(INTERNATIONAL_PROTOCOL);
            } else {
                // creat httpClient and do not verify ssl certificate
                client = (CloseableHttpClient) SSLCipherSuiteUtil.createHttpClient(INTERNATIONAL_PROTOCOL);
            }
            HttpResponse response = client.execute(signedRequest);
            // Print the body of the response.
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                String result = EntityUtils.toString(resEntity, "UTF-8");
                log.info("Processing Body with value: {}", result);
                return result;
            }
        } catch (Exception e) {
            log.info("HuaweiApiUtil doGET error! ", e);
        } finally {
            if (client != null) {
                client.close();
            }
        }
        return null;
    }
}
