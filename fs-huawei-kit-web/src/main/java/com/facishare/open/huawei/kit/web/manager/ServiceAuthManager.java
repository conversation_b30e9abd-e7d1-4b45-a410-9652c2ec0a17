package com.facishare.open.huawei.kit.web.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.huawei.kit.web.entity.ServiceAuthEntity;
import com.facishare.open.huawei.kit.web.mapper.ServiceAuthMapper;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * tb_service_auth表管理器
 * <AUTHOR>
 * @date 20240221
 */
@Component
public class ServiceAuthManager {
    @Resource
    private ServiceAuthMapper serviceAuthMapper;

    public List<ServiceAuthEntity> query(String fsService) {
        LambdaQueryWrapper<ServiceAuthEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ServiceAuthEntity::getFsService, fsService);
        List<ServiceAuthEntity> serviceAuthEntities = serviceAuthMapper.selectList(wrapper);
        return serviceAuthEntities;
    }

    public Integer insert(ServiceAuthEntity entity) {
        int count = serviceAuthMapper.insert(entity);
        LogUtils.info("ServiceAuthManager.insert,count={}",count);
        return count;
    }
}
