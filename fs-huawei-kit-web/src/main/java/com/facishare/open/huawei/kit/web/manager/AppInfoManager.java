package com.facishare.open.huawei.kit.web.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.huawei.kit.web.entity.AppInfoEntity;
import com.facishare.open.huawei.kit.web.mapper.AppInfoMapper;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class AppInfoManager {
    @Resource
    private AppInfoMapper appInfoMapper;

    public AppInfoEntity getEntity(String tenantKey) {
        LambdaQueryWrapper<AppInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppInfoEntity::getTenantKey,tenantKey);

        AppInfoEntity entity = appInfoMapper.selectOne(wrapper);

        return entity;
    }

    /**
     * 获取应用安装人员openId
     * @param tenantKey
     * @return
     */
    public String getInstallerOpenId(String tenantKey) {
        AppInfoEntity appInfoEntity = getEntity(tenantKey);
        if(appInfoEntity==null) return null;
        //管理员安装模式
        if(StringUtils.isNotEmpty(appInfoEntity.getInstallerOpenId())) {
            return appInfoEntity.getInstallerOpenId();
        }
        //普通成员安装模式
        return appInfoEntity.getInstallerEmployeeOpenId();
    }

    /**
     * 新增或更新应用信息表
     * @param entity
     * @return
     */
    public Integer updateAppInfo(AppInfoEntity entity) {
        LambdaQueryWrapper<AppInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppInfoEntity::getAppId,entity.getAppId());
        wrapper.eq(AppInfoEntity::getTenantKey,entity.getTenantKey());
        AppInfoEntity appInfoEntity = appInfoMapper.selectOne(wrapper);
        int count = 0;
        if(appInfoEntity!=null) {
            if(entity.getStatus()!=null) {
                appInfoEntity.setStatus(entity.getStatus());
            }
            if(StringUtils.isNotEmpty(entity.getApplicants())) {
                appInfoEntity.setApplicants(entity.getApplicants());
            }
            if(StringUtils.isNotEmpty(entity.getInstallerOpenId())) {
                appInfoEntity.setInstallerOpenId(entity.getInstallerOpenId());
            }
            if(StringUtils.isNotEmpty(entity.getInstallerEmployeeOpenId())) {
                appInfoEntity.setInstallerEmployeeOpenId(entity.getInstallerEmployeeOpenId());
            }
            if(StringUtils.isNotEmpty(entity.getOperator())) {
                appInfoEntity.setOperator(entity.getOperator());
            }
            appInfoEntity.setUpdateTime(new Date());
            count = appInfoMapper.updateById(appInfoEntity);
            LogUtils.info("AppInfoManager.updateAppInfo,updateById,count={}",count);
        } else {
            count = appInfoMapper.insert(entity);
            LogUtils.info("AppInfoManager.updateAppInfo,insert,count={}",count);
        }
        return count;
    }

    public List<AppInfoEntity> getAllEntity() {
        LambdaQueryWrapper<AppInfoEntity> wrapper = new LambdaQueryWrapper<>();
        List<AppInfoEntity> appInfoEntities = appInfoMapper.selectList(wrapper);
        LogUtils.info("AppInfoManager.getAllEntity,size={}",appInfoEntities.size());
        return appInfoEntities;
    }

    public AppInfoEntity getEntity(String tenantKey, String appId) {
        LambdaQueryWrapper<AppInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppInfoEntity::getTenantKey,tenantKey);
        wrapper.eq(AppInfoEntity::getAppId,appId);

        AppInfoEntity entity = appInfoMapper.selectOne(wrapper);

        return entity;
    }
}
