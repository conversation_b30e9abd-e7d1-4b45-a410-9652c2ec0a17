package com.facishare.open.huawei.kit.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.AppService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.utils.RSAEncryptionUtil;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.params.HuaweiAppInfoParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应用同步同步事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class ApplicationSyncKitEventHandler extends HuaweiEventHandler {

    @Resource
    private AppService appService;
    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.APPLICATION_SYNC;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        log.info("ApplicationSyncKitEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
        String clientSecret = "";
        try {
            clientSecret = RSAEncryptionUtil.decrypt(kitVerifyTemplateData.getIsvProduceReq().get("clientSecret").toString(), ConfigCenter.PRIVATE_KEY_CLIENT);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //应用同步，保存应用id
        String outEa = kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString();
        String appId = kitVerifyTemplateData.getIsvProduceReq().get("appId").toString();
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.huawei, outEa, appId);
        String clientId = kitVerifyTemplateData.getIsvProduceReq().get("clientId").toString();
        if (ObjectUtils.isNotEmpty(appInfoEntity)) {
            appInfoEntity.setStatus(OuterOaAppInfoStatusEnum.normal);
            appInfoEntity.setUpdateTime(System.currentTimeMillis());
            HuaweiAppInfoParams huaweiAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), HuaweiAppInfoParams.class);
            huaweiAppInfoParams.setClientId(clientId);
            huaweiAppInfoParams.setClientSecret(clientSecret);
            appInfoEntity.setAppInfo(JSON.toJSONString(huaweiAppInfoParams));
            Integer count = outerOaAppInfoManager.updateById(appInfoEntity);
            log.info("ApplicationSyncKitEventHandler.handle,update count={}",count);
        } else {
            appInfoEntity = new OuterOaAppInfoEntity();
            appInfoEntity.setChannel(ChannelEnum.huawei);
            appInfoEntity.setOutEa(outEa);
            appInfoEntity.setAppId(appId);
            appInfoEntity.setAppType(OuterOaAppInfoTypeEnum.isv);
            appInfoEntity.setStatus(OuterOaAppInfoStatusEnum.normal);
            appInfoEntity.setCreateTime(System.currentTimeMillis());
            appInfoEntity.setUpdateTime(System.currentTimeMillis());
            HuaweiAppInfoParams huaweiAppInfoParams = HuaweiAppInfoParams.builder()
                    .clientId(clientId)
                    .clientSecret(clientSecret)
                    .build();
            appInfoEntity.setAppInfo(JSON.toJSONString(huaweiAppInfoParams));
            Integer count = outerOaAppInfoManager.insert(appInfoEntity);
            log.info("ApplicationSyncKitEventHandler.handle,insert count={}",count);
        }
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        return TemplateResult.newSuccess(Result2.newSuccess());
    }
}
