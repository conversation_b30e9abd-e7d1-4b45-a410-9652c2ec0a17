package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.huawei.kit.web.arg.QueryEmployeeBindArg;
import com.facishare.open.huawei.kit.web.info.EmployeeBindInfo;
import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;

import java.util.List;

public interface EmployeeBindService {

//    Result<EmployeeBindInfo> queryEmployeeBind(String channel, QueryEmployeeBindArg arg);
//
//    Result<List<EmployeeBindInfo>> queryEmployeeBindListByOutData(ChannelEnum channel, String outEa, String outUserId);
}
