package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.template.outer.event.order.HuaweiSaveOrderHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.entity.HuaweiInstanceIdBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.HuaweiInstanceIdBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.HuaweiInstanceIdBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 更新实例事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class RefreshInstanceEventHandler extends HuaweiEventHandler {

    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Resource
    private HuaweiSaveOrderHandlerTemplate huaweiSaveOrderHandlerTemplate;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private HuaweiInstanceIdBindManager huaweiInstanceIdBindManager;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.REFRESH_INSTANCE;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        log.info("RefreshInstanceEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
        //更新实例
        //没有企业绑定关系时不做处理，通过instanceId查询corpInfo
        String refreshInstanceInstanceId = String.valueOf(kitVerifyTemplateData.getIsvProduceReq().get("instanceId"));
        List<HuaweiInstanceIdBindEntity> instanceIdBindEntities = huaweiInstanceIdBindManager.getEntities(HuaweiInstanceIdBindParams.builder()
                .instanceId(refreshInstanceInstanceId)
                .build());
        if(CollectionUtils.isEmpty(instanceIdBindEntities)) {
            return;
        }
        String outEa = instanceIdBindEntities.get(0).getOutEa();
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.huawei).outEa(outEa).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return;
        }
        //更新订单逻辑
        String refreshOrderId = String.valueOf(kitVerifyTemplateData.getIsvProduceReq().get("orderId"));
        HuaweiOrderDataModel refreshHuaweiOrderDataModel = new HuaweiOrderDataModel();
        refreshHuaweiOrderDataModel.setInstanceId(refreshInstanceInstanceId);
        refreshHuaweiOrderDataModel.setTenantId(outEa);
        refreshHuaweiOrderDataModel.setOrderId(refreshOrderId);

        TemplateResult refreshTemplateResult = huaweiSaveOrderHandlerTemplate.execute(refreshHuaweiOrderDataModel);
        log.info("HuaweiOuterInstanceTemplate.onEventHandle,refreshTemplateResult={}",refreshTemplateResult);
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        //更新实例
        //纷享无需创建实例，直接返回成功
        return TemplateResult.newSuccess(Result.newSuccess());
    }
}
