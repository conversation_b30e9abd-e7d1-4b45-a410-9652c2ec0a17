package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.info.AllOrgSyncInfo;
import com.facishare.open.huawei.kit.web.info.AuthSyncInfo;
import com.facishare.open.huawei.kit.web.result.Result2;
import com.facishare.open.huawei.kit.web.service.EnterpriseBindService;
import com.facishare.open.huawei.kit.web.service.HuaweiContactsService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 全量部门同步同步事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class AllOrgSyncKitEventHandler extends HuaweiEventHandler {
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private HuaweiContactsService huaweiContactsService;
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.ALL_ORG_SYNC;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        log.info("AllOrgSyncKitEventHandler.handle,kitVerifyTemplateData={}",kitVerifyTemplateData);
        Gson gson = new Gson();
        AllOrgSyncInfo allOrgSyncInfo = gson.fromJson(gson.toJson(kitVerifyTemplateData.getIsvProduceReq()), AllOrgSyncInfo.class);
        String outEa = kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString();
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, outEa, ConfigCenter.HUAWEI_APP_ID);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            //先存起来
            setTemRedis(allOrgSyncInfo.getTenantId(), getSupportEventType(), allOrgSyncInfo);
            return;
        }

        huaweiContactsService.syncAllDepartment(allOrgSyncInfo);
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        return TemplateResult.newSuccess(Result2.newSuccess());
    }

    private void setTemRedis(String outTenantId, String type, Object object) {
        log.info("AllOrgSyncKitEventHandler.setTempRedis,outTenantId={},type={},object={}",outTenantId, type, object);
        Gson gson = new Gson();
        String tem = redisDataSource.getRedisClient().get("huawei-kit2-sync-" + outTenantId);
        Map<String, Object> temSyncMap;
        if(StringUtils.isEmpty(tem)) {
            temSyncMap = new HashMap<>();
        } else {
            temSyncMap = gson.fromJson(tem, new TypeToken<Map<String, Object>>() {
            });
        }
        temSyncMap.put(type, object);
        redisDataSource.getRedisClient().set("huawei-kit2-sync-" + outTenantId, gson.toJson(temSyncMap));
        redisDataSource.getRedisClient().expire("huawei-kit2-sync-" + outTenantId, 60 * 10L);
    }
}
