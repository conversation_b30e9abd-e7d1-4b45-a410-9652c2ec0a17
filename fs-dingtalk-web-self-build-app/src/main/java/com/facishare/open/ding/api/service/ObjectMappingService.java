package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.model.FsUserInfoModel;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;

import java.util.List;
import java.util.Map;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-12 10:51
 */
public interface ObjectMappingService {

    /**
     * 根据EI查询绑定了的职员
     *
     * @param ei
     * @param appId
     * @return
     */
    Result<Map<String,Object>> queryMappingEmployee(BindEmpVo vo, Integer ei, String appId);

    /**
     * 根据EI查询钉钉中未同步的职员
     *
     * @param ei
     * @param appId
     * @return
     */
    Result<Map<String, Object>> queryNewEmployee(BindEmpVo vo, Integer ei, String ea, Integer userId, String appId);

    /**
     * 根据部门拉取员工
     */
     Result<Integer> insertMappingEmp(Dept dept, Integer ei, String clientIp, Long agentId, String appId);

    /**
     * 初始化后拉全量组织架构数据
     *
     * @param ei
     * @param ea
     * @param userId
     * @param appId
     * @return
     */
    Result<Map<String, Object>> syncPullOrganizationData(Integer ei, String ea, Integer userId, String appId);

    /**
     * 立即同步钉钉员工
     *
     * @param ei
     * @param userId
     * @param appId
     * @return
     */
    Result<Boolean> syncNewEmployee(Integer ei, Integer userId, String appId);

    /**
     * 原本：得到纷享全部的员工
     * 现在：增加userList,可以查询相应的员工的信息,传空值查询全部员工信息
     * @param ei
     * @return
     */
    Result<List<DingMappingEmployeeResult>> getEmployeeFs(Integer ei, List<Integer> userList);

    /**
     * 批量保存绑定的职员
     *
     * @param list
     * @param appId
     * @return
     */
    Result<Map<String,Integer>> bindEmployee(List<DingMappingEmployeeResult> list, Integer ei, Integer employeeId, boolean flag, String appId);

    /**
     * 解绑
     *
     * @param ei
     * @param cloudCode
     * @param appId
     * @return
     */
    Result<Integer> deleteBind(Integer ei, String cloudCode, String appId);

    /**
     * 保存设置
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    Result<Integer> saveOperation(DingMappingEmployeeResult dingMappingEmployeeResult, String appId);

    /**
     * 创建纷享职员
     *
     * @param vo
     * @param appId
     * @return
     */
    Result<Integer> createFxEmployee(EmployeeVo vo, String appId);

    /**
     * model2批量创建员工
     *
     * @param vo
     * @param ei
     * @param appId
     * @return
     */
    Result<Integer> batchCreateFxEmployee(List<CreateCrmEmployeeVo> vo, Integer ei, String appId);

    Result<Void> stopFxEmployee(Integer ei, Integer fxEmpId, String fxEmpName, String appId, String dingUserId);

    Result<Void> updateEmp(Integer ei, List<String> userIds, String appId);

    Result<Void> stopEmp(Integer ei, List<String> userIds, String appId);

    Result<DingMappingEmployeeResult> queryEmployeeByUnionId(Integer ei, String unionId, String appId);

    Result<DingMappingEmployeeResult> queryEmpByDingUserId(Integer ei, String dingUserId, String appId);

    Result<List<BindFxUserResult>> getBindEiAndUser(Integer offset, Integer limit, String appId);

    Result<List<BindFxEaResult>> getBindEa(Integer offset, Integer limit, String appId);

    //条件查询员工
    Result<ConditionEmployeeResult> conditionEmployee(QueryEmployeeVo queryEmployeeVo, Integer enterpriseId, String appId);
    //返回部门
    Result<List<DeptVo>> conditionDepts(Integer enterpriseId, String appId);

    //创建部门
    Result<Integer> createFxDept(Integer ei, List<Long> depts, String appId);

    //修改部门
    Result<Integer> modifyFxDept(Integer ei, List<Long> depts, String appId);

    //删除部门
    Result<Integer> removeFxDept(Integer ei, List<Long> depts, String appId);

    //创建部门负责人
   Result<Void> createDeptOwner(String clientIp,  String accessToken, String token,Integer ei, String appId);

   //重新映射部门
    Result<Void> againEmployeeMapping(Integer ei, String appId);

    //不依赖外部接口
    Result<Void> independenceAgainMapping(Integer ei, String appId);


    //模式二初始化部门人员
    Result<Void> initModelEmployee(Integer ei, Integer userId, String ea, String appId);

    //插入人员中间表
    Result<Void> allPullEmployeeInsert(Integer ei, Integer userId, String ea, String appId);

    //全量upsert部门信息
    Result<Void> upsertAllDept(Integer ei, Integer userId, String ea, String appId);

    /**
     * 删除员工
     * 
     * @param ei 企业ID
     * @param empId 钉钉员工ID
     * @param appId 应用ID
     * @return 删除结果
     */
    Result<Integer> deleteEmpByDingId(Integer ei, String empId, String appId);

    //新增CRM账号自动绑定
    Result<Void> autoSaveEmployeeAccount(CreateUserEventResult createUserEventResult, String appId);

    //新增钉钉账号自动绑定
    Result<Void> autoSaveDingEmployeeAccount(Integer ei, List<String> userIds, String appId);

    Result<Boolean> isGrayCorp(String ea);

    Result<Boolean> initAgainDept(Integer ei, String appId) throws InterruptedException;

    Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByFsIds(Integer ei, List<Integer> fsEmpIds, String appId);

    Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByDingIds(Integer ei, List<String> dingEmpIds, String appId);

    //更新员工姓名和手机号码
    Result<Integer> updateEmployeeAccountBindInfo(DingMappingEmployeeResult employeeResult, String appId);

    Result<List<FsUserInfoModel>> getFsUserInfo(String outEa, String outUserId, String appId);
}
