package com.facishare.open.ding.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ToolsService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingTodoManager;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import com.facishare.open.ding.provider.manager.TokenManager;
import com.facishare.open.ding.transfer.handler.DingTalkSelfBuildHandler;
import com.facishare.open.ding.web.manager.ExcelListener.BaseListener;
import com.facishare.open.ding.web.manager.FileManager;
import com.facishare.open.ding.web.manager.excel.ReadExcel;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaMessageBindMapper;
import com.facishare.open.oa.base.dbproxy.transfer.handler.OaRateLimitPageTransferHandler;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.transfer.dao.DataTransferDao;
import com.facishare.transfer.dao.DataTransferEntity;
import com.facishare.transfer.dao.DataTransferEntityProperty;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.github.mongo.support.DatastoreExt;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>钉钉云工具类接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2013-07-03 11:52
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/tools")
public class ToolsController {
    @Resource
    private ToolsService toolsService;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;
    @Autowired
    private List<DingTalkSelfBuildHandler> transferHandlerList;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaMessageBindMapper outerOaMessageBindMapper;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private DingtalkManager dingtalkManager;
    @Autowired
    private DingTodoManager dingTodoManager;
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;
    @Autowired
    private TokenManager tokenManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;

    @PostMapping(value = "/employee/bindEmpByExcel")
    public Result<Void> bindEmpByExcel(@RequestParam(value = "sheetName", required = false) String sheetName, @RequestParam(value = "appId", required = false) String appId, MultipartFile file) {
        byte[] bytes = new byte[0];
        try {
            bytes = file.getBytes();
            InputStream inputStream = new ByteArrayInputStream(bytes);
            ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
            };
            arg.setExcelListener(listen);
            arg.setInputStream(inputStream);
            if (StringUtils.isNotEmpty(sheetName)) {
                arg.setSheetName(sheetName);
            }
            fileManager.readExcelBySheetName(arg);
            if (CollectionUtils.isEmpty(listen.getDataList())) {
                log.info("ToolsController.bindEmpByExcel,listen is emp.");
                return Result.newSuccess();
            }
            List<DingMappingEmployeeResult> employeeResults = listen.getDataList().stream().map(v -> {
                DingMappingEmployeeResult result = new DingMappingEmployeeResult();
                result.setEi(Integer.valueOf(v.get(0)));
                result.setEmployeePhone(v.get(1));
                result.setEmployeeName(v.get(2));
                result.setEmployeeId(Integer.valueOf(v.get(3)));
                result.setDingEmployeeId(v.get(4));
                result.setEmployeeStatus(1);
                result.setBindStatus(2);
                return result;
            }).collect(Collectors.toList());
            log.info("CloudToolsController.updateDeptBind,employeeResults={}.", employeeResults);
            toolsService.bindEmpByExcel(employeeResults, appId);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.newSuccess();
    }

    @PostMapping(value = "/enterprise/enterpriseUnbind")
    public Result<Integer> enterpriseUnbind(@RequestParam(value = "fsEa") String fsEa, @RequestParam(value = "outEa") String appId) {
        return toolsService.enterpriseUnbind(fsEa, appId);
    }

    @PostMapping(value = "/enterprise/cleanEnterpriseBindLessThanTime")
    public Result<List<String>> cleanEnterpriseBindLessThanTime(@RequestParam(value = "time") Long time) throws Exception {
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(OuterOaEnterpriseBindEntity::getCreateTime, time);
        wrapper.eq(OuterOaEnterpriseBindEntity::getChannel, ChannelEnum.dingding);
        final List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(outerOaEnterpriseBindEntities)) {
            return Result.newSuccess(new ArrayList<>());
        }

        final MongoDataStoreFactoryBean factoryBean = new MongoDataStoreFactoryBean();
        factoryBean.setConfigName("fs-erpdss-oa-base-config");
        factoryBean.setSectionNames("oaBaseMongo");
        factoryBean.afterPropertiesSet();
        final DatastoreExt object = factoryBean.getObject();
        DataTransferDao dataTransferDao = new DataTransferDao();
        dataTransferDao.setDatastore(object);
        dataTransferDao.afterPropertiesSet();
        final List<String> collect = transferHandlerList.stream().map(OaRateLimitPageTransferHandler::getDataType).collect(Collectors.toList());
        final List<String> eas = outerOaEnterpriseBindEntities.stream()
                .filter(entity -> !entity.getFsEa().endsWith("__delete"))
                .map(OuterOaEnterpriseBindEntity::getFsEa)
                .distinct()
                .collect(Collectors.toList());
        log.warn("cleanEnterpriseBindLessThanTime, eas:{}", eas);
        final Query<DataTransferEntity> query = dataTransferDao.createQuery()
                .field(DataTransferEntityProperty.DATA_TYPE).in(collect)
                .field(DataTransferEntityProperty.ENTERPRISE_ACCOUNT).in(eas);
        dataTransferDao.datastore.delete(query);

        outerOaEnterpriseBindEntities.forEach(entity -> {
            entity.setFsEa(entity.getFsEa() + "__delete");
            outerOaEnterpriseBindMapper.updateById(entity);
        });
        return Result.newSuccess(eas);
    }

    @RequestMapping(value = "/fixTodo", method = RequestMethod.GET)
    @ResponseBody
    public Result<Void> fixTodo(@RequestParam(value = "ea") String ea, @RequestParam(value = "startTime") Long startTime, @RequestParam(value = "endTime") Long endTime, @RequestParam(value = "status") Boolean status) {
        final LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.gt(OuterOaMessageBindEntity::getUpdateTime, startTime);
        wrapper.lt(OuterOaMessageBindEntity::getUpdateTime, endTime);
        wrapper.eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding);
//        wrapper.eq(OuterOaMessageBindEntity::getStatus, OuterOaMessageBindStatusEnum.pending);
        wrapper.eq(OuterOaMessageBindEntity::getMessageType, OuterOaMessageBindMsgTypeEnum.todo);
        if (StringUtils.isNotBlank(ea)) {
            wrapper.eq(OuterOaMessageBindEntity::getFsEa, ea);
        }
        final List<OuterOaMessageBindEntity> outerOaMessageBindEntities = outerOaMessageBindMapper.selectList(wrapper);

        // 先按ea+appId分组
        Map<String, Map<String, List<String>>> groupedData = outerOaMessageBindEntities.stream()
                .collect(Collectors.groupingBy(OuterOaMessageBindEntity::getFsEa,
                        Collectors.groupingBy(OuterOaMessageBindEntity::getAppId,
                                Collectors.mapping(OuterOaMessageBindEntity::getSourceId, Collectors.toList()))));

        // 创建线程池只处理sourceId
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                50, // 核心线程数
                50, // 最大线程数
                60L, TimeUnit.SECONDS, // 空闲线程存活时间
                new LinkedBlockingQueue<>(2000), // 工作队列
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );

        // 处理每个ea+appId组
        groupedData.forEach((fsEa, appIdMap) -> {
            appIdMap.forEach((appId, sourceIds) -> {
                try {
                    final int ei = eieaConverter.enterpriseAccountToId(fsEa);

                    // 获取企业信息
                    DingEnterpriseResult data = dingEnterpriseManager.queryEnterpriseInfoByEi(ei, appId).getData();
                    if (Objects.isNull(data)) {
                        data = dingEnterpriseManager.queryEnterpriseByEa(fsEa + "__delete", appId).getData();
                    }

                    if (Objects.isNull(data)) {
                        log.warn("企业信息不存在, fsEa:{}, appId:{}", fsEa, appId);
                        return;
                    }

                    // 获取token
                    String accessToken = tokenManager.getToken(ei, appId);
                    if (StringUtils.isEmpty(accessToken)) {
                        log.warn("accessToken获取失败, fsEa:{}, appId:{}", fsEa, appId);
                        return;
                    }

                    Map<String, String> dingTalkToken = new HashMap<>();
                    dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);

                    // 获取员工绑定关系
                    final List<OuterOaEmployeeBindEntity> bindEntities = outerOaEmployeeBindManager.queryByFsEa(ChannelEnum.dingding, fsEa, appId);
                    if (CollectionUtils.isEmpty(bindEntities)) {
                        log.warn("企业信息不存在, fsEa:{}, appId:{}", fsEa, appId);
                        return;
                    }

                    // 直接收集有效的unionId
                    final List<String> unionIds = Collections.synchronizedList(new ArrayList<>());
                    // 使用并行流加速员工信息获取
                    bindEntities.parallelStream().forEach(employeeBindEntity -> {
                        try {
                            DingTalkEmployeeObject dingTalkEmployee = outerOaEmployeeDataManager.findDingTalkEmployeeObject(
                                    ChannelEnum.dingding, fsEa, employeeBindEntity.getOutEmpId(), appId);
                            if (dingTalkEmployee != null && StringUtils.isNotEmpty(dingTalkEmployee.getUnionId())) {
                                unionIds.add(dingTalkEmployee.getUnionId());
                            }
                        } catch (Exception e) {
                            log.warn("获取员工信息失败, fsEa:{}, appId:{}, employeeId:{}", fsEa, appId, employeeBindEntity.getOutEmpId(), e);
                        }
                    });

                    if (unionIds.isEmpty()) {
                        log.warn("没有有效的员工unionId, fsEa:{}, appId:{}", fsEa, appId);
                        return;
                    }

                    // 获取唯一的sourceId列表
                    List<String> uniqueSourceIds = sourceIds.stream().distinct().collect(Collectors.toList());

                    // 使用线程池并发处理每个sourceId
                    final DingEnterpriseResult finalData = data;
                    final Map<String, String> finalDingTalkToken = dingTalkToken;
                    for (String sourceId : uniqueSourceIds) {
                        executor.execute(() -> {
                            try {
                                // 获取具体的待办任务
                                LambdaQueryWrapper<OuterOaMessageBindEntity> queryWrapper = Wrappers.lambdaQuery();
                                queryWrapper.eq(OuterOaMessageBindEntity::getFsEa, fsEa)
                                        .eq(OuterOaMessageBindEntity::getSourceId, sourceId)
                                        .eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding)
                                        .eq(StringUtils.isNotEmpty(appId), OuterOaMessageBindEntity::getAppId, appId)
                                        .orderBy(true, true, OuterOaMessageBindEntity::getCreateTime);
                                List<OuterOaMessageBindEntity> entities = outerOaMessageBindMapper.selectList(queryWrapper);
                                if (CollectionUtils.isEmpty(entities)) {
                                    return;
                                }

                                // 查询审批状态
                                List<ObjectData> objectData = queryApprovalTaskData(ei, sourceId);
                                if (CollectionUtils.isEmpty(objectData)) {
                                    return;
                                }

                                ObjectData itemData = objectData.get(0);
                                String state = String.valueOf(itemData.get("state"));
                                if (state.equals("in_progress")) {
                                    return;
                                }

                                final String taskId = entities.get(0).getTaskId();

                                // 更新每个员工的待办状态 - 直接遍历unionIds
                                for (String unionId : unionIds) {
                                    try {
                                        dingtalkManager.updateTodoStatus(finalData, unionId, taskId, finalDingTalkToken, appId);
                                        log.info("dingtalkManager.updateTodoStatus, data:{}, unionId:{}, taskId:{} appId:{}", finalData, unionId, taskId, appId);
                                    } catch (Exception e) {
                                        log.warn("dingtalkManager.updateTodoStatus error, fsEa:{}, appId:{}, unionId:{}", fsEa, appId, unionId, e);
                                    }
                                }

                                log.info("dingtalkManager.updateTodoStatus success, fsEa:{}, appId:{}, sourceId:{} taskId:{}", fsEa, appId, sourceId, taskId);

                                if (BooleanUtils.isTrue(status)) {
                                    // 更新消息绑定状态
                                    final LambdaQueryWrapper<OuterOaMessageBindEntity> where = new LambdaQueryWrapper<>();
                                    where.eq(OuterOaMessageBindEntity::getStatus, OuterOaMessageBindStatusEnum.pending);
                                    where.eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding);
                                    where.eq(OuterOaMessageBindEntity::getFsEa, fsEa);
                                    where.eq(OuterOaMessageBindEntity::getAppId, appId);
                                    where.eq(OuterOaMessageBindEntity::getMessageType, OuterOaMessageBindMsgTypeEnum.todo);
                                    where.eq(OuterOaMessageBindEntity::getSourceId, sourceId);
                                    final List<OuterOaMessageBindEntity> outerOaMessageBindEntities1 = outerOaMessageBindMapper.selectList(where);
                                    outerOaMessageBindEntities1.forEach(e -> {
                                        e.setStatus(OuterOaMessageBindStatusEnum.approved);
                                        outerOaMessageBindMapper.updateById(e);
                                    });
                                }
                            } catch (Exception e) {
                                log.warn("dingtalkManager.updateTodoStatus error, fsEa:{}, appId:{}, sourceId:{}", fsEa, appId, sourceId, e);
                            }
                        });
                    }
                } catch (Exception e) {
                    log.warn("dingtalkManager.updateTodoStatus error, fsEa:{}, appId:{}", fsEa, appId, e);
                }
            });
        });

        // 关闭线程池并等待所有任务完成
        executor.shutdown();
        try {
            // 等待所有任务完成
            while (!executor.isTerminated()) {
                Thread.sleep(1000);
            }
        } catch (InterruptedException e) {
            log.error("dingtalkManager.updateTodoStatus error", e);
            Thread.currentThread().interrupt();
        }

        log.info("dingtalkManager.updateTodoStatus end, ea:{}, start:{}, end:{}", ea, startTime, endTime);
        return Result.newSuccess();
    }

    /**
     * 查询crm审批数据
     *
     * @param tenantId
     * @param dataId
     * @return
     */
    public List<ObjectData> queryApprovalTaskData(Integer tenantId, String dataId) {

        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        String erpOrgObj = "ApprovalTaskObj";
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(dataId);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        LogUtils.info("ApprovalTaskManager data={}", dataListRes);
        if (!dataListRes.isSuccess()) {
            LogUtils.warn("list erp org obj failed,tenantId:{},res:{}", tenantId, dataListRes);
            return null;
        }
        return dataListRes.getData().getDataList();
    }
}
