package com.facishare.open.ding.provider.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.enums.CrmMessageEnum;
import com.facishare.open.ding.api.enums.GenerateUrlTypeEnum;
import com.facishare.open.ding.api.result.BaseResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.common.model.bizlog.DingAPICallNumer;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.BaseExternalMessageArg;
import com.facishare.open.ding.provider.arg.TextCardMessageArg;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.open.ding.provider.entity.DingMessageArg;
import com.facishare.open.ding.provider.handler.QPSLimitHandlerFromProvider;
import com.facishare.open.ding.provider.utils.HttpRequestUtils;
import com.facishare.restful.common.StopWatch;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.*;

import static com.facishare.open.ding.provider.config.ConfigCenter.MAX_RECEIVER_LIMIT;

/**
 * <AUTHOR>
 * @Date 2020/10/14 11:21
 * @Version 1.0
 */
@Slf4j
@Component
// IgnoreI18nFile
public class MessageSendManager {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    @ReloadableProperty("mid.authorize.url")
    private String MID_URL;// = "https://www.fxiaoke.com/dingtalk/business/authorize?direct_uri=";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    /**
     * 钉钉打开普通页面协议
     */
    private static final String APP_LINK_PAGE_LINK_URL = "https://applink.dingtalk.com/page/link?target={target}&url={url}";
    //网页版
    private static final String DING_SINGLE_URL_WEB = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_login&state=STATE";

    private static final String MESSAGE_TYPE = "action_card";

    private static final String TEXT_TYPE = "markdown";
    private static final String TOKEN_INVALID_CODE = "88";

    @ReloadableProperty("sso.redirect.url.source")
    private String ssoRedirectUrlSource;

    @Autowired
    private QPSLimitHandlerFromProvider qpsLimitHandlerFromProvider;
    @Autowired
    private CrmRestManager crmRestManager;

    @Autowired
    private TokenManager tokenManager;

    //统一发送钉钉的卡片消息
    public BaseResult commonSendMenssage(BaseExternalMessageArg arg, DingEnterpriseResult enterpriseResult) {
        StopWatch watch = StopWatch.create("trace commonSendMenssage" + arg.getEi());
        log.info("commonSendMessage messageArg:{}", arg);
        final String appId = enterpriseResult.getAppKey();
        BaseResult result = new BaseResult();
        result.setCode(200);

        if (Objects.isNull(arg)) {
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        List<Integer> empUsers = arg.getReceiverIds();
        if (CollectionUtils.isEmpty(empUsers)) {
            log.warn("toUser is empty, arg={}.", arg);
            result.setMessage("stoUser is empty");
            return result;
        }

        try {
            for (int crmUserId : empUsers) {//按照Userid分开上报是为了能快速识别哪些userid用了最多的API
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(ei))
                        .bizName("").title("").senderId(0).appid("").crmUserId(crmUserId)
                        .objectApiName("").objectId("")
                        .functionName("textAndCardMsg")
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
        } catch (Exception ex) {}

        List<String> outEmpIds = new ArrayList<>();
        for (Integer fxId : empUsers) {
            DingMappingEmployeeResult mappingEmployeeResult = employeeManager.queryMappingEmployeeByEi(ei, fxId, appId);
            if (Objects.isNull(mappingEmployeeResult)) {
                log.info("emp not bind,ei={},fxId={}", ei, fxId);
                continue;
            }
            outEmpIds.add(mappingEmployeeResult.getDingEmployeeId());
        }
        if (outEmpIds.isEmpty()) {
            log.info("no user need to send message");
            result.setMessage("no user need to send message");
            return result;
        }

        // 限制最多发送人数为50人
        if (outEmpIds.size() > ConfigCenter.MAX_RECEIVER_LIMIT && ConfigCenter.MAX_RECEIVER_LIMIT > 0) {
            log.warn("发送人数超过{}人限制，将截取。ei={}, outEmpIds={}", MAX_RECEIVER_LIMIT, ei, outEmpIds);

            // 上报bizlog
            try {
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().tenantId(String.valueOf(ei))
                        .bizName("").title("发送人数超限").senderId(0).appid(appId)
                        .crmUserId(0) // 不针对特定用户
                        .objectApiName("").objectId("")
                        .functionName("textAndCardMsg")
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            } catch (Exception ex) {
                log.warn("上报超过发送人数限制的bizlog失败", ex);
            }

            outEmpIds = outEmpIds.subList(0, MAX_RECEIVER_LIMIT);
        }

        final String receives = String.join(",", outEmpIds);

        watch.lap("queryEnter");
        Gson gson = new Gson();
        String clientUrl = DingRequestUtil.appendUrl(enterpriseResult.getClientIp());
        Map<String, Object> messageArg = new HashMap<>();
        String accessToken = tokenManager.getToken(ei, appId);
        if(StringUtils.isEmpty(accessToken)) {
            log.info("token is null");
            result.setMessage("token is null");
            return result;
        }
        String proxyMessageUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(accessToken);
        messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
        messageArg.put("type", "POST");
        messageArg.put("token", enterpriseResult.getToken());
        if (arg instanceof TextCardMessageArg) {
            //发送卡片消息
            messageArg.put("data", convertCardMessage((TextCardMessageArg) arg, enterpriseResult, receives));
        } else {
            //发送文本消息
            messageArg.put("data", convertTextMessage(arg, enterpriseResult, receives));
        }
        Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
        if(isQPSLimit) {
            //限流且重试多次失败
            log.info("向钉钉发送待办消息失败，限流且重试多次失败.messageArg={}", messageArg);
            return result;
        }
        Object messageResult = DingRequestUtil.proxyRequest(ei, clientUrl, gson.toJson(messageArg));
        watch.lap("sendMessage");
        log.info("MessageSend messageArg:{},messageResult:{}", messageArg, messageResult);
        if (Objects.isNull(messageResult)) {
            log.warn("向钉钉发送待办消息失败，messageArg={}", messageArg);
            return result;
        }
        JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());
        if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
            log.warn("向钉钉发送待办消息失败，messageArg={}.", messageArg);
            return result;
        }

        if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
            if (jsonObject.get("errcode").equals(TOKEN_INVALID_CODE)) {
                Boolean isQPSLimit1 = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
                if(isQPSLimit1) {
                    //限流且重试多次失败
                    log.info("向钉钉发送待办消息重试失败，限流且重试多次失败.messageArg1={}", messageArg);
                    return result;
                }
                Object refreshResult = refreshRequest(messageArg, ei, clientUrl, appId);
                log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, refreshResult);
            }
            log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, messageResult);

            log.info("向钉钉发送待办消息失败，messageArg={},messageResult={}.", messageArg, messageResult);
        }
        watch.log();
        result.setMessage("发送成功");
        return result;
    }

    private Object refreshRequest(Map<String, Object> messageArg, Integer ei, String clientUrl, String appId) {
        //重新获取token，发送消息
        Gson gson = new Gson();
        tokenManager.invalidate(ei, appId);
        String refreshToken = tokenManager.getToken(ei, appId);
        String refreshUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(refreshToken);
        messageArg.put("url", refreshUrl);//钉钉发送消息的url
        Object refreshResult = DingRequestUtil.proxyRequest(ei, clientUrl, gson.toJson(messageArg));
        return refreshResult;
    }


    private DingMessageArg convertTextMessage(BaseExternalMessageArg baseArg, DingEnterpriseResult result, String receives) {
        //发送文本消息
        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(result.getAgentId());
        dingMessageArg.setUserid_list(receives);
        dingMessageArg.getMsg().setMsgtype(TEXT_TYPE);
//       CrmMessageEnum.getAppId(baseArg.getReceiverChannelType()))
        JSONObject jsonObject = JSONObject.parseObject(baseArg.getReceiverChannelData());
        Object appId = jsonObject.get("appId");
        CrmMessageEnum messageEnum = CrmMessageEnum.getAppId(ObjectUtils.isNotEmpty(appId) ? appId.toString() : null);
//        String time = Long.toString(Calendar.getInstance().getTimeInMillis() / 1000);
//       dingMessageArg.getMsg().setMarkdown(new DingMessageArg.MarkDown(messageEnum.getMessageType(),baseArg.getMessageContent()));
        StringBuilder title = new StringBuilder();
        title.append(messageEnum.getMessageType()).append("：").append(baseArg.getMessageContent());
        if(title.toString().length() > 30) {
            title.replace(30, title.toString().length(), "...");
        }
        String time = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        title.append(time);
        dingMessageArg.getMsg().setMarkdown(new DingMessageArg.MarkDown(title.toString(), "### " + baseArg.getMessageContent()));
        return dingMessageArg;
    }

    private DingMessageArg convertCardMessage(TextCardMessageArg arg, DingEnterpriseResult result, String receives) {
        //发送文本消息
        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(result.getAgentId());
        dingMessageArg.setUserid_list(receives);
        dingMessageArg.getMsg().setMsgtype(MESSAGE_TYPE);

        StringBuilder title = new StringBuilder();
        title.append(StringUtils.isNotEmpty(arg.getTitle()) ? arg.getTitle() : "crm提醒");
        //优化卡片消息
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(arg.getForm())) {
            for (int i = 0; i < arg.getForm().size(); i++) {
                if (i == 0) {
                    markdown.append("#### ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                    title.append("：").append(arg.getForm().get(i).getValue());
                } else {
                    markdown.append("#### ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                }

            }
        } else {
            markdown.append("### ").append(arg.getTitle()).append("\n");
            markdown.append("#### ").append(arg.getMessageContent()).append("\n");
            if(StringUtils.isNotEmpty(arg.getMessageContent())) {
                title.append("：").append(arg.getMessageContent());
            }
        }
        dingMessageArg.getMsg().getAction_card().setMarkdown(!StringUtils.isEmpty(markdown.toString()) ? markdown.toString() : arg.getMessageContent());
//        String time = Long.toString(Calendar.getInstance().getTimeInMillis() / 1000);
//        //title加上时间戳，避免相同信息钉钉限制发送
//        dingMessageArg.getMsg().getAction_card().setTitle(arg.getTitle() + time);
        //判断标题的长度
        if(title.toString().length() > 30) {
            title.replace(30, title.toString().length(), "...");
        }
        String time = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        title.append(" ").append(time);
        dingMessageArg.getMsg().getAction_card().setTitle(title.toString());
        dingMessageArg.getMsg().getAction_card().setSingle_title("查看详情");
        StringBuilder stringBuilder = new StringBuilder();
        String directUri;
        boolean biMessage = false;
        //对于文件和bi，可以在这里拼接好要跳转的链接
        if(arg.getGenerateUrlType() ==  GenerateUrlTypeEnum.BI_MESSAGE_URL.getType()) {
            log.info("convertCardMessage,BI_MESSAGE_URL=true");
            String commonWebviewUrl = ConfigCenter.AVA_FS_COMMON_WEBVIEW_URL.replace("{url}", URLEncoder.encode(URLEncoder.encode(ConfigCenter.DING_FUNCTION_URL + arg.getUrl())));
            String finalUrl = commonWebviewUrl;
            //短链形式
            Result<String> shortUrlResult = crmRestManager.createShortUrl(arg.getEi(), commonWebviewUrl);
            if(!shortUrlResult.isSuccess()) {
                log.info("DingTalkSendTextCardMsgHandlerTemplate.buildMsg,createShortUrl,create to do short url error, arg :{}", shortUrlResult);
            } else {
                finalUrl = shortUrlResult.getData();
            }
            log.info("convertCardMessage,createShortUrl,finalUrl={}", finalUrl);
            byte[] commonWebviewUrlBytes = finalUrl.getBytes();
            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
            directUri = MID_URL + "?ei=" + arg.getEi()+ "&url=" + param + "&taskId=" + arg.getGenerateUrlType();
            biMessage = true;
        } else if(arg.getGenerateUrlType() ==  GenerateUrlTypeEnum.FILE_MESSAGE_URL.getType()) {
            log.info("convertCardMessage,FILE_MESSAGE_URL=true");
            Map<String, String> fileInfo = new HashMap<>();
            fileInfo.put("path", arg.getExtraDataMap().get("filePath"));
            fileInfo.put("filename", URLEncoder.encode(URLEncoder.encode(arg.getExtraDataMap().get("fileName"))));

            // 使用 ObjectMapper 将对象转换为 JSON
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString;
            try {
                jsonString = URLEncoder.encode(objectMapper.writeValueAsString(new Object[]{fileInfo}));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            String commonWebviewUrl = ConfigCenter.DING_FUNCTION_URL + ConfigCenter.FILE_VIEW_URL.replace("{nfDatas}", jsonString);
            String finalUrl = commonWebviewUrl;
            //短链形式
            Result<String> shortUrlResult = crmRestManager.createShortUrl(arg.getEi(), commonWebviewUrl);
            if(!shortUrlResult.isSuccess()) {
                log.info("DingTalkSendTextCardMsgHandlerTemplate.buildMsg,createShortUrl,create to do short url error, arg :{}", shortUrlResult);
            } else {
                finalUrl = shortUrlResult.getData();
            }
            log.info("convertCardMessage,createShortUrl,finalUrl={}", finalUrl);
            byte[] commonWebviewUrlBytes = finalUrl.getBytes();
            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
            directUri = MID_URL + "?ei=" + arg.getEi()+ "&url=" + param + "&taskId=" + arg.getGenerateUrlType();
            biMessage = true;
        } else if(arg.getGenerateUrlType() ==  GenerateUrlTypeEnum.COMMENT_REDIRECT_URL.getType()) {
            log.info("convertCardMessage,COMMENT_REDIRECT_URL=true");
            String commonWebviewUrl = URLEncoder.encode(arg.getUrl());
            byte[] commonWebviewUrlBytes = commonWebviewUrl.getBytes();
            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
            directUri = MID_URL + "?ei=" + arg.getEi()+ "&url=" + param + "&taskId=" + arg.getGenerateUrlType();
        } else if(arg.getGenerateUrlType() ==  GenerateUrlTypeEnum.ATME_URL.getType()) {
            //at我的事件
            String commonWebviewUrl = ConfigCenter.DING_ATME_URL.replace("{feedId}", arg.getExtraDataMap().get("feedId"));
            byte[] commonWebviewUrlBytes = commonWebviewUrl.getBytes();
            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
            directUri = MID_URL + "?ei=" + arg.getEi()+ "&url=" + param + "&taskId=" + arg.getGenerateUrlType();
        } else {
            log.info("convertCardMessage,otherGenerateUrlType=true");
            String objectApiName = arg.getExtraDataMap().get("objectApiName");
            String objectId = arg.getExtraDataMap().get("objectId");
            String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
            directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                    + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType();
        }
        String directAppId = result.getRedirectAppId();
        stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));

        String finalUrl = stringBuilder.toString();
        if(biMessage) {
            String appLinkPageLinkUrl = APP_LINK_PAGE_LINK_URL
                    .replace("{target}","workbench")
                    .replace("{url}",URLEncoder.encode(stringBuilder.toString()));
            finalUrl = appLinkPageLinkUrl;
        }
        log.info("finalUrl = {}", finalUrl);
        dingMessageArg.getMsg().getAction_card().setSingle_url(finalUrl);//需要跳转到纷享的url
        return dingMessageArg;

    }


}
