package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.common.result.Result;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;

public interface ExternalOaTodoService {
    /**
     * 创建待办
     * @param createTodoArg
     * @return
     */
    CreateTodoResult createTodo(CreateTodoArg createTodoArg, DingEnterpriseResult mappingEnterprise);

    /**
     * 处理待办
     * @param dealTodoArg
     * @return
     */
    DealTodoResult dealTodo(DealTodoArg dealTodoArg,DingEnterpriseResult mappingEnterprise);

    /**
     * 删除待办
     * @param deleteTodoArg
     * @return
     */
    DeleteTodoResult deleteTodo(DeleteTodoArg deleteTodoArg,DingEnterpriseResult mappingEnterprise);
}
