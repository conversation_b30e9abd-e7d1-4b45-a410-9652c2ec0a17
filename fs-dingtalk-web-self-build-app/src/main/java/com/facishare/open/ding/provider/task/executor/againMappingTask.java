package com.facishare.open.ding.provider.task.executor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.model.UserDetailVo;
import com.facishare.open.ding.common.model.UserVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.constants.Constant;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.organization.adapter.api.model.biz.employee.ModifyEmployee;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class againMappingTask implements Runnable {

    private EmployeeService employeeAdapterService;
    private static final Integer CURRENT_EMPLOYEE_ID = -9;
    private Integer ei;
    private Map<Long, DeptVo> deptMaps;
    private Integer userId;
    private String dingUserId;
    private String accessToken;
    private String clientIp;
    private String phone;
    private Long deptId;
    private DingEnterpriseResult enterprise;

    public againMappingTask(EmployeeService employeeAdapterService,Map<Long, DeptVo> deptMaps, Integer ei,String dingUserId, Integer userId, String phone,Long deptId, String clientIp, String accessToken, DingEnterpriseResult enterprise) {
        this.employeeAdapterService=employeeAdapterService;
        this.ei = ei;
        this.deptMaps = deptMaps;
        this.dingUserId = dingUserId;
        this.userId=userId;
        this.clientIp=clientIp;
        this.accessToken=accessToken;
        this.phone=phone;
        this.deptId=deptId;
        this.enterprise=enterprise;


    }

    @Override
    public void run() {
        ModifyEmployee.Argument argument = new ModifyEmployee.Argument();
        UserVo userDetail = null;
//        DingMappingEmployeeResult mappingResult = employeeDtoMap.get(emps.get(index).getDingEmployeeId());
        argument.setEnterpriseId(ei);
        argument.setCurrentEmployeeId(CURRENT_EMPLOYEE_ID);
        argument.setEmployeeId(userId);
        argument.setMobile(phone);
        argument.setAccount(phone);
        //建议这里查询的时候查询部门人员的信息
        userDetail = DingRequestUtil.getUserDetail(ei, accessToken, dingUserId, clientIp, Long.valueOf(enterprise.getAgentId()));
        if (ObjectUtils.isEmpty(userDetail)) {
            log.info("查询用户信息失败 ei:{}.emp：{}", ei, dingUserId);
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            userDetail = DingRequestUtil.getUserDetail(ei, accessToken, dingUserId, clientIp, Long.valueOf(enterprise.getAgentId()));
            if (ObjectUtils.isEmpty(userDetail)) {
                log.info("重试查询用户信息失败 ei:{}.emp：{}", ei, dingUserId);
                return;
            }
            log.info("重试查询用户信息成功 ei:{}.emp：{}", ei, dingUserId);
        }
        List<Integer> crmDepts = new ArrayList<>();
        userDetail.getDepartment().stream().forEach(item -> {
            List<Integer> depts = treeDeptIds((HashMap<Long, DeptVo>) deptMaps, item, new ArrayList<Integer>());
            crmDepts.addAll(depts);
        });
        DeptVo deptVo = deptMaps.get(deptId);
        Integer crmParentID = ObjectUtils.isNotEmpty(deptVo) ? deptVo.getCrmDeptId() : Constant.TREE_PARENT_ID;
        argument.setMainDepartmentId(crmParentID);
        //附属部门
        argument.setViceDepartmentIds(crmDepts);
        log.info("againMappingEmployee starting ....:{}", argument);
        ModifyEmployee.Result modifyResult = null;
        try {
            modifyResult = employeeAdapterService.modifyEmployeeV2(argument);
            log.info("modify argument ei:{},arg:{},result:{}", ei, argument, modifyResult);
        } catch (Exception e) {
            log.error("modify argument ei:{},arg:{},result:{}", ei, argument, modifyResult);
            e.printStackTrace();
        }
    }
    //递归获取附属部门
    public List<Integer> treeDeptIds(HashMap<Long, DeptVo> deptMaps, Long dingDeptId, List<Integer> ids) {
//        List<Long> ids = new ArrayList<>();
        if ((null!=dingDeptId) && (deptMaps.get(dingDeptId) != null)) {
            Integer crmDeptId = deptMaps.get(dingDeptId).getCrmDeptId();
            Long dingDeptID = deptMaps.get(dingDeptId).getDingParentId();
            ids.add(crmDeptId.intValue());
            treeDeptIds(deptMaps, dingDeptID, ids);
        }
        return ids;
    }

}
