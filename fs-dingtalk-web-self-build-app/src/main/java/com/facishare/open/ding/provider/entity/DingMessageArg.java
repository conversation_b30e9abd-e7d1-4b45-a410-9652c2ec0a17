package com.facishare.open.ding.provider.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-10-16 16:27
 */
@Data
public class DingMessageArg {
    private String agent_id;
    private String userid_list;
    private Msg msg = new Msg();

    @Data
    public static class Msg{
        private String msgtype;
        private MarkDown markdown;
        private ActionCard action_card = new ActionCard();
        private OA oa;
    }

    @Data
    public static class ActionCard{
        private String title;
        private String markdown;
        private String single_title;
        private String single_url;
    }

    @Data
    @AllArgsConstructor
    public static class MarkDown{
        private String title;
        private String text;
    }

    @Data
    public static class OA{
        private String message_url;//消息点击链接地址
        private String pc_message_url;
        private StatusBarArg status_bar;
        private Head head;
        private Body body;
    }

    @Data
    public static class Head{
        private String bgcolor;
        private String text;//消息的头部标题。

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatusBarArg{
        private String status_value;
        private String status_bg;//状态栏背景色，默认为黑色，推荐0xFF加六位颜色值。
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Body{
        private String title;//消息体的标题
        private List<Form> form;//消息体的表单，最多显示6个，超过会被隐藏。
        private String content;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Form{
        private String key;
        private String value;
    }

}
