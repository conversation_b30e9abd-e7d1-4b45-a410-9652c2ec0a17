package com.facishare.open.ding.provider.utils;

/**
 * @<NAME_EMAIL>
 * @ClassName: StatisUtil
 * @Description: StatisUtil
 * @datetime 2019/3/25 15:29
 * @Version 1.0
 */
// IgnoreI18nFile
public class StatisUtil {

    ThreadLocal<Integer> failNumberLocal = new ThreadLocal<>();
    ThreadLocal<Integer> succNumberLocal = new ThreadLocal<>();

    public void initNumber() {
        failNumberLocal.set(0);
        succNumberLocal.set(0);
    }

    public void setFailNumber(Integer failNumber) {
        failNumberLocal.set(failNumber);
    }

    public void setSuccNumber(Integer succNumber) {
        succNumberLocal.set(succNumber);
    }

    public Integer getFailNumber() {
        return failNumberLocal.get();
    }

    public Integer getSuccNumber() {
        return succNumberLocal.get();
    }

    public void failNumberAdd() {
        failNumberLocal.set(failNumberLocal.get() + 1);
    }

    public void succNumberAdd() {
        succNumberLocal.set(succNumberLocal.get() + 1);
    }

    public Integer getTotal() {
        return failNumberLocal.get() + succNumberLocal.get();
    }
}
