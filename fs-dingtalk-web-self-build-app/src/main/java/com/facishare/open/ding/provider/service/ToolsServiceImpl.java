package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ToolsService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service(value = "toolsServiceImpl")
@Slf4j
public class ToolsServiceImpl implements ToolsService {
    @Resource
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Resource
    private DingEnterpriseManager dingEnterpriseManager;

    @Override
    public Result<Void> bindEmpByExcel(List<DingMappingEmployeeResult> employeeResults, String appId) {
        //查询已同步到员工绑定表的信息
        for(DingMappingEmployeeResult employeeResult : employeeResults) {
            List<DingMappingEmployeeResult> dingEmpIdList = dingMappingEmployeeManager.findDingEmpIdList(employeeResult.getEi(), employeeResult.getDingEmployeeId(), appId);
            if(CollectionUtils.isEmpty(dingEmpIdList)) {
                continue;
            }
            //更新绑定关系
            Integer count = dingMappingEmployeeManager.updateMappingEmployee(employeeResult, appId);
            log.info("ToolsServiceImpl.bindEmpByExcel,employeeResult={},count={}.", employeeResult, count);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> enterpriseUnbind(String fsEa, String appId) {
        return dingEnterpriseManager.deleteEnterpriseMappIng(fsEa, appId);
    }
}
