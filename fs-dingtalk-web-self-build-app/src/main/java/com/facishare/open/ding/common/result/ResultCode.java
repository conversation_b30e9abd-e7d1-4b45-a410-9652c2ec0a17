package com.facishare.open.ding.common.result;

/**
 * Created by system on 2018/3/29.
 */
public enum ResultCode {

	/** 成功 **/
	SUCCESS(0, "success", "成功"),
	LEADER_NOT_EXISTS_CODE(29, "leader not exists", "找不到指定的汇报对象，可能已离职。"),
	DEPT_NOT_EXISTS_CODE(27, "dept not exists", "没有对应的部门"),

	/** 系统繁忙 */
	SERVER_BUSY(-1, "server is busy", "系统繁忙"),

	/** 系统错误 */
	SYSTEM_ERROR(-2, "system error", "系统错误"),

	NOT_APP_MANAGER(-3, "not app manager", "用户不是应用管理员"),

	NOT_CLOUD_MANAGER(-4, "not cloud manager", "Cloud用户名或密码不正确"),

	ENTERPRISE_NOT_BIND(-5, "the enterprise not bind", "企业未绑定或不存在"),

	CLIENT_POST_FAILED(-8,"client post error","中转客户端post请求钉钉openapi失败"),


	PARAMS_ERROR(-101, "params error", "参数错误"),

	OUT_OF_LIMIT(-102, "query count is out of limit, maximum is 1000", "超出批量查询最大数量1000"),

	CONFIGURATION_NOT_EXIST(-201, "configuration not exist", "对接配置不存在"),

	CONFIGURATION_ALREADY_EXIST(-202, "configuration already exist", "对接配置已存在"),

	SYNC_TASK_ALREADY_RUNNING(-203, "sync task is already running", "同步任务正在运行，请等待结果通知"),

	NOT_SUPPORT_MANUAL_SYNC(-204, "not support manual sync", "不支持手动同步"),

	ALREADY_BIND_ACCOUNT(-206, "already bind account", "已存在绑定账号"),

	NOT_BIND_EMP(-207, "emp not bind", "员工未绑定"),

	CLOUD_HAS_CONNECTED(-300,"cloud has connected","已经有企业与该cloud已经建立连接"),

	DING_SERVER_EXCEPTION(-301, "dingding server exception", "钉钉接口异常"),

	DING_REGIST_FAILED(-304, "regist call back failed", "注册回调业务接口失败,请检查参数"),

	DING_CONNECT_PARAM_ERROR(-305,"dingding params error","钉钉appKey或appSecret或clientIp错误"),

	SAVE_ENTERPRISE_ERROR(-306,"save enterprise failed","企业保存成功"),

	DEPT_LIST_ERROR(-308,"query dingding dept list failed","查询钉钉部门列表失败"),

	INIT_DING_EMP(-310, "dingding employee is initing", "正在初始化钉钉用户，请稍后"),

	GET_DING_EMP_FAILED(-312, "get dingding user failed", "查询钉钉员工失败"),

	TO_USER_EMPTY(-314,"message receiver is empty","消息接收人员为空"),

	DING_CORPID_ERROR(-316,"dingding params error","钉钉corpId或clientIp错误"),

	TOKEN_NOT_EXIST(-401, "token not exist", "token不存在"),

	EMP_HAS_BIND(-601,"employee has been binded","该纷享职员已被绑定"),

	FIELD_INIT_ERROR(-602,"bind field failed","字段初始化失败"),

	GET_FXEMP_FAILED(-603,"get fx employee failed","获取纷享职员信息失败"),

	CREATE_FXEMP_FAILED(-604,"create fx employee failed","创建纷享职员失败"),

	MODIFY_FXEMP_FAILED(-605,"modify fx employee failed","修改纷享职员失败"),

	STOP_FXEMP_FAILED(-606,"stop fx employee failed","停用纷享职员失败"),
	QUERY_DEPT_DETAIL(-607,"query ding dept failed","查询钉钉部门失败"),


	GET_ORG_BY_OWNER_FAILED(-701,"get org by owner failed","根据负责人查询其一级部门失败"),
	DING_CALL_BACK_URL_EXIST(-705, "regist call back failed", "企业回调地址已经存在,不影响初始化组织架构，但请按照对接文档配置应用回调"),
	AVAILABLE_EMPLOYEE_NOT_ENOUGH(-705, "regist call back failed", "企业员工已超额"),
	DEPT_NAME_IS_EXIST(*********, "circle name is exist", "部门名称已存在"),
	EMPLOYEE_NAME_IS_EXIST(*********, "employee name is exist", "人员名称已存在"),

	EMPLOYEE_MOBILE_EXIST(46, "employee mobile is exist", "人员手机号已存在"),
	EMPLOYEE_ACCOUNT_EXIST(********, "employee account is exist", "人员账号已存在"),
	EMPLOYEE_PHONE_BIND_EXIST(********, "employee phone account is exist", "人员账号已存在"),
	EMPLOYEE_IS_EXIST(********, "employee mobile is exist", "CRM人员与钉钉人员信息不一致"),
	ALL_PULL_ORGANIZATION_ING(********, "all pull Organization is initing", "正在全量同步数据，请稍后"),
	EMPLOYEE_IS_STOP(********, "EMPLOYEE HAVEN STOPED", "员工已停用"),
	ENTERPRISE_COUNT_FULL(44, "ENTPRISE_EMP_COUNT_FULL", "员工配额已满"),
	FS_CLOUD_NOT_DATA(45, "FS_CLOUD_NOT_DATA", "纷享没有该数据"),
	FS_CLOUD_CREATE_API_SIGNATURE(46, "FS_CLOUD_CREATE_API_SIGNATURE", "纷享生成JS失败"),

	SERVER_ERROR(47, "SERVER_ERROR", "服务错误"),

	ACCESS_TOKEN_INVALID(50, "SERVER_ERROR", "ACCESS TOKEN过期"),

	TODO_NOT_SUPPORT(60, "fs todo not supported", "此待办不支持跳转，请到纷享内处理"),

	FUNCTION_FILTER_MESSAGE_ERROR(********, "todo function had errormessage", "函数执行报错"),

	TODO_MESSAGE_FILTER_NOT_SEND(********, "todo message filter not send", "消息过滤"),
	;

	/** 错误码 */
	private int errorCode;

	/** 错误信息 */
	private String errorMessage;

	/** 错误描叙 */
	private String description;

	ResultCode(int errorCode, String errorMessage, String description) {
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
		this.description = description;
	}

	public Integer getErrorCode() {
		return errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public String getDescription() {
		return description;
	}

	public static ResultCode getEnumsByErrorCode(int errorCode) {
		ResultCode[] resultCodesEnums = ResultCode.values();
		for (int i = 0 ; i < resultCodesEnums.length; i++) {
			if (resultCodesEnums[i].errorCode == errorCode) {
				return resultCodesEnums[i];
			}
		}
		return null;
	}
}
