package com.facishare.open.ding.provider.task.executor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class UpdateEmployeeTask implements Runnable {
    private Sets.SetView<String> setDiffer;
    private HashMap<String, User> employeeDataBaseMap;
    private HashMap<String, User> employeeDingMap;
    private Integer ei;
    private String appId;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;

    public UpdateEmployeeTask(Sets.SetView<String> setDiffer, HashMap<String, User> employeeDataBaseMap, HashMap<String, User> employeeDingMap,Integer ei, String appId) {
        this.setDiffer = setDiffer;
        this.employeeDataBaseMap = employeeDataBaseMap;
        this.employeeDingMap = employeeDingMap;
        this.ei=ei;
        this.appId = appId;
    }

    @Override
    public void run() {
        List<User> updateUserLists = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(setDiffer)) {
            setDiffer.parallelStream().filter(item -> !employeeDingMap.get(item).getMobile().equals(employeeDataBaseMap.get(item).getMobile())
                    || !employeeDingMap.get(item).getName().equals(employeeDataBaseMap.get(item).getName())).forEach(item -> updateUserLists.add(employeeDingMap.get(item)));

        }
        if (CollectionUtils.isNotEmpty(updateUserLists)) {
            Result<Integer> integerResult = dingMappingEmployeeManager.batchUpdate(ei, updateUserLists, appId);
            log.info("update employee ei:{},row:{},updateMap:{}", ei, integerResult.getData(), updateUserLists);
        }

    }
}
