package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.AuthService;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.vo.ConnectionVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>企业绑定</p>
 *
 * @version 1.0
 * @dateTime 2019-08-19 14:16
 * @anthor liqb
 */
@Slf4j
@RestController
@RequestMapping("/enterprise")
public class EnterpriseController extends BaseController {
    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private AuthService authService;

    @RequestMapping(value = "/queryEnterprise")
    public Result queryEnterprise(){
        UserVo userVo = getUserVo();
        if (userVo==null){
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].",userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEi(userVo.getEnterpriseId(), getAppId()
        );
//        if (result!=null && StringUtils.isNotEmpty(result.getData().getAppKey())){
//            result.getData().setAppSecret("******");
//            result.getData().setRedirectAppSecret("******");
//        }
        return result;
    }

    @RequestMapping(value = "/saveEnterprise",method = RequestMethod.POST)
    public Result saveEnterprise(@RequestBody @Validated ConnectionVo vo){
        UserVo userVo = getUserVo();
        if (userVo==null){
            log.warn("纷享用户为空，saveKcEnterprise userVo=[{}].",userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        vo.setEi(userVo.getEnterpriseId());
        vo.setEa(userVo.getEnterpriseAccount());
        vo.setEmployeeId(userVo.getEmployeeId());
        vo.setEnterpriseName(userVo.getEnterpriseName());

        Result result = enterpriseService.startConnect(vo);
        return result;
    }

    @RequestMapping(value = "/updateEnterprise",method = RequestMethod.POST)
    public Result updateKcEnterprise(@RequestBody @Validated  ConnectionVo vo){
        UserVo userVo = getUserVo();
        if (userVo==null){
            log.warn("纷享用户为空，updateKcEnterprise userVo=[{}].",userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
        vo.setEi(userVo.getEnterpriseId());
        vo.setEa(userVo.getEnterpriseAccount());
        vo.setEmployeeId(userVo.getEmployeeId());

        Result result = enterpriseService.updateConnection(vo);
        return result;
    }

    /**
     * 查询纷享该企业所有一级部门
     * @return
     */
    @RequestMapping(value = "/queryFxDepts")
    public Result queryFxDepts(){
        UserVo userVo = getUserVo();
        if (userVo==null){
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].",userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        Result result = enterpriseService.queryDeptLevelOne(userVo.getEnterpriseId());
        return result;
    }

    /**
     * 设置crm提醒的开启或者关闭
     */
    @RequestMapping(value = "/saveAlertStatus",method = RequestMethod.POST)
    public Result saveAlertStatus(@RequestBody Integer status){
        UserVo userVo = getUserVo();
        if (userVo==null){
            log.warn("纷享用户为空，updateKcEnterprise userVo=[{}].",userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
        ConnectionVo vo=new ConnectionVo();
        vo.setEmployeeId(userVo.getEmployeeId());
        vo.setEa(userVo.getEnterpriseAccount());
        vo.setEi(userVo.getEnterpriseId());
        vo.setAlertStatus(status);
        Result result = enterpriseService.saveOperation(vo);
        return result;
    }

    @RequestMapping(value = "/queryAutoBind", method = RequestMethod.GET)
    public Result<Integer> queryAutoBind() {
        UserVo userVo = getUserVo();
        Result<DingEnterpriseResult> enterprise = enterpriseService.queryEnterpriseByEa(userVo.getEnterpriseAccount(), getAppId());
        return Result.newSuccess(enterprise.getData().getAutBind());
    }

    @RequestMapping(value = "/saveAutoBind", method = RequestMethod.GET)
    public Result<Integer> saveAutoBind(@RequestParam Integer autoBind) {
        UserVo userVo = getUserVo();
        Result<Integer> enterprise = enterpriseService.saveAutoBind(userVo.getEnterpriseAccount(),userVo.getEnterpriseId(), autoBind, getAppId());
        return enterprise;
    }
//
//    @RequestMapping(value = "/updateAllDingEnterprise", method = RequestMethod.POST)
//    public Result<Void> updateAllDingEnterprise() {
//        Result<Void> result = enterpriseService.updateAllDingEnterprise();
//        return result;
//    }
}
