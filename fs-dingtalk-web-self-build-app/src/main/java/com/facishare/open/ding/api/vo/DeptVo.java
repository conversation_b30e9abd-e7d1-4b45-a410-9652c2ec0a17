package com.facishare.open.ding.api.vo;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.OutDeptData;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/7/24 19:35
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class DeptVo implements Serializable {
    private Long id;
    private Integer ei;
    private Integer crmDeptId;
    private Integer crmParentId;
    private Long dingDeptId;
    private Long dingParentId;
    private String name;
    private Integer seq;
    //钉钉的部门负责人
    private String dingDeptOwner;
    //crm的部门负责人
    private Integer crmDeptOwner;
    private Date createTime;
    private Date updateTime;

    public DeptVo (Integer ei,String name,Integer crmDeptOwner,Integer crmParentId){
        this.ei=ei;
        this.crmParentId=crmParentId;
        this.name=name;
        this.crmDeptOwner=crmDeptOwner;
    }

    public DeptVo (Integer ei,String name,Integer crmDeptOwner,Integer crmParentId,Integer crmDeptId){
        this.ei=ei;
        this.crmParentId=crmParentId;
        this.crmDeptId=crmDeptId;
        this.name=name;
        this.crmDeptOwner=crmDeptOwner;
    }
    
    /**
     * 将DeptVo转换为OuterOaDepartmentBindEntity
     *
     * @param dcId
     * @param appId         应用ID
     * @param outEa         外部企业账号
     * @param eieaConverter EI-EA转换器
     * @return 部门绑定实体
     */
    public OuterOaDepartmentBindEntity toBindEntity(String dcId, String appId, String outEa, EIEAConverter eieaConverter) {
        OuterOaDepartmentBindEntity entity = new OuterOaDepartmentBindEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setFsEa(eieaConverter.enterpriseIdToAccount(this.ei));
        entity.setOutEa(outEa);
        entity.setAppId(appId);
        entity.setDcId(dcId);
        entity.setFsDepId(String.valueOf(this.crmDeptId));
        entity.setOutDepId(String.valueOf(this.dingDeptId));
        entity.setBindStatus(BindStatusEnum.normal);
        
        if (this.createTime != null) {
            entity.setCreateTime(this.createTime.getTime());
        }
        if (this.updateTime != null) {
            entity.setUpdateTime(this.updateTime.getTime());
        }
        
        return entity;
    }
    
    /**
     * 将DeptVo转换为OuterOaDeptDataEntity
     * 
     * @param appId 应用ID
     * @param outEa 外部企业账号
     * @return 部门数据实体
     */
    public OuterOaDeptDataEntity toDataEntity(String appId, String outEa) {
        // 部门MongoDB格式数据
        OutDeptData mongoData = new OutDeptData();
        mongoData.setDeptId(this.dingDeptId);
        mongoData.setParentId(this.dingParentId);
        mongoData.setName(this.name);
        mongoData.setSeq(this.seq);
        mongoData.setOwner(this.dingDeptOwner);
        
        OuterOaDeptDataEntity entity = new OuterOaDeptDataEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setOutEa(outEa);
        entity.setAppId(appId);
        entity.setOutDeptId(String.valueOf(this.dingDeptId));
        entity.setDeptName(this.name);
        entity.setParentDeptId(this.dingParentId != null ? String.valueOf(this.dingParentId) : null);
        entity.setOutDeptInfo(JSON.parseObject(JSON.toJSONString(mongoData)));
        
        if (this.createTime != null) {
            entity.setCreateTime(this.createTime.getTime());
        }
        if (this.updateTime != null) {
            entity.setUpdateTime(this.updateTime.getTime());
        }
        
        return entity;
    }
    
    /**
     * 根据OuterOaDepartmentBindEntity和OuterOaDeptDataEntity合并生成DeptVo
     * 
     * @param bindEntity 部门绑定实体
     * @param dataEntity 部门数据实体
     * @return 部门VO对象
     */
    public static DeptVo fromEntities(OuterOaDepartmentBindEntity bindEntity, OuterOaDeptDataEntity dataEntity, Integer ei) {
        if (bindEntity == null) {
            return null;
        }
        
        DeptVo deptVo = new DeptVo();

        // 设置基本信息
        // 从企业账号提取企业ID
        deptVo.setEi(ei);
        deptVo.setCrmDeptId(Integer.parseInt(bindEntity.getFsDepId()));
        deptVo.setDingDeptId(Long.parseLong(bindEntity.getOutDepId()));

        Long createTime = bindEntity.getCreateTime();
        Long updateTime = bindEntity.getUpdateTime();
        // 设置详细信息
        if (dataEntity != null) {
            deptVo.setName(dataEntity.getDeptName());
            
            if (dataEntity.getParentDeptId() != null) {
                deptVo.setDingParentId(Long.parseLong(dataEntity.getParentDeptId()));
            }
            
            // 从MongoDB格式数据中提取其他信息
            OutDeptData mongoData = dataEntity.getOutDeptInfo().toJavaObject(OutDeptData.class);
            if (mongoData != null) {
                deptVo.setSeq(mongoData.getSeq());
                deptVo.setDingDeptOwner(mongoData.getOwner());
            }

            createTime = Math.min(createTime, dataEntity.getCreateTime());
            updateTime = Math.max(updateTime, dataEntity.getUpdateTime());
        }

        deptVo.setCreateTime(new Date(createTime));
        deptVo.setUpdateTime(new Date(updateTime));
        
        return deptVo;
    }
}
