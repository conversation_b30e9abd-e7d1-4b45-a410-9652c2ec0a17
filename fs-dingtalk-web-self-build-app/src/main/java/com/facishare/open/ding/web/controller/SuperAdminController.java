package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.api.service.DingtalkUserService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/tools/superAdmin")
public class SuperAdminController {
    @Autowired
    private DingtalkUserService dingtalkUserService;
    @Autowired
    private DingtalkManager dingtalkManager;

    /**
     * 谨慎使用
     * @param sql
     * @return
     */
    @RequestMapping(value = "/deleteSql", method = RequestMethod.POST)
    public Result<Integer> deleteSql(@RequestBody String sql) {
        Result<Integer> result = dingtalkUserService.deleteSql(sql);
        return result;
    }

    @RequestMapping(value = "/updateSql",method = RequestMethod.POST)
    public Result<Integer> updateSql(@RequestBody String sql){
        Result<Integer> result = dingtalkUserService.updateSql(sql);
        return result;
    }

    @RequestMapping(value = "/selectSql",method = RequestMethod.POST)
    public Result<List<Map<String, Object>>> selectSql(@RequestBody String sql){
        Result<List<Map<String, Object>>> result = dingtalkUserService.selectSql(sql);
        return result;
    }

//    @RequestMapping(value = "/updateTaskStatus",method = RequestMethod.POST)
//    public Result<List<Map<String, Object>>> updateTaskStatus(@RequestParam(value = "tenantId",required = false) Integer tenantId,@RequestParam(value = "sourceId",required = false) String sourceId ,@RequestParam(value = "appId",required = false) String appId){
//         dingtalkManager.updateTaskStatus(tenantId,sourceId,appId);
//        return Result.newSuccess();
//    }
}
