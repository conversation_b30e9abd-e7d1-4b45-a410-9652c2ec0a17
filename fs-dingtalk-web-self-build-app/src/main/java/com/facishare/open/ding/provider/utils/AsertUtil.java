package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.provider.entity.DingSyncApi;
import com.facishare.open.ding.provider.model.result.CrmResponseResult;
import com.facishare.open.ding.provider.model.result.SyncResult;
import com.facishare.open.ding.provider.model.result.crm.DeliveryNoteProductListData;
import com.facishare.open.ding.api.enums.OperationStatusEnum;
import com.facishare.open.ding.api.enums.OperationTypeEnum;
import com.facishare.open.ding.api.enums.SyncLogStatusEnum;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.model.result.crm.ErpObjListData;
import com.facishare.open.ding.provider.mongodb.entity.DingSyncLogDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @<NAME_EMAIL>
 * @ClassName: AsertUtil
 * @Description: AsertUtil
 * @datetime 2019/3/8 17:37
 * @Version 1.0
 */
@Slf4j
// IgnoreI18nFile
public abstract class AsertUtil {

    /**
     * 判断返回结果是否成功
     * @param obj
     */
    public static void isSuccess(Result obj) {
        if (!obj.isSuccess()) {
            SyncResult syncResult = SyncResult.builder().code(obj.getErrorCode()).message(obj.getErrorMessage()).build();
            throw new EmptyException(syncResult);
        }
    }

    /**
     * 判断关联对象是否是空
     * @param obj
     * @param msg1
     * @param msg2
     */
    public static void isNull(Object obj, String msg1, String msg2) {
        if(Objects.isNull(obj)) {
            String message = String.format("sync %s is faild, %s is null!!", msg1, msg2);
            log.warn(message);
            throw new EmptyException(SyncResult.builder().code(SyncResult.FAILURE).message(message).build());
        }
    }

    /**
     * 判断集合是否为空
     * @param collection
     * @param ei
     */
    public static void collectionNull(CrmResponseResult<ErpObjListData> collection, int ei, String salesOrder){
        if(CollectionUtils.isEmpty(collection.getData().getDataList())){
            String message = "销售订单["+salesOrder+"]下没有订单产品";
            log.warn("["+ei+"]:querySalesOrderProduct fail,salesOrderProduct is not exits, result=[{}].", collection);
            throw new EmptyException(SyncResult.builder().code(SyncResult.FAILURE).message(message).build());
        }
    }

    /**
     * 判断集合是否为空
     * @param collection
     * @param ei
     */
    public static void collectionDeliveryNull(CrmResponseResult<DeliveryNoteProductListData> collection, int ei, String salesOrder){
        if(CollectionUtils.isEmpty(collection.getData().getData())){
            String message = "销售订单["+salesOrder+"]下没有发货单产品";
            log.warn("["+ei+"]:queryDeliveryNoteProduct fail,DeliveryNoteProduct is not exits, result=[{}].", collection);
            throw new EmptyException(SyncResult.builder().code(SyncResult.FAILURE).message(message).build());
        }
    }

    /**
     * 日期转换
     * @param dateStr
     * @return
     */
    public static String getParseDateStr(String dateStr) {
        String date;
        if(StringUtils.isEmpty(dateStr)) {
            log.warn("syncDeliveryNoteData failed  date parse faild date[{}]", dateStr);
            SyncResult syncResult = SyncResult.builder().code(SyncResult.FAILURE).message("日期格式转换失败").build();
            throw new EmptyException(syncResult);
        }
        try {
            date = String.valueOf(DateUtil.parse(dateStr).getTime());
        } catch (ParseException e) {
            log.warn("syncDeliveryNoteData failed  date parse faild date[{}]", dateStr);
            SyncResult syncResult = SyncResult.builder().code(SyncResult.FAILURE).message("日期格式转换失败").build();
            throw new EmptyException(syncResult);
        }
        return date;
    }

    /**
     * 判断结果集是否为空
     * @param dataList
     */
    public static void listNotNull(List<List> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("syncStockObjData KingdeeObjectUtils queryObjList empty, no need to sync.");
            SyncResult syncResult = SyncResult.builder().code(SyncResult.NO_NEED).message("syncStockObjData KingdeeObjectUtils queryObjList empty, no need to sync.").build();
            throw new EmptyException(syncResult);
        }
    }

    /**
     * 包装kcSyncLogDO对象
     * @param ei
     * @param userId
     * @param taskId
     * @param syncType
     * @param cloudValueMap
     * @param dingSyncApi
     * @param e
     * @return
     */
    public static DingSyncLogDO wrapDingSyncLogDo(Integer ei, Integer userId,
                                                Long taskId, int syncType,
                                                Map<String, Object> cloudValueMap, DingSyncApi dingSyncApi, Exception e, OperationTypeEnum operator) {
        String fId = MapUtils.getString(cloudValueMap, "FID");
        String fBillNo = MapUtils.getString(cloudValueMap, "FBillNo");
        String fOrderNo = MapUtils.getString(cloudValueMap, "FOrderNo");
        String content = String.format("发货单:[%s], 订单号:[%s] 同步异常，数据流向为[%s]", fBillNo, fOrderNo, DynamicParamUtil.convertDirection(dingSyncApi.getSyncDirection()));
        DingSyncLogDO dingSyncLogDO = DynamicParamUtil.buildSyncLogDO(ei, dingSyncApi, fId, userId, syncType, operator.getType());
        DynamicParamUtil.supplementLogDO(dingSyncLogDO, e.getMessage(), null, null, content, OperationStatusEnum.SYNC_FAIL.getStatus(), 1, 0, 1, SyncLogStatusEnum.NO_RECOVER.getStatus());
        return dingSyncLogDO;
    }

}
