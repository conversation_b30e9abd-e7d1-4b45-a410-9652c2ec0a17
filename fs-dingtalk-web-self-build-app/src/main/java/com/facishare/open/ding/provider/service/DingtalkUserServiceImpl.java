package com.facishare.open.ding.provider.service;

import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingtalkUserService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.HttpUtils;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDepartmentBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDeptDataMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019-11-13 16:01
 */
@Slf4j
@Service("dingtalkUserService")
public class DingtalkUserServiceImpl implements DingtalkUserService {
    @Autowired
    private DingEnterpriseManager enterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    @Resource
    private OuterOaDepartmentBindMapper outerOaDepartmentBindMapper;
    
    @Resource
    private OuterOaDeptDataMapper outerOaDeptDataMapper;

    @Override
    public Result<DingMappingEmployeeResult> getDingtalkUserInfo(Map<String, String> parameters){
        //查询绑定企业
        Integer ei = Integer.parseInt(parameters.get("ei"));
        String appId = parameters.get("appId");
        // 由于需要获取所有企业的信息,这里传入null作为appId
        Result<DingEnterpriseResult> result = enterpriseManager.queryEnterpriseByEi(ei, appId);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        String redirectAppId = result.getData().getRedirectAppId();
        String redirectAppSecret = result.getData().getRedirectAppSecret();

        Gson gson =  new Gson();
        Map<String, String> argMap = new HashMap<>();
        argMap.put("redirectAppId", redirectAppId);
        argMap.put("redirectAppSecret", redirectAppSecret);
        argMap.put("token", result.getData().getToken());
        argMap.put("code", parameters.get("code"));
        String getUserUrl = result.getData().getClientIp() + "proxy/getUserByCode";
        log.info("getUserUrl={}", getUserUrl);
        CloseableHttpResponse response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
        String entity = null;
        String unionId = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("redirectAppId={},redirectAppSecret={},response={}",redirectAppId,redirectAppSecret,entity);
            OapiSnsGetuserinfoBycodeResponse.UserInfo dingUser = gson.fromJson(entity, OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
            String name = dingUser.getNick();
            String openId = dingUser.getOpenid();
            unionId = dingUser.getUnionid();
            log.info("name={},openId={},unionId={}", name,openId,unionId);
//            User user = new User();
//            user.setName(name);
//            user.setUnionid(unionId);
        } catch (Exception e) {
            log.warn("getUserInfo failed,entity={}", entity);
            return Result.newError(ResultCode.GET_DING_EMP_FAILED);
        }
        //根据员工绑定关系获取纷享员工身份
        Result<DingMappingEmployeeResult> mappingResult = employeeManager.queryEmpByUnionId(ei, unionId, appId);
        if (Objects.isNull(mappingResult) || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())){
            log.info("the emp not bind");
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        return mappingResult;
    }

    @Override
    public Result<Integer> deleteSql(String sql) {
        log.info("deleteSql: {}", sql);
        
        // 根据SQL语句确定调用哪个Mapper
        if (sql.toLowerCase().contains("outer_oa_department_bind")) {
            int count = outerOaDepartmentBindMapper.deleteSql(sql);
            return Result.newSuccess(count);
        } else {
            // 默认继续使用旧的DAO实现
            int count = outerOaDeptDataMapper.deleteSql(sql);
            return Result.newSuccess(count);
        }
    }

    @Override
    public Result<Integer> updateSql(String sql) {
        log.info("updateSql: {}", sql);
        
        // 根据SQL语句确定调用哪个Mapper
        if (sql.toLowerCase().contains("outer_oa_department_bind")) {
            int count = outerOaDepartmentBindMapper.updateSql(sql);
            return Result.newSuccess(count);
        } else {
            // 默认继续使用旧的DAO实现
            int count = outerOaDeptDataMapper.updateSql(sql);
            return Result.newSuccess(count);
        }
    }

    @Override
    public Result<List<Map<String, Object>>> selectSql(String sql) {
        log.info("selectSql: {}", sql);
        
        // 根据SQL语句确定调用哪个Mapper
        if (sql.toLowerCase().contains("outer_oa_department_bind")) {
            List<Map<String, Object>> list = outerOaDepartmentBindMapper.selectSql(sql);
            return Result.newSuccess(list);
        } else {
            // 默认继续使用旧的DAO实现
            List<Map<String, Object>> list = outerOaDeptDataMapper.selectSql(sql);
            return Result.newSuccess(list);
        }
    }
}
