package com.facishare.open.ding.web.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.ding.web.base.UserContextHolder;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.config.ConfigCenter;
import com.facishare.open.ding.web.exception.NoPermissionException;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.uc.api.exeception.UserCenterException;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 用户权限验证拦截器
 *
 * <AUTHOR>
 * @date 2023.5.16
 */
@Component
@Slf4j
// IgnoreI18nFile
public class UserInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;

    private void getUserFromFsCookie(String fsAuthXCCookie,String requestURI, String appId) {
        log.info("UserInterceptor.getUserFromFsCookie,fsAuthXCCookie={}",fsAuthXCCookie);
        CookieToAuth.Argument argument = new CookieToAuth.Argument();
        argument.setCookie(fsAuthXCCookie);
        argument.setFsToken(null);
        argument.setIp(null);
        CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
        if (!result.isSucceed() || result.getValidateStatus() != ValidateStatus.NORMAL
                || Objects.isNull(result.getBody())) {
            log.info("UserInterceptor.getUserFromFsCookie,failed,result={}", result);
            return;
        }
        AuthXC authXC = result.getBody();
        log.info("UserInterceptor.getUserFromFsCookie,authXC={}", JSON.toJSONString(authXC));

        Integer ei = authXC.getEnterpriseId();
        String ea = authXC.getEnterpriseAccount();
        Integer employeeId = authXC.getEmployeeId();

        // 需要鉴权的接口名单，这些接口是钉钉管理后台WEB调用的接口
        for (String uri : ConfigCenter.needAuthInterfaceList) {
            if (StringUtils.containsIgnoreCase(requestURI, uri)) {
                log.info("UserInterceptor.preHandle,requestURI={},need auth", requestURI);
                try {
                    Result<Boolean> roleCode = fsEmployeeServiceProxy.hasRoleCode(ei + "", employeeId + "",
                            ConfigCenter.validRoleCodeList);
                    log.info("UserInterceptor.getUserFromFsCookie,roleCode={}", roleCode);
                    if (roleCode.getData() == false) {
                        throw new NoPermissionException("用户没有权限访问当前URL");
                    }
                } catch (Exception e) {
                    log.info("UserInterceptor.getUserFromFsCookie,hasRoleCode,exception={}", e.getMessage());
                }
            }
        }
        fillCookie(authXC,requestURI, appId);
    }

    private void fillCookie(AuthXC authXC,String requestURI, String appId) {
        SimpleEnterprise simpleEnterprise = new SimpleEnterprise();
        try {
            GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
            getSimpleEnterpriseArg.setEnterpriseAccount(authXC.getEnterpriseAccount());
            getSimpleEnterpriseArg.setEnterpriseId(authXC.getEnterpriseId());
            GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService
                    .getSimpleEnterprise(getSimpleEnterpriseArg);
            simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
        } catch (UserCenterException e) {
            log.info("UserInterceptor.fillCookie,getSimpleEnterprise exception={}", e.getMessage());
        }

        UserContextHolder.get().set(new UserVo(authXC, simpleEnterprise.getEnterpriseName(), simpleEnterprise.getEnterpriseShortName(), appId));
        log.info("UserInterceptor.fillCookie,requestURI={},passed", requestURI);
    }

    private void FillUserContext(HttpServletRequest request) {
        // todo 950 获取appId
        String appId = request.getParameter("appId");
        Cookie[] cookies = request.getCookies();
        if (Objects.isNull(cookies)) {
            if (StringUtils.containsIgnoreCase(request.getRequestURI(), "/tools/superAdmin")) {
                throw new NoPermissionException("无权使用管理工具相关接口");
            }
            return;
        }
        for (Cookie cookie : cookies) {
            if ("FSAuthXC".equalsIgnoreCase(cookie.getName())) {
                getUserFromFsCookie(cookie.getValue(),request.getRequestURI(), appId);
                return;
            }
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        String traceId = TraceUtils.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            TraceUtils.initTraceId(UUID.randomUUID().toString());
        }

        String requestURI = request.getRequestURI();
        List<String> headers = Collections.list(request.getHeaderNames());
        log.info("UserInterceptor.preHandle,requestURI={},headers={}", requestURI, JSONObject.toJSONString(headers));

        // 需要验证纷享用户身份的接口
        FillUserContext(request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        // 每次请求结束，清除当前线程数据 （tomcat是使用线程池来处理业务的）
        UserContextHolder.get().remove();
    }
}
