package com.facishare.open.ding.provider.mq;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EnterpriseConfigAccountSyncTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @Version 1.0
 */
@Component
@Slf4j
public class UserRoleEventListener extends OrganizationChangedListener {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;


    public UserRoleEventListener() {
        super("fs-dingtalk-provider");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        log.info("onEmployeeChanged,event={}", event);
        //是否有绑定关系
        final String fsEa = event.getEnterpriseAccount();
        Integer enterpriseId = eieaConverter.enterpriseAccountToId(fsEa);
        Result<List<OuterOaEnterpriseBindEntity>> enterpriseBindResult = dingEnterpriseManager.queryAllEnterpriseBind(fsEa);
        final List<OuterOaEnterpriseBindEntity> entities = enterpriseBindResult.getData();
        if (!enterpriseBindResult.isSuccess() || CollectionUtils.isEmpty(entities)) {
            return;
        }

        for (OuterOaEnterpriseBindEntity entity : entities) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, entity.getId());
            SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            if (settingAccountRulesModel.getSyncTypeEnum() != EnterpriseConfigAccountSyncTypeEnum.accountBind || settingAccountRulesModel.getBindTypeEnum() != BindTypeEnum.auto) {
                // 不是自动绑定,跳过
                LogUtils.info("enterprise need auto bind:{}", entity.getFsEa());
                continue;
            }

            //需要读取匹配的字段：
            OuterOaConfigInfoEntity uniqueEmployeeData = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, entity.getId());
            SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(uniqueEmployeeData.getConfigInfo(), SystemFieldMappingResult.class);
            SystemFieldMappingResult.ItemFieldMapping itemFieldMapping = systemFieldResult.getItemFieldMappings().stream().filter(SystemFieldMappingResult.ItemFieldMapping::getMatchUnique).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(itemFieldMapping)) {
                log.info("filter itemFieldMapping is empty uniqueEmployeeData:{}", uniqueEmployeeData);
                continue;
            }

            Integer fsEmployeeId = event.getNewEmployeeDto().getEmployeeId();

            //这个员工已经绑定了，那就不继续
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(entity.getId(), String.valueOf(fsEmployeeId), null);
            if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                log.info("employee data had bind");
                continue;
            }

            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(enterpriseId.toString(), event.getNewEmployeeDto().getEmployeeId() + "");
            if (!detail.isSuccess() || ObjectUtils.isEmpty(detail.getData())) {
                log.info("crm get employee fail");
                continue;
            }

            Object crmObjectValue = detail.getData().get(itemFieldMapping.getCrmFieldApiName());
            if (ObjectUtils.isEmpty(crmObjectValue)) {
                log.info("filter crmvalue is empty");
                continue;
            }

            List<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntities = outerOaEmployeeDataManager.selectByField(entity, itemFieldMapping.getOuterOAFieldApiName(), String.valueOf(crmObjectValue));
            if (ObjectUtils.isEmpty(outerOaEmployeeDataEntities)) {
                LogUtils.info("out Employee not found apiName:{} value:{}", itemFieldMapping.getOuterOAFieldApiName(), crmObjectValue);
                continue;
            }

            //匹配插入绑定
            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = new OuterOaEmployeeBindEntity();
            outerOaEmployeeBindEntity.setChannel(entity.getChannel());
            outerOaEmployeeBindEntity.setBindStatus(BindStatusEnum.normal);
            outerOaEmployeeBindEntity.setId(IdGenerator.get());
            outerOaEmployeeBindEntity.setDcId(entity.getId());
            outerOaEmployeeBindEntity.setAppId(entity.getAppId());
            outerOaEmployeeBindEntity.setFsEmpId(String.valueOf(event.getNewEmployeeDto().getEmployeeId()));
            outerOaEmployeeBindEntity.setOutEmpId(outerOaEmployeeDataEntities.get(0).getOutUserId());
            outerOaEmployeeBindEntity.setUpdateTime(System.currentTimeMillis());
            outerOaEmployeeBindEntity.setCreateTime(System.currentTimeMillis());
            outerOaEmployeeBindEntity.setFsEa(fsEa);
            outerOaEmployeeBindEntity.setOutEa(entity.getOutEa());
            LogUtils.info("fsemp tripper auto bind:{}-{}{}", fsEa, fsEmployeeId, crmObjectValue);
            outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));

        }
    }
}
