<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:protocol id="dubbo" name="dubbo" port="${dubbo.provider.port}"/>
    <dubbo:provider  timeout="15000" />

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.AuthService"-->
<!--                   group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                   ref="authServiceImpl"-->
<!--                   protocol="dubbo"-->
<!--                   version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.EnterpriseService"-->
<!--                   group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                   ref="enterpriseService"-->
<!--                   protocol="dubbo"-->
<!--                   version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.ObjectMappingService"-->
<!--                   group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                   ref="objectMappingService"-->
<!--                   protocol="dubbo"-->
<!--                   version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.RedisDingService"-->
<!--                   group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                   ref="redisDingService"-->
<!--                   protocol="dubbo"-->
<!--                   version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.SyncLogService"-->
<!--                   group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                   ref="syncLogService"-->
<!--                   protocol="dubbo"-->
<!--                   version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.DingtalkUserService"-->
<!--                   group="#{systemProperties['dingtalkinnergroup']}"-->
<!--                   ref="dingtalkUserService"-->
<!--                   protocol="dubbo"-->
<!--                   version="${dubbo.provider.version}"/>-->

<!--    <dubbo:service interface="com.facishare.open.ding.api.service.ExternalMessageService"-->
<!--                   ref="externalMessageServiceImpl"-->
<!--                   group="dingtalk"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="15000"/>-->

<!--    <dubbo:service interface="com.fxiaoke.message.extrnal.platform.api.ExternalTodoService"-->
<!--                   ref="externalTodoServiceImpl"-->
<!--                   group="dingtalk"-->
<!--                   protocol="dubbo"-->
<!--                   timeout="15000"/>-->

<!--    <dubbo:service id="externalOaTodoService"-->
<!--                   ref="externalOaTodoServiceImpl"-->
<!--                   interface="com.facishare.open.ding.api.service.ExternalOaTodoService"-->
<!--                   group="dingtalk"-->
<!--                   protocol="dubbo" />-->
    <dubbo:service interface="com.facishare.open.ding.api.service.cloud.DingAuthService"
                   group="#{systemProperties['dingtalkinnergroup']}"
                   ref="dingAuthServiceImpl"
                   protocol="dubbo"
                   version="1.0"/>

    <dubbo:service interface="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"
                   ref="dingTalkSelfBuildManager"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   retries="1"/>

<!--    <dubbo:service-->
<!--            id="toolsService"-->
<!--            ref="toolsServiceImpl"-->
<!--            interface="com.facishare.open.ding.api.service.ToolsService"-->
<!--            protocol="dubbo"-->
<!--            version="1.0"-->
<!--            timeout="60000" />-->
</beans>