package dao;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dao.DingEnterpriseDao;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-10 16:34
 */
@Slf4j
public class DingEnterpriseDaoTest extends BaseAbstractTest {

    @Autowired
    private DingEnterpriseDao dingEnterpriseDao;
    @Autowired
    private DingtalkManager dingtalkManager;

    @Test
    public void findByEATest() {
        DingEnterprise fktest = dingEnterpriseDao.findByEA("fktest");
        Assert.assertNotNull("findByEA is not null", fktest);
    }

    @Test
    public void testOa(){
        CreateTodoArg createTodoArg= JSONObject.parseObject("{\"bizType\":\"452\",\"content\":\"客户审批(2022-04-20 01:14)\",\"ea\":\"83384\",\"ei\":589994,\"extraDataMap\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"activityInstanceId\":\"\",\"workflowInstanceId\":\"625eeddfe12cc1174244ef60\",\"taskId\":\"625eeddfc88936605b45457a\",\"objectId\":\"625eeddd76116d00015d27ca\"},\"form\":[{\"key\":\"流程主题\",\"value\":\"客户审批(2022-04-20 01:14)\"},{\"key\":\"客户名称\",\"value\":\"客户数据1111\"},{\"key\":\"1级行业\",\"value\":\"\"},{\"key\":\"客户级别\",\"value\":\"\"},{\"key\":\"成交状态\",\"value\":\"未成交\"},{\"key\":\"负责人\",\"value\":\"柯南颖001234\"}],\"generateUrlType\":1,\"groupKeys\":[\"webhookMessageDistribute\",\"feishu\"],\"receiverIds\":[1042,1046,1030,1079,1031,1083,1069],\"senderId\":-10000,\"sourceId\":\"625eeddfc88936605b45457a\",\"title\":\"待处理的CRM审批流程\",\"url\":\"todo?apiname=AccountObj&id=625eeddd76116d00015d27ca&ea=fktest018\",\"appId\":\"test_app_id\"}",
                CreateTodoArg.class);
        boolean contains = ConfigCenter.CRM_TO_BIZ_TYPES.contains(createTodoArg.getBizType());
        boolean contains1 = ConfigCenter.OA_GRAY_TENANTS.contains(createTodoArg.getEa());

        if(ConfigCenter.OA_GRAY_TENANTS.contains(createTodoArg.getEa()) &&
                ConfigCenter.CRM_TO_BIZ_TYPES.contains(createTodoArg.getBizType())){
            System.out.println("DingEnterpriseDaoTest.testOa");
        }
    }

    @Test
    public void saveTest() {
        DingEnterprise dingEnterprise = new DingEnterprise();
        dingEnterprise.setEa("fktest");
        dingEnterprise.setEi(100086);
        int insert = dingEnterpriseDao.insert(dingEnterprise);

        Assert.assertTrue(" insert fail...", insert == 1);
    }

//    @Test
//    public void updateTest() {
//        dingtalkManager.updateTaskStatus(83384,"677e30c723580e02aa3ecdec", appId);
//
//    }
    
}