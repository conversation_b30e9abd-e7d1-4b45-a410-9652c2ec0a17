package manager;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class DingEnterpriseManagerTest  extends BaseAbstractTest {
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Test
    public void test() {
        String appKey="ding2tvtlkfe9vxewk8w";
        System.out.println(dingEnterpriseManager.queryEnterPriseByAppKey(appKey));
    }

    @Test
    public void queryAllEnterpriseResult() {
        String x = "{\"errcode\":0,\"department\":[{\"createDeptGroup\":true,\"name\":\"华南\",\"id\":994519831,\"autoAddUser\":true,\"parentid\":994696824},{\"createDeptGroup\":true,\"name\":\"海外市场1\",\"id\":995183799,\"autoAddUser\":true,\"parentid\":994696824},{\"createDeptGroup\":false,\"name\":\"西南\",\"id\":995638115,\"autoAddUser\":false,\"parentid\":994696824},{\"createDeptGroup\":true,\"name\":\"西北\",\"id\":995885249,\"autoAddUser\":true,\"parentid\":994696824}],\"errmsg\":\"ok\"}";
        JSONObject jsonObject = JSONObject.parseObject(x);
        System.out.println(jsonObject);


        System.out.println(dingEnterpriseManager.queryAllEnterpriseResult("71557"));
    }
}
