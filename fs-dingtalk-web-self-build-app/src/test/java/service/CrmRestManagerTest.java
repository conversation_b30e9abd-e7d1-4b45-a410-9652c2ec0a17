package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.CreateCrmEmployeeVo;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.crm.CrmRestManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/2/24 17:41
 * @Version 1.0
 */
@Slf4j
public class CrmRestManagerTest extends BaseAbstractTest {
    @Autowired
    private CrmRestManager crmRestManager;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;

    @Test
    public void  createDept(){
        DeptVo vo=new DeptVo();
        vo.setEi(78583);
        vo.setName("手机测试12112");
        vo.setCrmParentId(1000);
        vo.setCrmDeptOwner(1000);
        Result<Integer> dept = crmRestManager.createDept(vo);

    }

    @Test
    public void queryuser(){
        DingMappingEmployeeResult byDingEmpId = dingMappingEmployeeManager.findByDingEmpId(708205, "1211", null);
        log.info("dingEmp");
    }

    @Test
    public void  modifyDept(){
        /**
         * trace ei:82313, vo:DeptVo(id=749980, ei=82313, crmDeptId=2058, crmParentId=2059, dingDeptId=375403450, dingParent
         * Id=1, name=技术部, seq=null, dingDeptOwner=011057352023653669, crmDeptOwner=null, createTime=Wed May 11 01:35:15 CST 2022, updateTime=Wed May 11 02:01:07 CST 2022),
         * crm modifyDept message:{"code":320001401,"message":"字段_id的数据值2058类型不正确"}
         */
        DeptVo vo=new DeptVo();
        vo.setEi(82313);
        vo.setName("技术部121");
        vo.setCrmDeptId(2058);
        vo.setCrmParentId(2059);

        Result<Void> voidResult = crmRestManager.modifyDept(vo);
    }

    @Test
    public void  stopDept(){
        DeptVo vo=new DeptVo();
        vo.setEi(78583);
        vo.setName("手机测试12");
        vo.setCrmParentId(1005);
        vo.setCrmDeptOwner(1000);
        vo.setCrmDeptId(1015);
        Result<Void> voidResult = crmRestManager.stopDept(vo);
    }

    @Test
    public void testCreateEmp(){
        CreateCrmEmployeeVo createCrmEmployeeVo=new CreateCrmEmployeeVo();
        createCrmEmployeeVo.setName("小陈");
        createCrmEmployeeVo.setMobile("+85-1236666");
        createCrmEmployeeVo.setCrmMainDeptId("1097");
        createCrmEmployeeVo.setEi(83384);
        createCrmEmployeeVo.setCrmEmpId(1000);
        createCrmEmployeeVo.setEmployeeNumber("jobTest001");
        createCrmEmployeeVo.setEmail("<EMAIL>");
//        createCrmEmployeeVo.setLeader(1049);
        Result<Integer> emp = crmRestManager.createEmp(createCrmEmployeeVo);

    }

    @Test
    public void testModifyEmp(){
        CreateCrmEmployeeVo createCrmEmployeeVo=new CreateCrmEmployeeVo();
        createCrmEmployeeVo.setName("黎妹妹");
        createCrmEmployeeVo.setMobile("13711911808");
        createCrmEmployeeVo.setCrmMainDeptId("1000");
        createCrmEmployeeVo.setEi(83384);
        createCrmEmployeeVo.setCrmEmpId(109);
        createCrmEmployeeVo.setEmployeeNumber("jobTest001");
        createCrmEmployeeVo.setEmail("<EMAIL>");
        Result<Integer> emp = crmRestManager.modifyEmp(createCrmEmployeeVo);

    }


    @Test
    public void testStopEmp(){

        Result<Integer> emp = crmRestManager.stopEmp(1048,76340);

    }

    @Test
    public void testQueryEmp(){

        Result<Map<String, String>> phone = crmRestManager.queryByPhone(82777, "user_id", "1002");
        log.info("phone");
    }




}
