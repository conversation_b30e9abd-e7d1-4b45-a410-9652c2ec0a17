package controller;

import base.BaseAbstractTest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptException;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ExternalMessageService;
import com.facishare.open.ding.api.vo.ConnectionVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.SendTextCardContextArg;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.template.message.DingSelfSendCardMessageTemplate;
import com.facishare.open.ding.web.constants.Constant;
import com.facishare.open.ding.web.controller.EnterpriseController;
import com.facishare.open.ding.web.dingding.DingRequestUtil;

import java.io.IOException;

import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/17 18:01
 */
@Slf4j
public class EnterpriseControllerTest extends BaseAbstractTest {
    @Autowired
    private EnterpriseController enterpriseController;

    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private SSOLoginService ssoLoginService;

    @Autowired
    private ExternalMessageService messageService;
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;
    @Autowired
    private DingSelfSendCardMessageTemplate dingSelfSendCardMessageTemplate;
    @Test
    public void testQuery() {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setEi(83384);
        arg.setReceiverIds(Lists.newArrayList(1000));
        arg.setMessageContent("测试123");
        arg.setTitle("待办测试");
        arg.setUrl("https://www.ceshi112.com/FHH/EM0HXUL/SSO/Login?token=%s");
        SendTextCardContextArg sendTextCardContextArg=new SendTextCardContextArg();
        sendTextCardContextArg.setSendTextCardMessageArg(arg);

        Result<DingEnterpriseResult> sendCardEnterpriseResult = dingEnterpriseManager.queryEnterpriseInfoByEi(arg.getEi(), null);
        sendTextCardContextArg.setDingEnterpriseResult(sendCardEnterpriseResult.getData());
        dingSelfSendCardMessageTemplate.execute(sendTextCardContextArg);
        ConnectionVo vo = new ConnectionVo();

        vo.setEa("test");
        Result result = enterpriseController.queryEnterprise();
        log.info("result = " + result);
    }

    @Test
    public void test() {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(71658, 1003));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();

        String dicUrl = "https://www.ceshi112.com/FHH/EM0HXUL/SSO/Login?token=%s";

        BASE64Encoder encoder = new BASE64Encoder();
        String base64 = encoder.encode(dicUrl.getBytes());
        System.out.println("编码后--" + base64);
        //解码
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            byte[] d = decoder.decodeBuffer(base64);
            System.out.println("解码后--" + new String(d));
        } catch (IOException e) {
            e.printStackTrace();
        }

        String redirectUrl = String.format(dicUrl, fsToken);
        String url = "dingtalk://dingtalkclient/page/link?url=" + URLEncoder.encode(redirectUrl);
        System.out.println(url);
    }

    //加解密的信息
    @Test
    public void testEncry() throws DingTalkEncryptException {
//        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
//                "ding5fe5aa1ba078facc24f2f5cc6abecb85");
//
        String plaintext = "{\"CorpId\":\"ding5fe5aa1ba078facc24f2f5cc6abecb85\",\"EventType\":\"org_dept_create\",\"DeptId\":[394773729],\"TimeStamp\":\"1597236510165\"}";
        /**
         * args=[21f765fd1ebcc0bce9b95c440ae1ff61f1256e25, 159750845
         * 2640, ilIUV9zM, 76246, {"encrypt":"VlPdwvlJADvTPWRJ0r0HVS20/usV8cn22XceVua3BXXVf7COeYe3G2DxqRxoVofdZYpmNJwzfyKDrwjiqD2IxdZaZ8IphRVtB1wAqdeF/VxIgS0/FJpjfbc6ag3arAFc3/1077aiivNAWkUc2wQedT1Y7CBQS4tELrQpGp67Xf1TvY5hxAHEHVIw51EYSQ15r1gD3x2P+3qPFQ2jnwYAw5nldf
         * y8tYXcNElFVIvIvOZXT59FKEAblA3nJ7+gabcK"}]
         */
//        Map<String, String> irEx13mD = dingTalkEncryptor.getEncryptedMap(plaintext, 1597236510165L, "irEx13mD");
//        log.info(""+irEx13mD);
//
//
//        String decryptMsg = dingTalkEncryptor.getDecryptMsg("e6ff36d44e311d4b61c24ea6a253684aeddebefb", "1597236510165", "irEx13mD", "5G4ozpINjd7It6gR0LuU4lgHVs4/nMnQLSUa//zNhHK/Y6u2tRadugz48Ji23Cd5FNT+emqu7ntGQQljEs3XH2k1aWzBI4XiSPZfR/JaRQFFofiOwobPR/kRc2OmvfpZ6s51AH9mG2AogGK/FXkDnqiAEJniGfZi26GAFVJnMw4UBKC2ZiK3KtPdw+h0DjWTGAbhDaLACzxRAqrBPGY4D2HYUY7Ikn3RJIuteI/2jjlq5h8/Gw3xfijVQzwhRV8C");
//        log.info(""+decryptMsg);
        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                "ding5fe5aa1ba078facc24f2f5cc6abecb85");
        //从post请求的body中获取回调信息的加密数据进行解密处理
        String encryptMsg = "kawV27KQQYejsj\n" +
                "FLzKcz8X1x4Y4+tX6Om2sruYbVDzU1Equ9cm8eJOCFcf4T/PnVmbIwFsPo7Z3LEc3LIZtDAa1KNJwTxS7ImY8KbZ68tfrMbhqQv34LMAQMkKaIn3yQZUG81DFmg/7C/xkuuFdjbg2koePbSFcyv1lV7sTb6Hs/vvufMqz2iU3DGleQVNWfmjfnovFu0Q0BLXk2f7BrQuDLKbwP+s/7gpLTGVc/rlNDnASnpuLuIJV+k5+6GTVE";
        String plainText = dingTalkEncryptor.getDecryptMsg("466a0f21ddf2ac7cc086b564408e2d44a8f30bfc", "1615434047958", "6QB9hla0", encryptMsg);
        log.info("plainText={}", plainText);
        //加密
        DingTalkEncryptor newDingTalk = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                "ding5fe5aa1ba078facc24f2f5cc6abecb85");
        Map<String, String> dingMap = newDingTalk.getEncryptedMap(plaintext, 1597236510165L, "irEx13mD");

        log.info("" + dingMap);

        String decryptMsg = newDingTalk.getDecryptMsg(dingMap.get("msg_signature").toString(), dingMap.get("timeStamp").toString(), dingMap.get("nonce").toString(), dingMap.get("encrypt").toString());
        log.info("" + decryptMsg);
    }
    /**
     * 绿米回调
     */
    @Test
    public void encryTest() throws DingTalkEncryptException {

//        String nonce = "irEx13mD";
//        String plainText = "{\r\n" +
//                "     'EventType': 'user_add_org',\r\n" +
//                "     'TimeStamp': '1597371357080',\r\n" +
//                "     'CorpId': 'ding1d769e1e02af8dfc35c2f4657eb6378f',\r\n" +
//                "     'UserId': ['172515221729079951']\r\n" +
//                " }";
//        String timestamp = "1597371357080";




        String nonce="irEx13mD";
        String plaintext = "{\"CorpId\":\"ding1d769e1e02af8dfc35c2f4657eb6378f\",\"EventType\":\"user_add_org\",\"UserId\":['172515221729079951'],\"TimeStamp\":\"1597371357080\"}";
        String timestamp="1597371357080";
        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                "ding1d769e1e02af8dfc35c2f4657eb6378f");
        Map<String, String> encryptedMap = dingTalkEncryptor.getEncryptedMap(plaintext, Long.valueOf(timestamp), nonce);
        log.info(""+encryptedMap);
        String decryptMsg = dingTalkEncryptor.getDecryptMsg(encryptedMap.get("msg_signature").toString(), encryptedMap.get("timeStamp").toString(), encryptedMap.get("nonce").toString(), encryptedMap.get("encrypt").toString());
        log.info("" + decryptMsg);
    }

    @Test
    public void testUser(){
        OapiUserGetResponse dingskqye5ljk4pogfbf = DingRequestUtil.getUser("dingskqye5ljk4pogfbf", "D4F0V5a8-HRUvXck3LDtP_AGw7UDQP_D_0k2l8kmttFOURFIYyrJkUcBGvvt_OwL", "011037513926803436702");
        log.info("user:{}",dingskqye5ljk4pogfbf);

    }

    @Test
    public void testGetToken(){
        Map<String, String> accessToken = DingRequestUtil.getProxyToken("dingd0vww6pgdoy16ozi", "28lTExiCrOCpDOfNGg-MaugItuNJWph99VZRFQsiYr6EMoZCJAfqLC-PHcfmDo53");
        System.out.println("EnterpriseControllerTest.testGetToken");
    }

    @Test
    public void testPattern(){
        String content = "**";

        String pattern = "^[^\\*]+$";

        boolean isMatch = Pattern.matches(pattern, content);
        System.out.println("字符串中是否包含了 'runoob' 子字符串? " + isMatch);
    }


    @Test
    public void testCall(){
        String appKey="ding2tvtlkfe9vxewk8w";
        Result<List<DingEnterprise>> enterprise = Result.newSuccess();
        if(StringUtils.isEmpty(appKey)){
            enterprise= enterpriseService.queryEnterpriseByCorpId("ding0266e33afd06b337f2c783f7214b6d69", null);
        }else {
            enterprise = enterpriseService.queryEnterpriseByAppKey(appKey);
        }
        log.info("enter");
    }

    @Test
    public void testPhone(){
        String phone="13432858633";
        String substring = phone.substring(phone.length() - 4, phone.length());
        log.info(substring);
    }

    @Test
    public void getUserByCode(){
        OapiSnsGetuserinfoBycodeResponse response = DingRequestUtil.getUserByCode("dingoanr35qskoipmnllyf",
                "ADkbD7KwkGeKWqqJZZQwfeuBQgFrcobbb875zC9lOY6PA9LO42b7oHITJrWVrHvg",
                "eca6aeb3dd6a36fc93dd4b0171d49b0f");
        log.info(response.getErrmsg());
    }
}
