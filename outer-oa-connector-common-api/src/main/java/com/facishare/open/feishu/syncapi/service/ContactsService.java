package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.model.ContactScopeModel;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;

import java.io.Serializable;
import java.util.List;

/**
 * 纷享通讯录服务
 *
 * <AUTHOR>
 * @date 20220802
 */
public interface ContactsService extends Serializable {
    /**
     * 初始化纷享通讯录
     *
     * @param fsEa
     * @return
     */
    Result<Void> initContactsAsync(String fsEa);

    /**
     * 获取CRM应用可见范围内的人员和部门信息
     * @return
     */
    Result<ContactScopeModel> getContactScopeData(String appId, String outEa);

    /**
     * 批量添加纷享员工
     *
     * @param appId
     * @param outEa
     * @param userList
     * @return
     */
    Result<Void> addUserList(String appId, String outEa, List<UserData.User> userList);

    /**
     * 批量停用纷享员工
     *
     * @param userList
     * @return
     */
    Result<Void> removeUserList(String appId, String outEa, List<UserData.User> userList);

    /**
     * 批量新增纷享部门
     *
     * @param appId
     * @param outEa
     * @param departmentList
     * @return
     */
    Result<Void> addDepList(String appId, String outEa, List<DepartmentData.Department> departmentList);

    /**
     * 批量停用纷享部门
     *
     * @param appId
     * @param outEa
     * @param departmentList
     * @return
     */
    Result<Void> removeDepList(String appId, String outEa, List<DepartmentData.Department> departmentList);

//    /**
//     * 重置管理员角色
//     * @return
//     */
//    Result<Void> resetAdminRole(String appId, String outEa);

    /**
     * 恢复员工
     * @param outEa
     * @param outUserId
     * @return
     */
    Result<Void> resumeEmployee(String outEa,String outUserId);

    /**
     * 停用员工
     * @param outEa
     * @param outUserId
     * @return
     */
    Result<Void> stopEmployee(String outEa,String outUserId);

    Result<Void> autoGetContactData();

    Result<ContactScopeModel> saveOrUpdateContactData(String appId, String outEa);

    /**
     * 刷新可见范围数据缓存
     * @param
     * @return
     */
    Result<Void> refreshContactScopeDataCacheAsync(String eventType, String appId, String outEa, String eventData);

    Result<Void> saveOrUpdateContactUser(String eventType, String appId, String outEa, UserData.User user, UserData.User oldUser, String eventData);

    Result<Void> deleteContactUser(String appId, String outEa, UserData.User user);

    Result<Void> deleteContactDepartment(String appId, String outEa, DepartmentData.Department department);

    Result<Void> saveOrUpdateContactDepartment(String eventType, String appId, String outEa, DepartmentData.Department department, DepartmentData.Department oldDepartment, String eventData);

    /**
     * 添加纷享员工
     *
     * @param appId
     * @param outEa
     * @param user
     * @return
     */
    Result<Void> addUser(String appId, String outEa, UserData.User user);
}
