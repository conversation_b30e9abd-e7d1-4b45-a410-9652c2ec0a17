package com.facishare.open.feishu.syncapi.result.whatsapp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WhatsappGetAllRegisterPhoneResult implements Serializable {
    private List<PhoneInfo> data;

    @Data
    public static class PhoneInfo implements Serializable {
        private String display_phone_number;
        private String quality_rating;
        private String verified_name;
        private String name_status;
        private String register_status;
        private String status;
        private String current_limit;
        private String code_verification_status;
        private String waba_id;
        private String waba_name;
    }
}
