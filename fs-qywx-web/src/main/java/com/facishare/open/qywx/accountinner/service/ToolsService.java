package com.facishare.open.qywx.accountinner.service;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountinner.model.FsAccountModel;
import com.facishare.open.qywx.accountsync.arg.BatchCreateQywxConnectorArg;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.facishare.open.qywx.accountsync.result.Result;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public interface ToolsService {
    Result<AppLicenseInfo> getAppLicenseInfo(String corpId,String appId);
    Result<List<String>> getUserListInAppVisibleRange(String corpId, String appId);
    @Deprecated
    Result<String> userId2OpenUserId(String corpId, String userId);
    Result<String> userId2OpenUserId2(String corpId, String appId, String userId);
    Result<String> userId2OpenUserId3(String corpId, String appId, String userId);
    Result<String> corpId2OpenCorpId(String corpId);

//    /**
//     * 刷新员工表，把out_account字段刷成密文，isv_out_account刷成明文（前提是原来表里面out_account字段存的是明文）
//     * @param corpId
//     * @return
//     */
//    Result<Integer> refreshEmployeeTable(String corpId);

    /**
//     * 刷新企业绑定表，out_ea字段刷成密文，isv_out_ea刷成密文（前提是原来表里面out_ea字段存的是明文）
//     * @param corpId
//     * @return
//     */
//    Result<Integer> refreshEnterpriseTable(String corpId);

//    /**
//     * 刷新企业应用绑定表，把corp_id字段刷成密文，isv_corp_id刷成密文（前提是原来表里面out_account字段存的是明文）
//     * @param corpId
//     * @return
//     */
//    Result<Integer> refreshApplicationTable(String corpId);

//    /**
//     * 迁移企业或者代开发应用的信息，明文转密文
//     * 更据agentId有没有值判断是迁移企业还是代开发应用，有值迁移的是代开发应用
//     * @param corpId
//     * @param agentId
//     * @return
//     */
//    Result refreshEnterpriseAccount(String corpId, String agentId);

//    /**
//     * 迁移企业或和代开发应用的信息，明文转密文
//     * @param corpIds
//     * @return
//     */
//    Result<Map<String, Object>> refreshAllEnterpriseAccount(List<String> corpIds);

    /**
     * 代开发应用或第三方应用查询迁移状态
     * @param corpId
     * @return
     */
    Result<Void> getEnterpriseAccountMigration(String corpId, String appId);

//    /**
//     * 刷新部门绑定表，把out_ea字段刷成密文（前提是原来表里面out_ea字段存的是明文）
//     * @param corpId
//     * @return
//     */
//    Result<Integer> refreshDepartmentTable(String corpId);
//
//    /**
//     * 刷新订单表，把paid_corpid字段刷成密文（前提是原来表里面paid_corpid字段存的是明文）
//     * @param corpId
//     * @return
//     */
//    Result<Integer> refreshOrderTable(String corpId);
//
//    /**
//     * 刷新应用信息表
//     * @param corpId
//     * @return
//     */
//    Result<Integer> refreshApplicationInfoTable(String corpId);

//    /**
//     * 删除外部联系人缓存表
//     * @param corpId
//     * @return
//     */
//    Result<Integer> deleteExternalContactTable(String corpId);

//    /**
//     * 删除用户缓存表
//     * @param corpId
//     * @return
//     */
//    Result<Integer> deleteUserTable(String corpId);

//    /**
//     * 更新服务商的员工绑定表
//     * @param
//     * @return
//     */
//    Result<List<QyweixinAccountEmployeeMapping>> updateServiceProviderEmployeeBind(String fsEa, String appId, String outEa);

    /**
     * 获取指定级别的已绑定的客户信息
     * @param bindType
     * @param accountLevelList
     * @return
     */
    Result<List<FsAccountModel>> getAccountInfo(int bindType,List<String> accountLevelList);

    /**
     * 查询指定的纷享客户信息
     * @param fsEaList
     * @return
     */
    List<FsAccountModel> queryAccountInfoList(List<String> fsEaList);

    /**
     * 查询应用信息
     * @param corpId
     * @param appId
     * @return
     */
    Result<QyweixinAppStatusInfo> getQYWXAppBindInfo(String corpId, String appId);

    /**
     * 查询应用授权情况
     * @param corpId
     * @return
     */
    Result<QyweixinAppAuthorityInfo> getQYWXRepAppAuthorityInfo(String corpId);

    /**
     * 查看crm客户是否已开通
     */
    Result<AccountObjInfo> queryCrmAccountObjStatus(String corpId);

    /**
     * 查看下crm订单是否成功
     */
    Result<SalesOrderObjInfo> queryCrmSalesOrderObjStatus(String orderId);

    /**
     * 解绑，目前只支持解绑企微和crm
     */
    Result<Integer> getEnterpriseRunStatus(String ea);

    Result<Void> unbind(String corpId, String fsEa, String appId);

//    Result<Void> updateOpenIds(List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos);

//    Result<Void> updateAllCorpBind();
//
//    Result<Void> updateAllCorpBindToCopy(Integer copyDirection);

    Result<Long> deleteUserInfo(String outEa, Boolean isDelete);

    Result<String> queryFsEnterpriseOpen(String outEa);

    Result<String> queryFsEmployeeOpen(String outEa, String outUserId);

    Result<String> queryEnterpriseBindType(String fsEa, String outEa);

    Result<String> queryFsEmployeeStatus(String outEa, String outUserId);
//
//    Result<Void> dealRepeatEmployees();
//
//    Result<Void> queryRepeatEmployees();

//    Result<Void> stopEmployee(Integer ei, String userId,String outEa);

//    Result<Void> dealEmpData(Integer ei, String userId, String newUserId,String outEa);

//    Result<Integer> updateDeptBindStatus(String fsEa, String fsDeptId, Integer status, String appId);

//    Result<Void> pushCorpBindData2Cloud(String domain);

//    Result<Void> pushEnterpriseData2Cloud(String fsEa, String domain);

//    Result<Integer> updateEnterpriseDomain(String fsEa, String domain);

//    Result<Void> pushCorpInfoData2Cloud(String domain);
    Result<String> batchCreateQywxConnector(BatchCreateQywxConnectorArg arg);

    Result<Void> migrateOrderInfo(List<String> outEaList);

    Result<Void> migrateBusinessInfoBind(List<String> outEaList);

    Result<Void> migrateQyweixinIdToOpenid(List<String> outEaList);

    Result<Void> migrateFileInfo(List<String> outEaList);

    Result<Void> migrateExternalContacts(List<String> outEaList);

    Result<Void> migrateAppBind(List<String> outEaList);

    /**
     * 迁移员工绑定信息
     * @param outEaList 企业ID列表
     * @return 结果
     */
    Result<Void> migrateEmpBind(List<String> outEaList);

    Result<Void> migrateDeptBind(List<String> outEaList);


    Result<Void> migrateConfigInfo(List<String> outEaList);

    Result<Void> migrateEnterpriseBind(List<String> outEaList, Timestamp startTime, Timestamp endTime, String finalAppId);

    Result<Integer> deleteQYWXAccountBind(String outEa, String fsEa);

    Result<Void> migrateOnlyEnterpriseBind(List<String> outEaList);

    Result<Void> cleanNewEnterpriseBind(String outEa, String appId, String fsEa);

    Result<Void> fixEnterpriseManualBindInfo();

    Result<Map<String, String>> migrateEnterpriseBind(String outEa);
}